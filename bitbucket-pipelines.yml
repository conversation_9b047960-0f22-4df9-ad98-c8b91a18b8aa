definitions:
  steps:
    - step: &unit_test
        name: Unit Test
        image: public.ecr.aws/j9v6w7g7/unit-test-image:latest
        caches:
          - node
        script:
          - unit_test
    - step: &sast
        name: SAST
        image: public.ecr.aws/j9v6w7g7/sast-image:latest
        size: 2x
        services:
          - docker
        caches:
          - docker
        script:
          - sast_analyzer
    - step: &dependency_scanning
        name: Dependency Scanning
        image: public.ecr.aws/j9v6w7g7/dependency-scanning-image:latest
        script:
          - dependency_scanning
    - step: &code_quality
        name: Code Quality Analysis
        image: public.ecr.aws/j9v6w7g7/code-quality-image:latest
        script:
          - code_quality
    - step: &prepare
        name: prepare
        image: alpine
        artifacts:
          - .env.local
    - step: &build
        name: build
        image: public.ecr.aws/j9v6w7g7/auto-build-image:stable
        services:
          - docker
        caches:
          - docker
        script:
          - env > .env.local
          - auto_build aws_configure
          - auto_build docker_login
          - cat .env.local
          - auto_build
    - step: &deploy
        name: deploy
        image: public.ecr.aws/j9v6w7g7/auto-deploy-image:stable
        services:
          - docker
        script:
          - auto_deploy kubernetes
  services:
    docker:
      memory: 3072

pipelines:
  pull-requests:
    '**':
      - step:
          <<: *unit_test
  branches:
    develop:
      - parallel:
        - step:
            <<: *dependency_scanning
        - step:
            <<: *unit_test
        - step:
            <<: *sast
        - step:
            <<: *code_quality
      - step:
          <<: *build
          deployment: BuildDevelopment
      - step:
          <<: *deploy
          deployment: Development
          image: public.ecr.aws/j9v6w7g7/auto-deploy-image:latest
    uat:
      - step:
          <<: *build
          deployment: BuildUAT
      - step:
          <<: *deploy
          deployment: UAT
    master:
      - step:
          <<: *build
          deployment: BuildProduction
      - step:
          <<: *deploy
          deployment: Production