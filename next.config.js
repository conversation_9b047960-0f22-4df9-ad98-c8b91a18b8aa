/* eslint-disable global-require */
/* eslint-disable import/no-extraneous-dependencies */
let withBundleAnalyzer = (conf) => conf; // Doing nothing

if (process.env.NODE_ENV !== 'production' && process.env.ANALYZE === true) {
  withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: process.env.ANALYZE === 'true',
  });
}

const nextConfig = {
  output: 'standalone',
  eslint: {
    dirs: ['.'],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  swcMinify: true,
  poweredByHeader: false,
  reactStrictMode: true,
  pageExtensions: ['page.tsx', 'page.ts'],
  experimental: {
    scrollRestoration: true,
  },
  modularizeImports: {
    lodash: {
      transform: 'lodash/{{member}}',
    },
    hooks: {
      transform: 'hooks/{{member}}',
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
      },
    ],
    minimumCacheTTL: 86400,
  },
  async rewrites() {
    return [
      {
        source: '/health',
        destination: '/api',
      },
    ];
  },
  env: {
    API_SERVER_BASE_URL: process.env.API_SERVER_BASE_URL,
    DEPLOY_ENV: process.env.DEPLOY_ENV,
    COOKIE_SECURE_PASSWORD: process.env.COOKIE_SECURE_PASSWORD,
    PUBLIC_DOMAIN: process.env.PUBLIC_DOMAIN,
    FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
    FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
    FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
    FIREBASE_SENDER_ID: process.env.FIREBASE_SENDER_ID,
    FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
    FIREBASE_MEASUREMENT_ID: process.env.FIREBASE_MEASUREMENT_ID,
  },
  webpack(config) {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.('.svg')
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: /\.[jt]sx?$/,
        resourceQuery: { not: /url/ }, // exclude if *.svg?url
        use: [
          {
            loader: '@svgr/webpack',
            options: {
              prettier: false,
              svgo: true,
              svgoConfig: {
                plugins: [
                  {
                    name: 'preset-default',
                    params: {
                      overrides: { removeViewBox: false },
                    },
                  },
                ],
              },
              titleProp: true,
            },
          },
        ],
      }
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

module.exports = withBundleAnalyzer(nextConfig);
