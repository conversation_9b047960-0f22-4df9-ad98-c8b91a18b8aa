# Hogugu Booking Web

Everthing about Hogugu Booking Web - Allow customer booking from web

## Requirement

- This project requires Firebase, Twilio.
- Node.js 14+ and npm (Recommended version 18)

### Getting started

Clone the repo and install all dependencies

```shell
yarn
```

Make a copy of `.env.example` rename it to `.env`
Then, you can run locally in development mode with live reload:

```shell
yarn run dev
```

Open http://localhost:3000 with your favorite browser to see your project.

```shell
├── README.md                       # README file
├── .husky                          # Husky configuration
├── .vscode                         # VSCode configuration
├── public                          # Public assets folder
├── src
│   ├── components                  # Components group by role, feature, etc...
│   ├── pages                       # Next JS Pages
│   ├── theme                       # Mantine theme
│   └── models                      # Query data fetching
│   └── utils                       # Utility functions
└── tsconfig.json                   # TypeScript configuration
```

