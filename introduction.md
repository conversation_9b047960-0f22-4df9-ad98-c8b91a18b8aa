# Teqnowa Web Platform Overview

## Project Structure

This is a Next.js application built for a platform called Teqnowa, which appears to be a matching platform specialized for plant construction projects. The platform connects companies with skilled individual contractors and other businesses in the plant construction industry.

### Key Technologies

- **Next.js 13.4.7** - React framework for server-rendered applications
- **TypeScript** - For type safety and better developer experience
- **Mantine UI (v6)** - Component library for building the UI
- **React Query** - For data fetching, caching, and state management
- **Zustand** - For global state management
- **Firebase** - For authentication and possibly other services
- **Iron Session** - For secure, encrypted, and stateless sessions
- **React Hook Form** - For form handling and validation
- **Yup** - For schema validation
- **Axios** - For HTTP requests

### Architecture

The project follows a standard Next.js file-based routing approach with some customizations:

1. **Custom Page Extensions**: Uses `.page.tsx` and `.page.ts` extensions for pages
2. **API Routes**: Implements API routes under `src/pages/api/`
3. **Component Organization**: Well-structured component hierarchy with layout components, form components, and feature-specific components
4. **Data Fetching**: Uses React Query for data fetching and state management
5. **Authentication**: Implements authentication using Firebase and custom auth provider

### Key Features

The application appears to provide the following features:

1. **User Authentication**: Login, registration, password management
2. **User Profiles**: Worker profiles with skills, certifications, and documents
3. **Job Listings**: Viewing and managing job postings
4. **Messaging**: Communication between companies and workers
5. **Matching System**: Connecting companies with suitable workers

### Code Organization

- **pages/**: Next.js pages for routing
- **components/**: Reusable UI components
- **hooks/**: Custom React hooks for shared logic
- **models/**: Data models and types
- **utils/**: Utility functions and constants
- **theme/**: Styling and theming configuration
- **lib/**: Core functionality and services

### Design Patterns

1. **Provider Pattern**: Used for auth, Firebase, and other global context needs
2. **Custom Hooks**: Encapsulates logic for data fetching, form handling, etc.
3. **Component Composition**: UI built through composable components
4. **Higher-Order Components**: For layout and shared functionality

### Localization

The application appears to be primarily in Japanese, with Japanese text in SEO metadata and date localization settings.

### Deployment and Infrastructure

The application is containerized with Docker and includes CI/CD pipeline configurations for BitBucket pipelines. 