server {
    listen       5000;
    server_name  localhost;

    location / {
        #auth_basic "Restricted";
        #auth_basic_user_file /etc/nginx/.htpasswd;
        resolver ******* valid=60s;
        proxy_buffering        on;
        proxy_cache_valid      200  10m;
        proxy_cache_use_stale  error timeout invalid_header updating
                               http_500 http_502 http_503 http_504;

        client_max_body_size       ${NGINX_VAR_CLIENT_MAX_BODY_SIZE};       # 10m
        client_body_buffer_size    ${NGINX_VAR_CLIENT_BODY_BUFFER_SIZE};    # 128k

        client_body_temp_path      ${NGINX_VAR_CLIENT_BODY_TEMP_PATH};

        proxy_connect_timeout      ${NGINX_VAR_PROXY_CONNECT_TIMEOUT};      # 90
        proxy_send_timeout         ${NGINX_VAR_PROXY_SEND_TIMEOUT};         # 90
        proxy_read_timeout         ${NGINX_VAR_PROXY_READ_TIMEOUT};         # 90

        proxy_buffer_size          ${NGINX_VAR_PROXY_BUFFER_SIZE};          # 4k
        proxy_buffers              ${NGINX_VAR_PROXY_BUFFERS};              # 4 32k
        proxy_busy_buffers_size    ${NGINX_VAR_PROXY_BUSY_BUFFERS_SIZE};    # 64k
        proxy_temp_file_write_size ${NGINX_VAR_PROXY_TEMP_FILE_WRITE_SIZE}; # 64k

        proxy_temp_path            ${NGINX_VAR_PROXY_TEMP_PATH};
        
        proxy_pass          http://localhost:5050;
    }

    location /sitemap.xml.gz {
        resolver               ******* valid=60s;
        proxy_pass             ${NGINX_VAR_CMS_BASE_URL}/sitemap.xml.gz;
        proxy_buffering        on;
        proxy_cache_valid      200  10m;
        proxy_cache_use_stale  error timeout invalid_header updating
                               http_500 http_502 http_503 http_504;
    }

    location /health {
        auth_basic off;
        proxy_pass          http://localhost:5050;
    }

    location /api/ {
        auth_basic off;
        proxy_pass          http://localhost:5050;
    }

    client_header_timeout 60;
    client_body_timeout   60;
    keepalive_timeout     60;
}