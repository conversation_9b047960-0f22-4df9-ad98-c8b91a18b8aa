{"name": "teqnowa-web", "version": "1.0.0", "scripts": {"dev": "rm -rf .next && next dev", "dev-win": "rmdir /s /q .next ?nul 2>&1 & next dev", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "export": "next export", "build-prod": "run-s clean build export", "clean": "rimraf .next out", "lint": "next lint --fix", "check-types": "tsc --noEmit --pretty", "prepare": "husky install"}, "dependencies": {"@cyntler/react-doc-viewer": "1.12.0", "@emotion/react": "^11.11.1", "@emotion/server": "^11.11.0", "@fontsource/noto-sans-jp": "^5.0.3", "@hookform/resolvers": "3.1.0", "@mantine/carousel": "^6.0.15", "@mantine/core": "^6.0.15", "@mantine/dates": "^6.0.15", "@mantine/hooks": "^6.0.15", "@mantine/modals": "^6.0.15", "@mantine/next": "^6.0.15", "@mantine/notifications": "^6.0.15", "@mantine/nprogress": "^6.0.15", "@svgr/webpack": "^8.0.1", "@tanstack/react-query": "^4.29.18", "@tanstack/react-query-devtools": "^4.29.18", "@total-typescript/ts-reset": "^0.4.2", "@types/lodash": "^4.14.195", "@types/node": "^20.3.2", "@types/react": "^18.2.14", "@types/react-scroll": "^1.8.7", "axios": "^1.4.0", "broadcast-channel": "^5.3.0", "cookies-next": "^2.1.2", "dayjs": "^1.11.8", "embla-carousel-react": "^7.1.0", "firebase": "^10.1.0", "framer-motion": "^10.12.17", "iron-session": "^6.3.1", "linkify-react": "^4.1.1", "linkifyjs": "^4.1.1", "lodash": "^4.17.21", "next": "13.5.9", "next-seo": "^6.1.0", "react": "^18.2.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-hook-form": "7.44.3", "react-image-crop": "^10.1.5", "react-number-format": "^5.2.2", "react-player": "^2.16.0", "sharp": "^0.32.1", "typescript": "^5.1.3", "yup": "^1.2.0", "zustand": "^4.3.8"}, "devDependencies": {"@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@next/bundle-analyzer": "^13.4.7", "@svgr/cli": "^8.0.1", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "eslint": "^8.43.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-next": "^13.4.7", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "prettier": "^2.8.8"}}