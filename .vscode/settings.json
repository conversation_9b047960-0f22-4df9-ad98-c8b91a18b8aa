{
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "search.exclude": {
    "package-lock.json": true
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.formatOnSave": false,
  "editor.codeActionsOnSave": [
    "source.addMissingImports",
    "source.fixAll.eslint"
  ],
  // Multiple language settings for json and jsonc files
  "[json][jsonc]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "typescript.tsdk": "node_modules/typescript/lib"
}
