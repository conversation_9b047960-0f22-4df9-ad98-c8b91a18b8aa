import { useQuery } from '@tanstack/react-query';
import type { Unsubscribe } from 'firebase/firestore';
import { chatService } from 'models/chat/chatService';
import { useEffect } from 'react';
import queryClient from 'utils/queryClient';

import useUser from './useUser';

const useFirebaseUser = (withListener = true) => {
  const { data: currentUser } = useUser();
  const firebaseUserId = currentUser?.firebaseUserId;

  const cacheUserData = queryClient.getQueryData([
    'users',
    'firebase-user-data',
    firebaseUserId,
  ]);

  const { data } = useQuery(
    ['users', 'firebase-user-data', firebaseUserId],
    chatService.getCurrentUserData,
    {
      refetchOnReconnect: false,
      enabled: !!firebaseUserId && !cacheUserData,
      staleTime: Infinity,
      meta: {
        noToastError: true,
      },
    },
  );

  const cacheUserSnapshot = queryClient.getQueryData<Unsubscribe>([
    'firebase-user-snapshot',
    firebaseUserId,
  ]);

  const { data: unsubscribe, remove } = useQuery({
    queryKey: ['firebase-user-snapshot', firebaseUserId],
    enabled: !!firebaseUserId && !cacheUserSnapshot && withListener,
    refetchOnReconnect: false,
    queryFn: (payload) =>
      chatService.attachUserDataListener({
        ...payload,
      }),
  });

  useEffect(() => {
    if (!firebaseUserId || !withListener) {
      return () => {};
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
        remove();
      }
    };
  }, [firebaseUserId, remove, unsubscribe, withListener]);

  return { data };
};

export default useFirebaseUser;
