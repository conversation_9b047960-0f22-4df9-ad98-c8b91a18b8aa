import type { UseMutationOptions } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import api from 'utils/api';
import helpers from 'utils/helpers';
import type { ExtendOptions } from 'utils/type';

interface Options<TVariables, TData, TError>
  extends Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'>,
    ExtendOptions {
  apiUrl: string | ((params: TVariables) => string);
}

const useMutate = <TVariables = unknown, TData = unknown, TError = unknown>(
  options: Options<TVariables, TData, TError>,
) => {
  const {
    apiUrl,
    defaultToast,
    method = 'post',
    successMessage,
    axiosConfig,
    ...otherOptions
  } = options;

  return useMutation({
    mutationFn: async (params: TVariables) => {
      const url = typeof apiUrl === 'string' ? apiUrl : apiUrl(params);
      const config = {
        method,
        url,
        data: params,
        ...axiosConfig,
      };
      const { data } = await api<TData>(config);
      return data;
    },
    onSuccess: () => {
      if (defaultToast || successMessage) {
        helpers.toast({
          message: successMessage || '完了しました。',
        });
      }
    },
    ...otherOptions,
  });
};

export default useMutate;
