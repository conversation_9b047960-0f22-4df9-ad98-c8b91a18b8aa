import type { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import api from 'utils/api';
import type { ExtendOptions } from 'utils/type';

interface Options<
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
> extends Omit<
      UseQueryOptions<TQueryFnData, TError, TData, QueryKey>,
      'queryFn' | 'queryKey'
    >,
    ExtendOptions {
  apiUrl: string;
}

const useFetch = <
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
>(
  options: Options<TQueryFnData, TData, TError>,
) => {
  const {
    queryKey,
    apiUrl,
    customParams,
    axiosConfig,
    method = 'get',
    ...otherOptions
  } = options;

  const fetchData = async () => {
    const config = {
      method,
      url: apiUrl,
      data: customParams,
      ...axiosConfig,
    };
    const { data } = await api<TQueryFnData>(config);
    return data;
  };

  return useQuery({
    queryKey,
    queryFn: fetchData,
    ...otherOptions,
  });
};

export default useFetch;
