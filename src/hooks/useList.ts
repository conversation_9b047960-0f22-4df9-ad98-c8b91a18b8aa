import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import api from 'utils/api';
import type { ExtendOptions, IListQuery, IListResult } from 'utils/type';

import useSearch from './useSearch';

interface Options<
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
> extends Omit<
      UseQueryOptions<
        IListResult<TQueryFnData>,
        TError,
        IListResult<TData>,
        unknown[]
      >,
      'queryFn' | 'queryKey'
    >,
    ExtendOptions {
  apiUrl: string;
}

const useList = <
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
>(
  options: Options<TQueryFnData, TData, TError>,
) => {
  const {
    queryKey,
    apiUrl,
    customParams,
    transform,
    method = 'get',
    axiosConfig,
    ...otherOptions
  } = options;
  const { query: searchParams } = useSearch();
  const params = {
    ...searchParams,
    page: searchParams.page ? Number(searchParams.page) : 1,
    limit: searchParams.limit ? Number(searchParams.limit) : 10,
    ...customParams, // Do not change the order. Please pass another param if you want to override the params
  };

  const formatParams = (_params: IListQuery<unknown>) => {
    const formattedParams = { ..._params };
    // if (formattedParams.orderBy && formattedParams.order) {
    //   formattedParams.sort = getSortString(
    //     formattedParams.orderBy,
    //     formattedParams.order,
    //   );
    //   delete formattedParams.order;
    //   delete formattedParams.orderBy;
    // }
    if (transform) {
      return transform(formattedParams);
    }
    return formattedParams;
  };

  const formattedParams = formatParams(params);
  const { data, isFetching, refetch, isLoading, isSuccess } = useQuery({
    queryKey: queryKey
      ? [...queryKey, formattedParams]
      : [apiUrl, formattedParams],
    queryFn: async () => {
      const config = {
        method,
        url: apiUrl,
        data: formattedParams,
        ...axiosConfig,
      };

      const { data: result } = await api<IListResult<TQueryFnData>>(config);
      return result;
    },
    keepPreviousData: true,
    ...otherOptions,
  });

  return {
    list: data?.docs || [],
    total: data?.totalDocs || 0,
    lastPage: data?.totalPages || 0,
    page: data?.page || 1,
    perPage: data?.limit || 10,
    isLoading,
    isFetching,
    isSuccess,
    refetch,
  };
};
export default useList;
