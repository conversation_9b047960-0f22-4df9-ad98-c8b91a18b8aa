import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { useMemo, useState } from 'react';
import api from 'utils/api';
import type { ExtendOptions, IListQuery, IListResult } from 'utils/type';

export const formatQuery = <TObjectSchema>(
  query: IListQuery<TObjectSchema>,
) => {
  const formattedQuery = { ...query };
  (Object.keys(formattedQuery) as Array<keyof typeof formattedQuery>).forEach(
    (key) => {
      const property = formattedQuery[key];
      if (
        property === undefined ||
        property === null ||
        property === '' ||
        (Array.isArray(property) && property.length === 0)
      ) {
        delete formattedQuery[key];
      }
    },
  );
  return formattedQuery;
};
interface Options<
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
> extends Omit<
      UseQueryOptions<
        IListResult<TQueryFnData>,
        TError,
        IListResult<TData>,
        unknown[]
      >,
      'queryFn' | 'queryKey'
    >,
    ExtendOptions {
  apiUrl: string;
}
const useControlList = <
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
  TObjectSchema = Record<string, unknown>,
>(
  options: Options<TQueryFnData, TData, TError>,
) => {
  const {
    queryKey,
    apiUrl,
    method = 'get',
    customParams,
    axiosConfig,
    ...otherOptions
  } = options;
  const [listQuery, setListQuery] = useState<IListQuery<TObjectSchema>>({
    page: 1,
    limit: 10,
    ...customParams,
  });
  // const onTableChange = (
  //   params: TablePaginationConfig,
  //   filter: Record<string, FilterValue | null>,
  //   sorter: SorterResult<TQueryFnData> | SorterResult<TQueryFnData>[],
  // ) => {
  //   // TODO: Handle multisort
  //   if (!Array.isArray(sorter)) {
  //     setListQuery((prevListQuery) => ({
  //       ...prevListQuery,
  //       ...filter,
  //       page: params.current || 1,
  //       limit: params.pageSize || 10,
  //       order: sorter.order,
  //       // Column that has sorter should have key prop
  //       orderBy: sorter.order ? sorter.columnKey?.toString() : undefined,
  //     }));
  //   }
  // };
  const onFilter = (values: TObjectSchema | Record<string, unknown>) => {
    setListQuery((prevListQuery) => ({
      ...prevListQuery,
      ...values,
      page: 1,
    }));
  };
  const formattedParams = useMemo(() => formatQuery(listQuery), [listQuery]);
  const { data, isFetching, refetch, isLoading } = useQuery({
    queryKey: queryKey
      ? [...queryKey, formattedParams]
      : [apiUrl, formattedParams],
    queryFn: async () => {
      const { data: result }: { data: IListResult<TQueryFnData> } = await api({
        method,
        params: formattedParams,
        url: apiUrl,
        ...axiosConfig,
      });
      return result;
    },
    keepPreviousData: true,
    ...otherOptions,
  });

  return {
    list: data?.docs || [],
    total: data?.totalDocs || 0,
    totalPages: data?.totalPages || 0,
    page: data?.page || 1,
    perPage: data?.limit || 10,
    isLoading,
    isFetching,
    listQuery,
    refetch,
    onFilter,
    setListQuery,
  };
};
export default useControlList;
