import type { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import useFetch from 'hooks/useFetch';
import authQuery from 'models/auth';
import type { IWorker } from 'models/auth/type';
import { useRouter } from 'next/router';
import helpers from 'utils/helpers';

import useGlobalState from './useGlobalState';

export interface Options<TQueryFnData = unknown, TData = TQueryFnData>
  extends Omit<
    UseQueryOptions<TQueryFnData, unknown, TData, QueryKey>,
    'queryFn' | 'queryKey'
  > {
  customParams?: Record<string, unknown>;
}

const useUser = <T extends IWorker>(options?: Options<T>) => {
  const { push, pathname, asPath } = useRouter();
  const { enabled = true, ...otherOptions } = options || {};
  const webCookie = helpers.getWebCookie();
  const { setReferrerCompleteProfile } = useGlobalState();

  return useFetch<T>({
    ...authQuery.getCurrentUser,
    enabled: enabled && !!webCookie?.accessToken,
    staleTime: Infinity,
    onSuccess: (data) => {
      if (
        data.isCompletedProfile === false &&
        pathname !== '/register/complete'
      ) {
        setReferrerCompleteProfile(asPath);
        push('/register/complete');
      }
    },
    ...otherOptions,
  });
};

export default useUser;
