import type { BroadcastChannel } from 'broadcast-channel';
import { type RefObject } from 'react';
import { create } from 'zustand';

export interface GlobalState {
  openedDrawer: boolean;
  setOpenDrawer: (payload: boolean) => void;
  referrerCompleteProfile: string;
  setReferrerCompleteProfile: (payload: string) => void;
  authChannel: BroadcastChannel | undefined;
  setAuthChannel: (payload: BroadcastChannel) => void;
  showChatTemplate: boolean;
  setShowChatTemplate: (payload: boolean) => void;
  chatUploadRef: RefObject<HTMLInputElement> | null;
  setChatUploadRef: (payload: RefObject<HTMLInputElement> | null) => void;
}
const useGlobalState = create<GlobalState>((set) => ({
  openedDrawer: false,
  setOpenDrawer: (payload) => set({ openedDrawer: payload }),
  referrerCompleteProfile: '/',
  setReferrerCompleteProfile: (payload) =>
    set({ referrerCompleteProfile: payload }),
  authChannel: undefined,
  setAuthChannel: (payload) => set({ authChannel: payload }),
  showChatTemplate: false,
  setShowChatTemplate: (payload) => set({ showChatTemplate: payload }),
  chatUploadRef: null,
  setChatUploadRef: (payload) => set({ chatUploadRef: payload }),
}));

export default useGlobalState;
