import { Text } from '@mantine/core';
import authQuery from 'models/auth';
import { useRouter } from 'next/router';
import api from 'utils/api';
import helpers from 'utils/helpers';
import queryClient from 'utils/queryClient';

import useGlobalState from './useGlobalState';
import useMutate from './useMutate';

const useLogout = (props?: { redirect?: string }) => {
  const { mutateAsync: handleLogout } = useMutate({
    ...authQuery.logout,
    onSuccess: () => {
      helpers.logEventTracking('logout');
      helpers.setEventTrackingUserId(undefined);
    },
  });
  const { replace, reload } = useRouter();
  const { authChannel } = useGlobalState();

  const logout = async () => {
    try {
      const webCookie = helpers.getWebCookie();
      if (!webCookie?.refreshToken) {
        helpers.toast({
          type: 'error',
          message: 'ページデータが取得できません。',
        });
        return;
      }
      await handleLogout({});
    } finally {
      setTimeout(() => helpers.removeWebCookie());
      delete api.defaults.headers.common.Authorization;
      queryClient
        .getQueryCache()
        .findAll(['currentUser'])
        .forEach((query) => query.reset());
      setTimeout(() => replace(props?.redirect || '/login').then(reload));
    }
  };

  return {
    logout,
    confirmLogout: () => {
      helpers.confirm({
        title: 'ログアウト',
        children: (
          <Text color="black" fz="16px" lh="24px" p={24}>
            本当にログアウトしますか？
          </Text>
        ),
        onConfirm: () => {
          if (authChannel) {
            authChannel.postMessage('logout');
          }
          logout();
        },
      });
    },
  };
};

export default useLogout;
