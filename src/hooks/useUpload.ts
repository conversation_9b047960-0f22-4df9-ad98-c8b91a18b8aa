import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import type { IFile } from 'models/auth/type';
import resourceQuery from 'models/resource';
import { useRef, useState } from 'react';
import helpers from 'utils/helpers';

import useMutate from './useMutate';

export interface UploadOptions {
  validTypes?: string[];
  maxSize?: number;
  enableOCRValidation?: boolean; // Enable OCR-specific validation
  onUploadSuccess?: (data: IFile[]) => void;
  onUploadProgress?: (
    percent: number,
    fileIndex: number,
    total: number,
  ) => void;
}

const useUpload = (options?: UploadOptions) => {
  const uploadRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { validTypes, maxSize, onUploadSuccess } = options || {};
  const { mutateAsync: signUrl } = useMutate<
    {
      signedUrls: {
        fileName: string;
        contentType: string;
        isPublic: boolean;
      }[];
    },
    { signedUrls: IFile[] }
  >({
    ...resourceQuery.signUrl,
    meta: {
      noToastError: true,
    },
  });

  const { mutateAsync: uploadToS3 } = useMutation<
    unknown,
    unknown,
    {
      url: string;
      file: File;
    }
  >({
    mutationKey: ['currentUser', 'uploadingToS3'],
    mutationFn: (params) => {
      return axios.put(params.url, params.file, {
        headers: {
          'Content-Type': params.file.type,
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percent = (progressEvent.loaded / progressEvent.total) * 100;
            options?.onUploadProgress?.(percent, 0, 1); // Adjust for single file upload
          }
        },
      });
    },
    meta: {
      noToastError: true,
    },
  });

  const getValidFileTypeText = () => {
    let validTypeText = '';
    validTypes?.forEach((type, index) => {
      const typeSplit = type.split('.');
      if (typeSplit[1]) {
        validTypeText += `${typeSplit[1].toUpperCase()}${
          index < validTypes.length - 1 ? '、' : ''
        } `;
      }
    });
    return validTypeText;
  };

  const validateFile = async (file: File): Promise<boolean> => {
    const validTypeText = getValidFileTypeText();
    if (validTypes) {
      const isValidFile = validTypes.find((type) => file.name.includes(type));

      if (!isValidFile) {
        helpers.toast({
          type: 'error',
          message: `指定できないファイル形式です。画像は${validTypeText}形式でアップロードしてください。`,
        });
        return false;
      }
    }

    // OCR-specific validation
    if (options?.enableOCRValidation) {
      const ocrValidation = await helpers.validateOCRFile(file);
      if (!ocrValidation.isValid) {
        helpers.toast({
          type: 'error',
          message: ocrValidation.errorMessage || 'ファイルの検証に失敗しました',
        });
        return false;
      }
    } else {
      // Standard file size validation (when OCR validation is not enabled)
      const isValidSize = file.size / 1024 / 1024 <= (maxSize || 0);
      if (maxSize && !isValidSize) {
        helpers.toast({
          type: 'error',
          message: `画像サイズは${maxSize}MB以内にしてください`,
        });
        return false;
      }
    }

    return true;
  };

  const uploadFile = async ({ files }: { files: File[] }) => {
    setIsUploading(true);
    try {
      const { signedUrls } = await signUrl({
        signedUrls: files.map((file) => ({
          fileName: file.name,
          contentType: file.type,
          isPublic: true,
        })),
      });

      const signedUrlsMap = signedUrls.map(async (data, index) => {
        await uploadToS3({ url: data.url, file: files[index]! });
        return data;
      });

      const results = await Promise.all(signedUrlsMap);

      if (!onUploadSuccess) {
        return;
      }

      const successData = results.map((d) => ({
        ...d,
        file: files.find((f) => f.name === d.originalName),
      }));

      onUploadSuccess(successData);
    } catch (error) {
      helpers.toast({
        type: 'error',
        message: 'ページデータが取得できません。',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return { validateFile, uploadFile, isUploading, uploadRef };
};

export default useUpload;
