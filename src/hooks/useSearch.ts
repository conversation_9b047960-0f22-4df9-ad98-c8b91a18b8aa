import _isEmpty from 'lodash/isEmpty';
import { usePathname, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/router';
import { useCallback } from 'react';

interface NavigationOptions {
  replace?: boolean;
  skipEmptyString?: boolean;
}
export default function useSearch() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams()!;

  // Get a new searchParams string by merging the current
  // searchParams with a provided key/value pair
  const setQuery = useCallback(
    (
      queryObj: object,
      { replace = false, skipEmptyString = false }: NavigationOptions = {},
    ) => {
      const params = new URLSearchParams(Array.from(searchParams.entries()));
      Object.entries(queryObj).forEach(([key, value]) => {
        if (_isEmpty(value) && skipEmptyString) {
          params.delete(key);
        } else {
          params.set(key, value);
        }
      });
      if (replace) {
        router.replace(`${pathname}?${params.toString()}`);
      } else {
        router.push(`${pathname}?${params.toString()}`);
      }
    },
    [pathname, router, searchParams],
  );
  return { query: Object.fromEntries(searchParams.entries()), setQuery };
}
