import type { QueryModel } from 'utils/type';

const resourceQuery = {
  prefectures: {
    queryKey: ['prefecture-list'],
    apiUrl: '/api/prefectures',
    axiosConfig: {
      baseURL: 'https://station.csp.scrum-dev.com',
    },
    staleTime: Infinity,
  },
  signUrl: {
    apiUrl: '/media/common/signed-urls',
    method: 'post',
  },
  sendEmail: {
    apiUrl: '/contact/send-mail',
    method: 'post',
    successMessage: '完了しました。',
  },
} satisfies QueryModel;

export default resourceQuery;
