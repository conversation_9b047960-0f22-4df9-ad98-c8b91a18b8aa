import type {
  InfiniteData,
  MutationFunction,
  QueryFunction,
  QueryKey,
} from '@tanstack/react-query';
import type {
  FirestoreDataConverter,
  QueryDocumentSnapshot,
  Timestamp,
  Unsubscribe,
} from 'firebase/firestore';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  increment,
  limit,
  onSnapshot,
  orderBy,
  query,
  runTransaction,
  serverTimestamp,
  updateDoc,
  where,
  writeBatch,
} from 'firebase/firestore';
import { db } from 'utils/firebase';
import queryClient from 'utils/queryClient';

import type {
  IMessage,
  IMessageAttachment,
  IRoom,
  IUser,
  MetaAction,
} from './type';

export interface ChatMessage {
  text: string;
  username: string;
  roomId: string;
  createdAt?: Date;
  id?: string;
}

const PER_PAGE = 20;

let cacheOperationLocks: Promise<void> | null = null;

interface DatabaseType {
  createdAt: Timestamp;
  lastMessage?: Omit<IMessage, 'createdAt'> & {
    createdAt: Timestamp;
  };
}
interface LocalType {
  createdAt: string;
  lastMessage?: IMessage;
}
const genericConverter = <T>(): FirestoreDataConverter<T & LocalType> => ({
  toFirestore: (item) => item,
  fromFirestore: (
    snapshot: QueryDocumentSnapshot<T & DatabaseType>,
    options,
  ) => {
    const data = snapshot.data(options);
    const lastMessage = data.lastMessage
      ? {
          ...data.lastMessage,
          createdAt: data.lastMessage.createdAt.toDate().toISOString(),
        }
      : undefined;
    return {
      ...data,
      lastMessage,
      createdAt: data.createdAt.toDate().toISOString(),
    };
  },
});
const sendMessage: MutationFunction<
  unknown,
  {
    value: string;
    roomId: string;
    senderId: string;
    attachments: IMessageAttachment[];
  }
> = async (data) => {
  const roomRef = doc(collection(db, 'rooms'), data.roomId);
  const messageRef = doc(collection(roomRef, 'messages'));

  const roomDoc = await getDoc(roomRef);
  const roomData = roomDoc.data() as IRoom;

  const readByInit = roomData.currentMembers.reduce(
    (mapping, member) => ({ ...mapping, [member]: member === data.senderId }),
    {},
  );

  const messageContext = {
    id: messageRef.id,
    type: 'TEXT',
    value: data.value,
    senderId: data.senderId,
    attachments: data.attachments,
    createdAt: serverTimestamp(),
    readBy: readByInit,
    ...(roomData.aiThreadId ? { aiThreadId: roomData.aiThreadId } : {}),
    isFromAI: false,
  };

  const batch = writeBatch(db);
  batch.set(
    messageRef.withConverter(genericConverter<IMessage>()),
    messageContext,
    {
      merge: true,
    },
  );

  Object.values(roomData.currentMembers).forEach((id) => {
    // Skip AI assistant & senderId
    if (id === 'ai_assistant' || id === data.senderId) {
      return;
    }

    const userRef = doc(collection(db, 'users'), id);
    batch.update(roomRef, {
      [`numberOfUnreadMessages.${id}`]: increment(1),
    });
    batch.update(userRef, {
      totalUnreadMessages: increment(1),
      [`numberOfUnreadMessages.${data.roomId}`]: increment(1),
    });
  });

  batch.update(roomRef, {
    lastMessage: messageContext,
  });
  await batch.commit();
};

const updateMessage: MutationFunction<
  unknown,
  {
    messageId: string;
    roomId: string;
    value?: string;
    attachments?: IMessageAttachment[];
    metaActions?: MetaAction[];
    metaActionsTypes?: string[];
  }
> = async (data) => {
  cacheOperationLocks = (cacheOperationLocks || Promise.resolve()).then(
    async () => {
      const {
        messageId,
        roomId,
        value,
        attachments,
        metaActions,
        metaActionsTypes,
      } = data;
      const roomRef = doc(collection(db, 'rooms'), roomId);
      const messageRef = doc(collection(roomRef, 'messages'), messageId);

      const updateData: any = {
        updatedAt: serverTimestamp(),
      };

      if (value !== undefined) {
        updateData.value = value;
      }

      if (attachments !== undefined) {
        updateData.attachments = attachments;
      }

      if (metaActions !== undefined) {
        updateData.metaActions = metaActions;
      }

      if (metaActionsTypes !== undefined) {
        updateData.metaActionsTypes = metaActionsTypes;
      }

      await updateDoc(messageRef, updateData);

      const messageDoc = await getDoc(
        messageRef.withConverter(genericConverter<IMessage>()),
      );
      const updatedMessage = messageDoc.data() as IMessage;

      const cache = queryClient.getQueryData<InfiniteData<IMessage[]>>([
        'messages',
        roomId,
      ]);

      if (cache) {
        const newPages = cache.pages.map((page) => {
          return page.map((message) => {
            if (message.id === messageId) {
              return { ...updatedMessage, id: messageId };
            }
            return message;
          });
        });

        queryClient.setQueryData<InfiniteData<IMessage[]>>(
          ['messages', roomId],
          (oldData) => {
            return {
              pageParams: oldData?.pageParams || [],
              pages: newPages,
            };
          },
        );
      }
    },
  );

  return cacheOperationLocks;
};

const clearMessageMetaAction: MutationFunction<
  unknown,
  { roomId: string; messageId: string }
> = async ({ roomId, messageId }) => {
  return updateMessage({
    roomId,
    messageId,
    metaActions: [],
    metaActionsTypes: [],
  });
};

const clearAllMetaActionsFromCache = (roomId: string) => {
  cacheOperationLocks = (cacheOperationLocks || Promise.resolve()).then(
    async () => {
      const cache = queryClient.getQueryData<InfiniteData<IMessage[]>>([
        'messages',
        roomId,
      ]);

      if (cache) {
        const newPages = cache.pages.map((page) => {
          return page.map((message) => {
            return {
              ...message,
              metaActions: [],
              metaActionsTypes: [],
            };
          });
        });

        queryClient.setQueryData<InfiniteData<IMessage[]>>(
          ['messages', roomId],
          (oldData) => {
            return {
              pageParams: oldData?.pageParams || [],
              pages: newPages,
            };
          },
        );
      }
    },
  );

  return cacheOperationLocks;
};

const readMessages: MutationFunction<
  unknown,
  { roomId: string; senderId: string }
> = async ({ roomId, senderId }) => {
  const roomRef = doc(collection(db, 'rooms'), roomId);
  const userRef = doc(collection(db, 'users'), senderId);
  const unreadMessage = await getDocs(
    query(
      collection(roomRef, 'messages'),
      where(`readBy.${senderId}`, '!=', true),
    ),
  );

  const unreadMessageIds = unreadMessage.docs.map((d) => d.id);

  await runTransaction(db, async (transaction) => {
    const userDoc = await transaction.get(userRef);
    const userData = userDoc.data() as IUser;
    const roomTotalUnread = userData.numberOfUnreadMessages
      ? userData.numberOfUnreadMessages[roomId] || 0
      : 0;
    transaction.update(roomRef, {
      [`numberOfUnreadMessages.${senderId}`]: 0,
      [`readingTime.${senderId}`]: serverTimestamp(),
    });
    transaction.update(userRef, {
      totalUnreadMessages: increment(-roomTotalUnread),
      [`numberOfUnreadMessages.${roomId}`]: 0,
    });
    unreadMessageIds.forEach((id) => {
      const messageRef = doc(collection(roomRef, 'messages'), id);
      transaction.update(messageRef, {
        [`readBy.${senderId}`]: true,
      });
    });
  });
};

const getMessages: QueryFunction<IMessage[]> = async (key) => {
  const roomId = key.queryKey[1] as string;
  let date = new Date();
  if (key.pageParam) {
    date = new Date(key.pageParam);
  }
  const snapshot = await getDocs(
    query(
      collection(doc(collection(db, 'rooms'), roomId), 'messages'),
      orderBy('createdAt', 'desc'),
      where('createdAt', '<', date),
      limit(PER_PAGE),
    ).withConverter(genericConverter<IMessage>()),
  );
  return snapshot.docs.map((d) => ({ ...d.data(), id: d.id }));
};

const hasMessageBefore = async (roomId: string, senderId: string) => {
  if (!senderId) {
    return false;
  }
  const data = await getDocs(
    query(
      collection(doc(collection(db, 'rooms'), roomId), 'messages'),
      where('senderId', '==', senderId),
      limit(1),
    ),
  );
  return !!data.docs.length;
};

const addDataToQueryCache = <T>(key: QueryKey, data: T & { id: string }) => {
  cacheOperationLocks = (cacheOperationLocks || Promise.resolve()).then(
    async () => {
      const cache =
        queryClient.getQueryData<InfiniteData<(T & { id: string })[]>>(key);
      let currentItems = cache?.pages.flat() || [];
      if (data.id === currentItems[0]?.id) {
        currentItems[0] = { ...data };
      } else {
        currentItems = currentItems.filter((item) => item.id !== data.id);
        currentItems.unshift(data);
      }
      const newData: T[][] = [];
      for (let i = 0; i < currentItems.length; i += PER_PAGE) {
        const currentPage = currentItems.slice(i, i + PER_PAGE);
        newData.push(currentPage);
      }
      queryClient.setQueryData<InfiniteData<T[]>>(key, (oldData) => {
        return {
          pageParams: oldData?.pageParams || [],
          pages: newData,
        };
      });
    },
  );
};

const attachMessageListener = (key: any): (() => void) => {
  const roomId = key.queryKey[1] as string;
  const messageQuery = query(
    collection(doc(collection(db, 'rooms'), roomId), 'messages'),
    where(
      'createdAt',
      '>',
      key.startAfter ? new Date(key.startAfter) : new Date(),
    ),
  ).withConverter(genericConverter<IMessage>());
  return onSnapshot(messageQuery, (snap) => {
    const changes = snap.docChanges();
    changes.forEach((change) => {
      if (change.type === 'added') {
        const data = { ...change.doc.data(), id: change.doc.id };
        addDataToQueryCache(['messages', roomId], data);
      }
    });
  });
};

const getDeletedRoomMemberInfo = async (roomId: string) => {
  const userRef = doc(collection(db, 'rooms'), roomId);
  const docSnap = await getDoc(userRef);
  const allMembers = docSnap.data()?.allMembers as string[];
  const currentMembers = docSnap.data()?.currentMembers as string[];
  const deletedMembers = allMembers.filter(
    (member) => !currentMembers.includes(member),
  );

  if (!deletedMembers.length) {
    return [];
  }

  const membersQuery = query(
    collection(db, 'users'),
    where('id', 'in', deletedMembers),
  );

  const usersSnap = await getDocs(membersQuery);
  return usersSnap.docs.map((snap) => snap.data());
};

const getMembersInfo = async (roomId: string) => {
  const userRef = doc(collection(db, 'rooms'), roomId);
  const docSnap = await getDoc(userRef);
  const allMembers = docSnap.data()?.allMembers as string[];

  if (!allMembers.length) {
    return [];
  }

  const membersQuery = query(
    collection(db, 'users'),
    where('id', 'in', allMembers),
  );

  const usersSnap = await getDocs(membersQuery);
  return usersSnap.docs.map((snap) => snap.data() as IUser);
};

const getCurrentUserData: QueryFunction<IUser> = async (params) => {
  const userId = params.queryKey[2] as string;

  const snapshot = await getDoc(doc(db, 'users', userId));

  return { ...snapshot.data(), id: snapshot.id } as IUser;
};

const attachUserDataListener: QueryFunction<Unsubscribe> = (params) => {
  const userId = params.queryKey[1] as string;

  const userRef = doc(db, 'users', userId);

  return onSnapshot(userRef, (dataChanges) => {
    const userChange = dataChanges.data() as IUser;

    queryClient.setQueryData<IUser>(
      ['users', 'firebase-user-data', userId],
      (oldData) => ({
        ...oldData,
        ...userChange,
      }),
    );
  });
};

// const attachConversationListener = () => {
//   const conversationQuery = query(
//     collection(db, 'rooms'),
//     where('status', '==', 'ACTIVE'),
//     where('allMembers', 'array-contains', '64c9d7418be5ffa404a8cbb3'),
//     where('lastMessage.createdAt', '>', new Date()),
//     orderBy('lastMessage.createdAt', 'desc'),
//   ).withConverter(genericConverter<IRoom>());
//   return onSnapshot(conversationQuery, (snap) => {
//     const changes = snap.docChanges();
//     changes.forEach((change) => {
//       if (['added', 'modified'].includes(change.type)) {
//         const data = { ...change.doc.data(), id: change.doc.id };
//         addDataToQueryCache(['rooms'], data);
//       }
//     });
//   });
// };
// const getRoomList: QueryFunction<IRoom[], string[]> = async (params) => {
//   const snapshot = await getDocs(
//     query(
//       collection(db, 'rooms'),
//       where('status', '==', 'ACTIVE'),
//       where('allMembers', 'array-contains', '64c9d7418be5ffa404a8cbb3'),
//       orderBy('lastMessage.createdAt', 'desc'),
//       startAfter(params.pageParam ? new Date(params.pageParam) : new Date()),
//       limit(PER_PAGE),
//     ).withConverter(genericConverter<IRoom>()),
//   );
//   return snapshot.docs.map((d) => ({ ...d.data(), id: d.id }));
// };

const getRoomData = async (roomId: string): Promise<IRoom> => {
  const roomRef = doc(collection(db, 'rooms'), roomId);
  const roomDoc = await getDoc(roomRef);
  return { ...roomDoc.data(), id: roomDoc.id } as IRoom;
};

const attachRoomListener = (roomId: string) => {
  const roomRef = doc(collection(db, 'rooms'), roomId);
  return onSnapshot(roomRef, (snap) => {
    const data = { ...snap.data(), id: snap.id };
    addDataToQueryCache(['chat', 'rooms', roomId], data);
  });
};

export const chatService = {
  sendMessage,
  getMessages,
  attachMessageListener,
  clearMessageMetaAction,
  clearAllMetaActionsFromCache,
  readMessages,
  hasMessageBefore,
  // attachConversationListener,
  // getRoomList,
  getDeletedRoomMemberInfo,
  PER_PAGE,
  getMembersInfo,
  getCurrentUserData,
  attachUserDataListener,
  getRoomData,
  attachRoomListener,
  updateMessage,
};
