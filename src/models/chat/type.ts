import type { CollectMethod } from 'models/job/type';
import type { Roles, UserStatus } from 'utils/constants';

export type IRoom = {
  id: string;
  status: string;
  type: string;
  currentMembers: string[];
  allMembers: string[];
  numberOfUnreadMessages: Record<string, number>;
  readingTime: Record<string, string>;
  lastMessage?: IMessage;
  createdAt: string;
  aiThreadId?: string;
  isAIAssisting?: boolean;
  collectMethod?: CollectMethod;
};

export enum ActionHandlerNames {
  CONFIRM_COLLECT_METHOD_MANUAL = 'CONFIRM_COLLECT_METHOD_MANUAL',
  CONFIRM_COLLECT_METHOD_UPLOAD = 'CONFIRM_COLLECT_METHOD_UPLOAD',
  COMPLETE_UPLOAD = 'COMPLETE_UPLOAD',
  TRIGGER_UPLOAD_FILE = 'TRIGGER_UPLOAD_FILE',
}

export type MetaAction = {
  content: string;
  component: 'button' | 'div';
  props?: Record<string, unknown>;
  action?: ActionHandlerNames;
};

export type IMessage = {
  id: string;
  type: string;
  value: string;
  createdAt: string;
  senderId: string;
  attachments: IMessageAttachment[];
  readBy: Record<string, boolean>;
  metaActions?: MetaAction[];
  metaActionsTypes?: string[];
};

export type IUser = {
  id: string;
  avatar: string;
  email: string;
  fullName: string;
  fullNameKata: string;
  role: Roles;
  status: UserStatus;
  totalUnreadMessages?: number;
  numberOfUnreadMessages?: Record<string, number>;
};

export enum FileType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  FILE = 'FILE',
}

export type IMessageAttachment = {
  url: string;
  name: string;
  contentType: string;
  id: string;
  status: string;
  width?: number;
  height?: number;
  size: number;
  type: FileType;
};

export type IAttachment = {
  key: string;
  url: string;
  file: File;
};
