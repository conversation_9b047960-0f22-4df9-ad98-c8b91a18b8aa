import type { QueryModel } from 'utils/type';

const chatQuery = {
  createChatRoom: {
    apiUrl: '/workers/contact-admin',
    method: 'POST',
  },
  updateDmRoomHasMessage: (id?: string) => ({
    apiUrl: `/workers/dm/${id}/hasMessageChatRoom`,
    method: 'patch',
  }),
  createSearchJobChatRoom: () => ({
    apiUrl: '/workers/ai/assistant/chat',
    method: 'POST',
  }),
} satisfies QueryModel;

export default chatQuery;
