import type { QueryModel } from 'utils/type';

const jobQuery = {
  recommendJobPosts: {
    queryKey: ['recommend-job-posts'],
    apiUrl: '/recommend-job-posts',
    customParams: {
      limit: 9,
    },
    cacheTime: 0,
  },
  getJobTypes: {
    queryKey: ['job-types'],
    apiUrl: '/workers/job-types',
  },
  postList: ({ workAreas, jobTypeIds, order }: Record<string, unknown>) => ({
    queryKey: ['job-posts', workAreas, jobTypeIds, order],
    apiUrl: '/job-posts',
  }),
  postDetail: (id: string) => ({
    queryKey: ['job-posts', id],
    apiUrl: `/job-posts/${id}`,
    meta: {
      noToastError: true,
    },
  }),
  applyJob: {
    apiUrl: '/workers/job-posts/apply',
    method: 'post',
    meta: {
      noToastError: true,
    },
  },
  openContact: {
    apiUrl: '/workers/job-posts/contact',
    method: 'post',
  },
  openContactAIAssistant: {
    apiUrl: '/workers/job-posts/contact-ai-assistant',
    method: 'post',
  },
  applyWithAIAssistant: {
    apiUrl: '/workers/job-posts/apply-with-ai-assistant',
    method: 'post',
  },
  getMatchingList: {
    queryKey: ['job-matchings'],
    apiUrl: '/workers/matchings',
    customParams: {
      limit: 10,
    },
  },
  getMatchingDetail: (id: string) => ({
    queryKey: ['currentUser', 'job-matchings', id],
    apiUrl: `/workers/matchings/${id}`,
  }),
  getContractedWorkerDetail: ({
    matchingId,
    contractedWorkerId,
  }: {
    matchingId: string;
    contractedWorkerId: string;
  }) => ({
    queryKey: ['worker', matchingId, contractedWorkerId],
    apiUrl: `/workers/matchings/${matchingId}/worker-contracts/${contractedWorkerId}`,
  }),
  createContractedWorker: (id: string) => ({
    apiUrl: `/workers/matchings/${id}/worker-contracts`,
    method: 'post',
    successMessage: '完了しました。',
  }),
  editContractedWorker: ({
    matchingId,
    contractedWorkerId,
  }: {
    matchingId: string;
    contractedWorkerId: string;
  }) => ({
    apiUrl: `/workers/matchings/${matchingId}/worker-contracts/${contractedWorkerId}`,
    method: 'patch',
    successMessage: '完了しました。',
  }),
  updateMatchingDocuments: (id) => ({
    apiUrl: `/workers/matchings/${id}/documents`,
    method: 'patch',
  }),
  membersInfo: (id) => ({
    queryKey: ['currentUser', 'job-matchings', id, 'memebers-info'],
    apiUrl: `/workers/matchings/${id}/members-info`,
  }),
  updateRoomHasMessage: (matchingId: string) => ({
    apiUrl: `/workers/matchings/${matchingId}/hasMessageChatRoom`,
    method: 'patch',
  }),
  confirmMatchingCollectingMethod: (matchingId: string) => ({
    apiUrl: `/workers/matchings/${matchingId}/confirm-collect-method`,
    method: 'patch',
  }),
  completeUploadAndStartOCR: (matchingId: string) => ({
    apiUrl: `/workers/matchings/${matchingId}/complete-upload-and-start-ocr`,
    method: 'patch',
  }),
  submitMatching: (matchingId: string) => ({
    apiUrl: `/workers/matchings/${matchingId}/submit`,
    method: 'post',
    successMessage: '完了しました。',
  }),
} satisfies QueryModel;

export default jobQuery;
