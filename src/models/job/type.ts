import type { IFile } from 'models/auth/type';
import type { StatusBadgeColor } from 'utils/constants';

export type IJobPostInfo = {
  assignedRecruiters?: string[];
  endDate?: string | null;
  startDate: string;
  workArea: string;
  title: string;
  isPrivateSalary: boolean;
  commutingCondition?: string;
  peopleNeeded?: number;
  publicationEndDate: string;
  minSalary?: number;
  maxSalary?: number;
  nationalCertifications?: string[];
  appliedMatching?: {
    jobPostId: string;
    _id: string;
    status: string;
    workerId: string;
  };
};

export type IJobPostItem = {
  _id: string;
  companyId: {
    _id: string;
    name: string;
    nameKata: string;
  };
  jobType?: {
    _id: string;
    name: string;
  };
  peopleJoined?: number;
  status: string;
  detail?: string;
  paymentPeriod?: string;
  constructionCertificationCriteria?: string[];
  insuranceCriteria?: string[];
  createdAt: string;
  updatedAt: string;
  appliedWorkers?: string[];
  dormitory?: ICriteria | null;
  foodAllowance?: ICriteria | null;
  commutingAllowance?: ICriteria | null;
  dailyCommutingAllowance?: ICriteria | null;
} & IJobPostInfo;

export type IMatchingJobPostInfo = {
  companyId: string;
  companyName: string;
  companyNameKata: string;
  jobType: string;
  status: string;
  createdBy: string;
} & IJobPostInfo;

export interface IJobMatchingItem {
  _id: string;
  jobPostId: string;
  jobPostInfo: IMatchingJobPostInfo;
  cvDocuments?: IFile[];
  workerContracts?: IWorkerContract[];
  updatedAt: string;
  createdAt: string;
  status: 'CONTACT' | 'APPLYING' | 'FINISHED' | 'COLLECTING';
  chatInfo: {
    roomFirebaseId: string;
    roomSearchId: string;
    // memberFirebaseIds: string[];
  };
  newGroupChat?: boolean;
}

export interface IJobType {
  _id: string;
  name: string;
}

export interface IJobApplyRequest {
  jobPostId: string;
  matchingInfo: {
    cvDocumentKeys: string[];
    workerContracts: {
      workerName: string;
      applicationDocumentKeys: string[];
    }[];
  };
}

export type IWorkerContract = {
  _id: string;
  workerName: string;
  lastName: string;
  firstName: string;
  lastNameKata: string;
  firstNameKata: string;
  isRequiredDocuments: boolean;
  isDoneDetail: boolean;
  isDoneDocuments: boolean;
  applicationDocuments: IFile[];
  employmentInsurance?: {
    type: string;
    number: string;
  };
  status: IWorkerContractStatus;
  dailySalary: number;
};

export type IWorkerContractStatus =
  | 'PENDING'
  | 'APPROVED'
  | 'DENIED'
  | 'REMOVED'
  | 'COLLECTING';

export type ICriteria = 'YES' | 'NO' | 'ACCEPT_OFFERS';

export enum CriteriaLabel {
  YES = '有り',
  NO = '無し',
  ACCEPT_OFFERS = '応相談',
}

export const MATCHING_STATUS: Record<
  IJobMatchingItem['status'],
  { label: string; color: keyof typeof StatusBadgeColor }
> = {
  APPLYING: {
    label: '応募中',
    color: 'INFO',
  },
  COLLECTING: {
    label: '名簿受付中',
    color: 'COLLECTING',
  },
  CONTACT: {
    label: '問い合わせ中',
    color: 'WARNING',
  },
  FINISHED: {
    label: '募集完了',
    color: 'SUCCESS',
  },
};

export const WORKER_CONTRACT_STATUS: Record<
  IWorkerContractStatus,
  { label: string; color: keyof typeof StatusBadgeColor }
> = {
  PENDING: {
    label: '対応待ち',
    color: 'WARNING',
  },
  APPROVED: {
    label: '承認',
    color: 'SUCCESS',
  },
  DENIED: {
    label: '却下',
    color: 'ERROR',
  },
  REMOVED: {
    label: '解除',
    color: 'ERROR',
  },
  COLLECTING: {
    label: '収集中',
    color: 'INFO',
  },
};

export const SORT_ORDER_VALUES = {
  nearest: '工期が近い順（デフォルト）',
  salary: '単価が高い順',
  public_end_date: '応募期限が近い順',
};

export enum CollectMethod {
  UPLOAD = 'UPLOAD',
  MANUAL = 'MANUAL',
}

export interface IConfirmCollectMethodDTO {
  collectMethod: CollectMethod;
}
