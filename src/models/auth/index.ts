import type { QueryModel } from 'utils/type';

const authQuery = {
  getCurrentUser: {
    apiUrl: '/api/auth/me',
    queryKey: ['currentUser'],
    axiosConfig: {
      baseURL: '/',
    },
  },
  login: {
    apiUrl: '/api/auth/login',
    axiosConfig: {
      baseURL: '/',
    },
  },
  logout: {
    apiUrl: '/api/auth/logout',
    axiosConfig: {
      baseURL: '/',
    },
  },
  refreshToken: {
    apiUrl: '/auth/workers/access-token',
  },
  register: {
    apiUrl: '/workers/register',
    method: 'post',
  },
  verifyEmailToken: {
    apiUrl: '/workers/verify-email-token',
    method: 'post',
  },
  setPassword: {
    apiUrl: '/workers/set-password',
    method: 'post',
  },
  updateWorkerInfo: {
    apiUrl: '/workers/worker-info',
    method: 'patch',
    successMessage: '完了しました。',
  },
  updateBasicInfo: {
    apiUrl: '/workers/basic-info',
    method: 'patch',
  },
  firebaseToken: {
    apiUrl: '/workers/firebaseToken',
    method: 'get',
  },
  requestNewPassword: {
    apiUrl: '/forget-password/worker/request-new-password',
    method: 'post',
  },
  verifyTokenForgetPassword: {
    apiUrl: '/forget-password/worker/verify-token',
    method: 'post',
  },
  setNewPassword: {
    apiUrl: '/forget-password/worker/set-new-password',
    method: 'post',
  },
} satisfies QueryModel;

export default authQuery;
