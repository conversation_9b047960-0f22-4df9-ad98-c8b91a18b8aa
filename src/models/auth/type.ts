import type { Omit } from 'lodash';
import type {
  NationalCertificate,
  Roles,
  UserStatus,
  WorkerType,
} from 'utils/constants';

export type IFile = {
  _id: string;
  key: string;
  originalName: string;
  originUrl: string;
  url: string;
  readOnly?: boolean;
  file?: File;
};

export type IUser = {
  _id: string;
  lastName?: string;
  firstName?: string;
  firstNameKata?: string;
  lastNameKata?: string;
  avatar?: IFile;
  email: string;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
  phone?: string;
  status: UserStatus;
  role: Roles;
  isCompletedProfile: boolean;
  fullName?: string;
  fullNameKata?: string;
  id?: string;
};

export type IWorkerInformation = {
  birthDate?: string;
  type: keyof typeof WorkerType;
  companyName?: string;
  companyPhone?: string;
  tradeName?: string;
  postCode?: string;
  prefecture?: string;
  city?: string;
  district?: string;
  building?: string;
  jobTypes?: { _id: string; name: string }[];
  nationalCertifications?:
    | (keyof typeof NationalCertificate)[]
    | (typeof NoNationalCertification)[];
  cvDocuments?: IFile[];
  firebaseUserId?: string;
  constructionCertifications?:
    | IConstructionCertification[]
    | (typeof NoRequired)[];
  insurances?: IInsurance[] | (typeof NoRequired)[];
  selfIntroduction?: string;
  applicationDocuments?: IFile[];
  chatInfo?: IChatInfo;
};

type IChatInfo = {
  _id: string;
  roomId?: string;
};

export type IWorker = IWorkerInformation & IUser;

export type ISetPasswordRequest = {
  password: string;
  token: string;
};

export type IForgotPasswordTokenVerifyData = {
  _id: string;
  userId: string;
  updatedAt: string;
  token: string;
  side: 'worker' | 'admin';
  expiredAt: string;
  createdAt: string;
};

export type IWorkerProfileRequest = {
  jobTypes: string[];
  cvDocumentKeys?: string[];
  applicationDocumentKeys?: string[];
} & Omit<
  IWorkerInformation,
  'jobTypes' | 'cvDocuments' | 'applicationDocuments'
>;

export type IConstructionCertification = '一般建設業許可' | '特定建設業許可';
export type IInsurance = '社会保険' | '一人親方労災保険' | '第三者賠償責任保険';
export const NoRequired = '持っていない';
export const NoNationalCertification = 'NOT_REGISTERED';
