import { createGetInitialProps } from '@mantine/next';
import Document, { Head, Html, Main, NextScript } from 'next/document';
import Script from 'next/script';

const getInitialProps = createGetInitialProps();

class MyDocument extends Document {
  static getInitialProps = getInitialProps;

  // eslint-disable-next-line class-methods-use-this
  render() {
    return (
      <Html lang="ja">
        <Head>
          <link href="/favicon.ico" rel="shortcut icon" />
          {/* Google Tag Manager */}
          <Script
            dangerouslySetInnerHTML={{
              __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl+ '&gtm_auth=${process.env.GOOGLE_TAG_MANAGER_AUTH}&gtm_preview=${process.env.GOOGLE_TAG_MANAGER_REVIEW}&gtm_cookies_win=x';f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','${process.env.GOOGLE_TAG_MANAGER_ID}')`,
            }}
            id="googleTagManager"
            strategy="afterInteractive"
          ></Script>
          {/* End Google Tag Manager */}
          {/* <!-- Google tag (gtag.js) -->  */}
          <Script
            async
            src={`https://www.googletagmanager.com/gtag/js?id=${process.env.GOOGLE_MEASUREMENT_ID}`}
            strategy="afterInteractive"
          ></Script>
          <Script
            dangerouslySetInnerHTML={{
              __html: `window.dataLayer = window.dataLayer || []; 
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date()); gtag('config', '${process.env.GOOGLE_MEASUREMENT_ID}');`,
            }}
            id="gtag"
            strategy="afterInteractive"
          ></Script>
        </Head>

        <body>
          <noscript
            dangerouslySetInnerHTML={{
              __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=${process.env.GOOGLE_TAG_MANAGER_ID}&gtm_auth=${process.env.GOOGLE_TAG_MANAGER_AUTH}&gtm_preview=${process.env.GOOGLE_TAG_MANAGER_REVIEW}&gtm_cookies_win=x"
              height="0" width="0" style="display:none;visibility:hidden"></iframe>
              `,
            }}
          ></noscript>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
