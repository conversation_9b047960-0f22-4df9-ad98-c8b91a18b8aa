import { Flex } from '@mantine/core';
import Layout from 'components/Layout';
import RegisterForm from 'components/RegisterForm';
import { useEffect } from 'react';
import helpers from 'utils/helpers';

const RegisterPage = () => {
  useEffect(() => {
    helpers.logEventTracking('start_sign_up');
  }, []);

  return (
    <Flex
      align={'center'}
      h={'100%'}
      justify={'center'}
      px={{ base: 16, sm: 0 }}
      py={{ base: 84, sm: 0 }}
    >
      <RegisterForm />
    </Flex>
  );
};

RegisterPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default RegisterPage;
