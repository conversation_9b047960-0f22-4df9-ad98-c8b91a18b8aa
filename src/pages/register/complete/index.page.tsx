import { Flex } from '@mantine/core';
import BasicInfomationStep from 'components/CompleteProfile/BasicInfomationStep';
import StepProgress from 'components/CompleteProfile/StepProgress';
import WorkerInformationStep from 'components/CompleteProfile/WorkerInformationStep';
import Layout from 'components/Layout';
import { useUser } from 'hooks';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';

const CompleteProfilePage = () => {
  const { data: currentUser } = useUser();
  const { query, push, pathname, events } = useRouter();

  const step =
    typeof query.step === 'string' && ['1', '2'].includes(query.step)
      ? Number(query.step)
      : 1;

  // useEffect(() => {
  //   beforePopState(() => {
  //     // Prevent backing browser on this page
  //     return false;
  //   });
  //   return () => {
  //     beforePopState(() => {
  //       return true;
  //     });
  //   };
  // }, [beforePopState]);

  useEffect(() => {
    events.on('routeChangeStart', () => {
      return false;
    });

    return events.off('routeChangeStart', () => {
      return true;
    });
  }, [events]);

  const handleChangeStep = (nextStep: number) => {
    push(
      {
        pathname,
        query: {
          step: nextStep,
        },
      },
      undefined,
      {
        shallow: true,
      },
    );
  };

  const renderStepContent = () => {
    if (
      step === 2 &&
      currentUser?.firstName &&
      currentUser?.lastName &&
      currentUser?.firstNameKata &&
      currentUser?.lastNameKata
    ) {
      return <WorkerInformationStep />;
    }
    return (
      <BasicInfomationStep
        onComplete={() => {
          handleChangeStep(2);
        }}
      />
    );
  };

  return (
    <Flex
      align={'center'}
      direction="column"
      gap={24}
      h="100%"
      justify={'flex-start'}
      pt={{ base: 24, sm: 40 }}
    >
      <StepProgress step={step} />
      {renderStepContent()}
    </Flex>
  );
};

CompleteProfilePage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default CompleteProfilePage;
