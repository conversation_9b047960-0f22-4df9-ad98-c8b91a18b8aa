import { Flex } from '@mantine/core';
import Layout from 'components/Layout';
import SetPasswordForm from 'components/SetPasswordForm';
import React from 'react';

const SetPasswordPage = () => {
  return (
    <Flex
      align={'center'}
      h={'100%'}
      justify={'center'}
      px={{ base: 16, sm: 0 }}
      py={{ base: 84, sm: 0 }}
    >
      <SetPasswordForm />
    </Flex>
  );
};

SetPasswordPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default SetPasswordPage;
