import IconArrowUp from '@icons/icon-arrow-up.svg';
import { ActionIcon, Affix, Box, Transition } from '@mantine/core';
import { useWindowScroll } from '@mantine/hooks';
import {
  CallToAction,
  Comparison,
  ContactBlock,
  Drawer,
  FAQs,
  Feature,
  Footer,
  Header,
  HeroBanner,
  ProblemSolution,
  Testimonial,
  UsageFlow,
} from 'components/RecruiterLP';
import React from 'react';

const RecruiterLandingPage = () => {
  const [scroll, scrollTo] = useWindowScroll();

  return (
    <Box>
      <Header />
      <Drawer />
      <HeroBanner />
      <ProblemSolution />
      <CallToAction buttonClassname="solution" />
      <UsageFlow />
      <Feature />
      <CallToAction buttonClassname="feature" />
      <Comparison />
      <Testimonial />
      <FAQs />
      <ContactBlock />
      <Footer />
      <Affix position={{ bottom: '5%', right: 20 }}>
        <Transition mounted={scroll.y > 0} transition="slide-up">
          {(transitionStyles) => (
            <ActionIcon
              onClick={() => scrollTo({ y: 0 })}
              size="r50"
              style={{
                ...transitionStyles,
              }}
              variant="white"
            >
              <IconArrowUp />
            </ActionIcon>
          )}
        </Transition>
      </Affix>
    </Box>
  );
};

export default RecruiterLandingPage;
