import '@fontsource/noto-sans-jp/400.css';
import '@fontsource/noto-sans-jp/500.css';
import '@fontsource/noto-sans-jp/600.css';
import '@fontsource/noto-sans-jp/700.css';
import 'utils/yupConfig';
import 'dayjs/locale/ja';

import { MantineProvider, ScrollArea } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { ModalsProvider } from '@mantine/modals';
import { Notifications } from '@mantine/notifications';
import type { DehydratedState } from '@tanstack/react-query';
import { Hydrate, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import AuthProvider from 'components/AuthProvider';
import FirebaseProvider from 'components/FirebaseProvider';
import GALogger from 'components/GALogger';
import { useAsPathInitializer } from 'hooks/useAsPath';
import type { NextPage } from 'next';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { DefaultSeo } from 'next-seo';
import type { ReactElement, ReactNode } from 'react';
import React from 'react';
import ReactGA from 'react-ga4';
import theme from 'theme';
import queryClient from 'utils/queryClient';

type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout<P = {}> = {
  Component: NextPageWithLayout<P>;
} & AppProps<P>;

// export const reportWebVitals = (metric: NextWebVitalsMetric) => {
//   console.log(metric);
// };

const MyApp = ({
  Component,
  pageProps,
}: AppPropsWithLayout<{ dehydratedState?: DehydratedState }>) => {
  const getLayout = Component.getLayout ?? ((page) => page);
  useAsPathInitializer();

  if (process.env.GOOGLE_MEASUREMENT_ID) {
    ReactGA.initialize(process.env.GOOGLE_MEASUREMENT_ID);
  }

  return (
    <MantineProvider
      theme={theme}
      withCSSVariables
      withGlobalStyles
      withNormalizeCSS
    >
      <Head>
        <meta content="width=device-width, initial-scale=1" name="viewport" />
        <meta content="/images/teqnowared.png" property="og:image" />
      </Head>
      <DefaultSeo
        dangerouslySetAllPagesToNoFollow={
          process.env.DEPLOY_ENV !== 'production'
        }
        dangerouslySetAllPagesToNoIndex={
          process.env.DEPLOY_ENV !== 'production'
        }
        description="テクノワは、プラント工事特化の案件を通じて企業とスキルの高い一人親方や企業が繋がれるマッチングプラットフォーム"
        openGraph={{
          title: 'テクノワ | プラント工事案件特化のマッチングプラットフォーム',
          description:
            'テクノワは、プラント工事特化の案件を通じて企業とスキルの高い一人親方や企業が繋がれるマッチングプラットフォーム',
          site_name:
            'テクノワ | プラント工事案件特化のマッチングプラットフォーム',
        }}
        title="テクノワ | プラント工事案件特化のマッチングプラットフォーム"
      />
      <QueryClientProvider client={queryClient}>
        <Hydrate state={pageProps.dehydratedState}>
          <ReactQueryDevtools initialIsOpen={false} />
          <ModalsProvider
            modalProps={{
              scrollAreaComponent: ScrollArea.Autosize,
            }}
          >
            <DatesProvider settings={{ locale: 'ja' }}>
              <Notifications containerWidth={440} />
              <FirebaseProvider />
              <AuthProvider />
              <GALogger />
              {getLayout(<Component {...pageProps} />)}
            </DatesProvider>
          </ModalsProvider>
        </Hydrate>
      </QueryClientProvider>
    </MantineProvider>
  );
};

export default MyApp;
