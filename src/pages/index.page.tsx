import { Box } from '@mantine/core';
import {
  FAQ,
  Introduction,
  JobPost,
  TopBanner,
  Tutorial,
} from 'components/Home';
import ChatFloatingBanner from 'components/Home/ChatFloatingBanner';
import Layout from 'components/Layout';
import React from 'react';

const HomePage = () => {
  return (
    // Homepage
    <Box>
      <ChatFloatingBanner />
      <TopBanner />
      <JobPost />
      <Introduction />
      <Tutorial />
      <FAQ />
    </Box>
  );
};

HomePage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default HomePage;
