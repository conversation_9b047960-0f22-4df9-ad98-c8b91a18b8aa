import { Flex, LoadingOverlay } from '@mantine/core';
import Layout from 'components/Layout';
import { useUser } from 'hooks';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import helpers from 'utils/helpers';

const ContactChatRoomPage = () => {
  const { data: userData, isLoading = false } = useUser();
  const { query, replace } = useRouter();
  const roomId = Array.isArray(query.id) ? query.id.join('') : query.id;

  useEffect(() => {
    if (!isLoading && userData?.chatInfo) {
      if (roomId && roomId === userData.chatInfo.roomId) {
        replace('/contact-admin');
      } else {
        helpers.toast({
          message: 'この操作を行う権限がありません。',
          type: 'error',
        });
        replace('/');
      }
    }
  }, [isLoading, replace, roomId, userData?.chatInfo]);

  if (isLoading) {
    return (
      <Flex align={'center'} justify={'center'} mih={240}>
        <LoadingOverlay visible />
      </Flex>
    );
  }

  return null;
};

ContactChatRoomPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default ContactChatRoomPage;
