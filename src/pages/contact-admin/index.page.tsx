import IconEmptyMessage from '@icons/icon-empty-messages.svg';
import { Flex, Text } from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import { useMutation, useQuery } from '@tanstack/react-query';
import Layout from 'components/Layout';
import { MessageList } from 'components/Messages';
import type { TSendMessagePayload } from 'components/Messages/MessageActions';
import MessageActions from 'components/Messages/MessageActions';
import Utils from 'components/Messages/utils';
import { useMutate, useUser } from 'hooks';
import debounce from 'lodash/debounce';
import type { IUser } from 'models/auth/type';
import chatQuery from 'models/chat';
import { chatService } from 'models/chat/chatService';
import type { IMessageAttachment } from 'models/chat/type';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';

const ChatPage = () => {
  const { data: userData, refetch } = useUser();
  const firebaseUserId = userData?.firebaseUserId;
  const id = userData?.chatInfo?.roomId;
  const hasMessageRef = useRef<boolean>(true);

  const { mutateAsync: createChatRoom } = useMutate<
    unknown,
    { _id: string; roomId: string }
  >(chatQuery.createChatRoom);

  useEffect(() => {
    if (!!userData && !!firebaseUserId && !id) {
      createChatRoom(
        {},
        {
          onSuccess: () => refetch(),
        },
      );
    }
  }, [createChatRoom, firebaseUserId, id, refetch, userData]);

  const { data: firebaseMembersInfo } = useQuery<unknown, unknown, IUser[]>({
    queryKey: ['users', 'firebase'],
    refetchOnReconnect: false,
    queryFn: () => chatService.getMembersInfo(id || ''),
    enabled: !!id && !!firebaseUserId,
  });

  const membersInfo = firebaseMembersInfo?.reduce<Record<string, IUser>>(
    (members, v) => ({ ...members, [v.id || '']: v }),
    {},
  );

  const { mutateAsync: updateRoomHasMessage } = useMutate(
    chatQuery.updateDmRoomHasMessage(id),
  );

  const { mutateAsync: sendMessage, isLoading: isSendingMessage } = useMutation<
    unknown,
    unknown,
    {
      value: string;
      roomId: string;
      senderId: string;
      attachments: IMessageAttachment[];
    }
  >({
    mutationFn: (payload) =>
      chatService.sendMessage({
        ...payload,
      }),
  });

  const handleSendMessage = useCallback(
    async (payload: TSendMessagePayload, onSuccess: () => void) => {
      if (firebaseUserId) {
        await sendMessage({
          value: payload.message,
          roomId: id || '',
          senderId: firebaseUserId,
          attachments: payload.attachments.map((file) => ({
            id: file.key,
            contentType: file.file?.type || '',
            name: file.originalName,
            url: file.originUrl,
            size: file.file?.size || 0,
            status: 'ACTIVE',
            type: Utils.getFileTypeFromContentType(file.file?.type || ''),
          })),
        });
        onSuccess?.();

        if (!hasMessageRef.current) {
          updateRoomHasMessage({});
          hasMessageRef.current = true;
        }
      }
    },
    [firebaseUserId, id, sendMessage, updateRoomHasMessage],
  );

  const debounceResize = useMemo(
    () =>
      debounce(() => {
        // First we get the viewport height and we multiple it by 1% to get a value for a vh unit
        const vh = window.innerHeight * 0.01;
        // Then we set the value in the --vh custom property to the root of the document
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      }, 300),
    [],
  );

  useWindowEvent('resize', debounceResize);

  if (!userData || !firebaseUserId || !id) {
    return null;
  }

  return (
    <Flex
      align="stretch"
      direction={'column'}
      h="100%"
      sx={{
        maxHeight: 'calc(var(--vh, 1vh) * 100 - var(--mantine-header-height))',
      }}
    >
      <Flex
        bg="White"
        direction="column"
        mih={0}
        pos="relative"
        py={16}
        sx={{ flex: '1' }}
        w="100%"
      >
        {/* Message list */}
        <MessageList
          EmptyMessage={
            <Flex
              align={'center'}
              direction={'column'}
              gap={8}
              justify={'center'}
            >
              <IconEmptyMessage />
              <Text lh={1.5} size={16}>
                お気軽にご相談ください
              </Text>
            </Flex>
          }
          members={membersInfo}
          onMarkRoomHasNoMessage={() => {
            hasMessageRef.current = false;
          }}
          pb={{ base: 28, sm: 0 }}
          pt={24}
          px={{ base: 16, sm: 24 }}
          roomId={id}
        />
        {/* Message input */}
        <MessageActions
          disabled={!firebaseUserId}
          loading={isSendingMessage}
          maxInputRow={4}
          onSendMessage={handleSendMessage}
          readonly={isSendingMessage}
        />
      </Flex>
    </Flex>
  );
};

ChatPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default ChatPage;
