import { Flex } from '@mantine/core';
import Layout from 'components/Layout';
import ForgetPasswordForm from 'components/SetPasswordForm/ForgetPassword';
import React from 'react';

const ForgetPasswordPage = () => {
  return (
    <Flex
      align={'center'}
      h={'100%'}
      justify={'center'}
      px={{ base: 16, sm: 0 }}
      py={{ base: 84, sm: 0 }}
    >
      <ForgetPasswordForm />
    </Flex>
  );
};

ForgetPasswordPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default ForgetPasswordPage;
