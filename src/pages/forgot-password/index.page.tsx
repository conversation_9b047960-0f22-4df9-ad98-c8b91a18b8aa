import { Flex } from '@mantine/core';
import ForgotPasswordForm from 'components/ForgotPasswordForm';
import Layout from 'components/Layout';
import React from 'react';

const ForgotPasswordPage = () => {
  return (
    <Flex
      align={'center'}
      h={'100%'}
      justify={'center'}
      px={{ base: 16, sm: 0 }}
      py={{ base: 84, sm: 0 }}
    >
      <ForgotPasswordForm />
    </Flex>
  );
};

ForgotPasswordPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default ForgotPasswordPage;
