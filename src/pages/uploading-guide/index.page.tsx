import { Alert, Box, List, Text } from '@mantine/core';
import StaticPageLayout from 'components/Home/StaticPageLayout';
import Layout from 'components/Layout';
import Image from 'next/image';
import { NextSeo } from 'next-seo';
import React from 'react';

const UploadGuidePage = () => {
  return (
    <>
      <NextSeo
        openGraph={{
          title: '確認書類の撮影・アップロードガイド | テクノワ',
        }}
        title="確認書類の撮影・アップロードガイド | テクノワ"
      />
      <StaticPageLayout
        subtitle="Shooting and uploading guide"
        title={`確認書類の撮影・アップロードガイド`}
      >
        <Box
          styles={{
            '*': {
              fontSize: '16px ',
            },
          }}
        >
          <>
            <Text fz={18} mb="md" mt="xl" weight={700}>
              撮影時の注意点
            </Text>
            <Text fz={16} mb="sm">
              確認書類の一部が隠れないように、全体が入るように撮影してください。
              <br />
              以下の状態で提出された場合は、「不備」として差戻しとさせていただきますので、ご注意ください。
            </Text>
            <List ml={8}>
              <List.Item>書類の一部が折れいている</List.Item>
              <List.Item>光の反射や影で見えない</List.Item>
              <List.Item>ピントがあっていない、不鮮明</List.Item>
              <List.Item>書類の文字が読み取れない</List.Item>
              <List.Item>書類の有効期限が切れている</List.Item>
            </List>

            <Alert color="orange" mt="md" p={24} title="">
              <Text c="black" fw={700} mb="sm">
                注意
              </Text>
              <List>
                <List.Item>
                  各書類の撮影は、表面、裏面の撮影が必要です。いずれかに不備があると審査が通りません。
                </List.Item>
                <List.Item>
                  現在有効な原本に限ります。コピーした本人確認書類での申請は無効になります。
                </List.Item>
              </List>
            </Alert>

            <Text fz={18} mb="md" mt="40px" weight={700}>
              書類撮影のポイント
            </Text>
            <Text mb={24}>
              撮影にあたっては、逆光やカメラの角度などにご注意ください。
            </Text>
            <Text fw={700} fz={16}>
              Point 1 暗くなく、光が反射しない場所で撮影してください
            </Text>
            <Text fz={16} mb={24}>
              周囲が暗い場合や光が反射して一部が白飛びしている場合、画像認識がされません。画像認識されても、審査時にテキストが読めない場合は確認ができません。
            </Text>
            <Box
              mb={24}
              px={64}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.smallerThan('sm')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={500}
                src="/images/photo-guide-1.png"
                style={{
                  maxWidth: '100%',
                }}
                width={700}
              />
            </Box>
            <Box
              mb={24}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.largerThan('md')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={207}
                src="/images/photo-guide-1-mb.png"
                width={311}
              />
            </Box>
            <Box
              mb={24}
              px={64}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.smallerThan('sm')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={500}
                src="/images/photo-guide-2.png"
                style={{
                  maxWidth: '100%',
                }}
                width={700}
              />
            </Box>
            <Box
              mb={24}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.largerThan('md')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={207}
                src="/images/photo-guide-2-mb.png"
                width={311}
              />
            </Box>
            <Text fw={700} fz={16} mb={8}>
              Point 2 確認書類がすべて見えるように撮影してください
            </Text>
            <List mb={24} ml={12}>
              <List.Item>
                書類の全体を写してください。見切れていると、差戻しの対象となります。
              </List.Item>
              <List.Item>
                スマートフォンで撮影する場合、確認書類を机や台などの平らな場所に置いて撮影してください
              </List.Item>
            </List>
            <Box
              mb={24}
              px={64}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.smallerThan('sm')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={500}
                src="/images/photo-guide-3.png"
                style={{
                  maxWidth: '100%',
                }}
                width={700}
              />
            </Box>
            <Box
              mb={24}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.largerThan('md')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={207}
                src="/images/photo-guide-3-mb.png"
                width={311}
              />
            </Box>
            <Text fw={700} fz={16} mb={8}>
              Point 3 本人確認書類にピントを合わせて撮影してください
            </Text>
            <Text mb={24}>
              ぼやけていて不鮮明な場合、差戻しの対象となります。
            </Text>
            <Box
              mb={24}
              px={64}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.smallerThan('sm')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={500}
                src="/images/photo-guide-4.png"
                style={{
                  maxWidth: '100%',
                }}
                width={700}
              />
            </Box>
            <Box
              mb={24}
              sx={(theme) => ({
                display: 'block',
                [theme.fn.largerThan('md')]: {
                  display: 'none',
                },
              })}
            >
              <Image
                alt="guide-1"
                height={207}
                src="/images/photo-guide-4-mb.png"
                width={311}
              />
            </Box>
            <Text fw={700} fz={16} mb={8}>
              Point 4
              書類を手に持って撮影する場合、指などで情報を隠さないようにしてください
            </Text>
            <List icon="※" mb={24} ml={12}>
              <List.Item>
                以上の撮影ガイドに沿って、裏表両面撮影してください
              </List.Item>
              <List.Item>
                健康診断書が3ページ以上にわたる場合は、2ページ目以降のすべてのページを［裏面］としてアップロードしてください。
              </List.Item>
              <List.Item>撮影が完了したら画像を確認してください</List.Item>
              <List.Item>
                本人確認書類の文字が鮮明に映っているか、読み取れるかを確認してください。鮮明に映っていない場合は再撮影をしてください。
              </List.Item>
            </List>
          </>
        </Box>
      </StaticPageLayout>
    </>
  );
};

UploadGuidePage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default UploadGuidePage;
