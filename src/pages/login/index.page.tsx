import { yupResolver } from '@hookform/resolvers/yup';
import IconEmail from '@icons/icon-email.svg';
import IconPassword from '@icons/icon-password.svg';
import { Box, Button, Card, Flex, Text, Title } from '@mantine/core';
import { PasswordField, TextField } from 'components/Form';
import Layout from 'components/Layout';
import { useGlobalState, useMutate } from 'hooks';
import { useAsPath } from 'hooks/useAsPath';
import authQuery from 'models/auth';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import helpers from 'utils/helpers';
import type { WebToken } from 'utils/type';

import type { LoginFormValues } from './schema';
import schema from './schema';

const LoginPage = () => {
  const { replace, query } = useRouter();
  const { prevAsPath } = useAsPath();
  const { authChannel } = useGlobalState();

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<LoginFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  const { mutateAsync: login, isLoading } = useMutate<
    LoginFormValues,
    WebToken
  >({
    ...authQuery.login,
    onSuccess: () => {
      helpers.logEventTracking('login');
    },
  });

  const onSubmit: SubmitHandler<LoginFormValues> = async (values) => {
    await login(values);
    if (authChannel) {
      authChannel.postMessage('login');
    }
    // Prev path can be other route except set password page
    const prevPath =
      prevAsPath &&
      !['/set-password', '/register', '/forget-password'].some((path) =>
        prevAsPath.startsWith(path),
      )
        ? prevAsPath
        : '/';
    replace(typeof query.referer === 'string' ? query.referer : prevPath);
  };

  return (
    <Flex align="center" h="100%" justify="center" px={16} py={64}>
      <Card
        component="form"
        display="flex"
        maw={424}
        onSubmit={handleSubmit(onSubmit)}
        px={{ base: 15, sm: 31 }}
        py={{ base: 32, sm: 40 }}
        sx={{ flexDirection: 'column' }}
        w="100%"
      >
        <Title
          color="black"
          fz={{ base: 24, sm: 32 }}
          lh={{ base: '34px', sm: '46px' }}
          mb={{ base: 24, sm: 32 }}
          ta="center"
        >
          ログイン
        </Title>
        <TextField
          control={control}
          icon={<IconEmail color="#9CA3AF" height={24} width={24} />}
          label="メールアドレス"
          mb={24}
          name="email"
          placeholder="メールアドレスを入力してください"
        />
        <PasswordField
          control={control}
          icon={<IconPassword height={24} width={24} />}
          label="パスワード"
          mb={32}
          name="password"
          placeholder="パスワードを入力してください"
        />
        <Button disabled={!isValid} fullWidth loading={isLoading} type="submit">
          ログインする
        </Button>
        <Box mb={8} mt={{ base: 24, sm: 32 }} ta="center">
          <Text
            color="info-500"
            component={Link}
            fw={400}
            fz={16}
            href="/forgot-password"
            lh="24px"
          >
            パスワードを忘れた方はこちら
          </Text>
        </Box>
        <Box ta="center">
          <Text
            color="info-500"
            component={Link}
            fw={400}
            fz={16}
            href="/register"
            lh="24px"
          >
            会員登録はこちら
          </Text>
        </Box>
      </Card>
    </Flex>
  );
};

LoginPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default LoginPage;
