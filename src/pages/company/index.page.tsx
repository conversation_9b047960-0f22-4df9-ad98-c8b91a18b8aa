import { Box, Text } from '@mantine/core';
import StaticPageLayout from 'components/Home/StaticPageLayout';
import Layout from 'components/Layout';
import { NextSeo } from 'next-seo';
import React from 'react';

import { DATA } from './data';

const CompanyPage = () => {
  return (
    <>
      <NextSeo
        openGraph={{
          title: '運営会社 | テクノワ',
        }}
        title="運営会社 | テクノワ"
      />
      <StaticPageLayout subtitle="company information" title="運営会社">
        <Box
          sx={(theme) => ({
            display: 'table',
            width: '100%',
            '*': {
              fontSize: 16,
              [theme.fn.smallerThan('sm')]: {
                fontSize: 14,
              },
            },
            '> *': {
              display: 'table-row',
              padding: 24,
              [theme.fn.smallerThan('sm')]: {
                paddingLeft: 0,
                paddingRight: 0,
              },
              '&:not(:last-child)': {
                borderBottom: `1px solid ${theme.colors['gray-300']}`,
                '> *': {
                  borderBottom: `1px solid ${theme.colors['gray-300']}`,
                },
              },
              '> *': {
                display: 'table-cell',
                padding: 24,
                wordBreak: 'break-word',
                [theme.fn.smallerThan('sm')]: {
                  minWidth: 98,
                  paddingLeft: 0,
                  paddingRight: 0,
                  '&:last-child': {
                    paddingLeft: 16,
                  },
                },
              },
            },
          })}
        >
          {Object.entries(DATA).map((entry, i) => (
            <Box key={i}>
              <Text fw={700}>{entry[0]}</Text>
              <Text>{entry[1]}</Text>
            </Box>
          ))}
        </Box>
      </StaticPageLayout>
    </>
  );
};

CompanyPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default CompanyPage;
