import { withSessionRoute } from 'lib/withSession';
import type { NextApiRequest, NextApiResponse } from 'next';
import api from 'utils/api';
import helpers from 'utils/helpers';
import type { IError } from 'utils/type';

async function logoutRoute(req: NextApiRequest, res: NextApiResponse) {
  try {
    const webCookie = helpers.getWebCookie(req, res);
    const response = await api({
      method: 'delete',
      url: '/auth/admin/signout',
      data: {
        refreshToken: webCookie?.refreshToken,
      },
    });
    req.session.destroy();
    res.send(response);
  } catch (e) {
    const { statusCode, ...rest } = e as IError;
    res.status(statusCode || 404).send({ statusCode, ...rest });
  }
}
export default withSessionRoute(logoutRoute);
