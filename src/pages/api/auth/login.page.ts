import { withSessionRoute } from 'lib/withSession';
import type { NextApiRequest, NextApiResponse } from 'next';
import type { LoginFormValues } from 'pages/login/schema';
import api from 'utils/api';
import helpers from 'utils/helpers';
import type { IError, WebToken } from 'utils/type';

async function loginRoute(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { body } = req;

    const response = await api<LoginFormValues, { data: WebToken }>({
      method: 'post',
      url: '/auth/worker/signin',
      data: body,
    });
    helpers.setToken({ ...response.data }, { req, res });
    const parseToken = helpers.parseJwt(response.data.accessToken)!;

    req.session.user = {
      _id: parseToken._id,
      email: parseToken.email,
      role: parseToken.role,
    };

    await req.session.save();
    res.send(response);
  } catch (e) {
    const { statusCode, ...rest } = e as IError;
    res.status(statusCode || 404).send({ statusCode, ...rest });
  }
}
export default withSessionRoute(loginRoute);
