import { withSessionRoute } from 'lib/withSession';
import type { NextApiRequest, NextApiResponse } from 'next';
import api from 'utils/api';
import helpers from 'utils/helpers';
import type { IError } from 'utils/type';

async function userProfileRoute(req: NextApiRequest, res: NextApiResponse) {
  try {
    const userSession = req.session.user;

    const response = await api({
      method: 'get',
      url: '/workers/profile',
      ...helpers.getTokenConfig(req, res),
    });

    req.session.user = {
      ...userSession,
      isCompletedProfile: response.data.isCompletedProfile,
    };

    await req.session.save();
    res.send(response);
  } catch (e) {
    const { statusCode, ...rest } = e as IError;
    res.status(statusCode || 404).send({ statusCode, ...rest });
  }
}
export default withSessionRoute(userProfileRoute);
