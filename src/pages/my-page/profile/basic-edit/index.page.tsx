import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Flex, Text } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import CardLayout from 'components/CardLayout';
import Layout from 'components/Layout';
import BasicInfomationForm from 'components/Profile/BasicInfomationForm';
import type { BasicProfileFormValues } from 'components/Profile/BasicInfomationForm/schema';
import schema from 'components/Profile/BasicInfomationForm/schema';
import { useMutate, useUser } from 'hooks';
import authQuery from 'models/auth';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import helpers from 'utils/helpers';

const EditBasicInfoPage = () => {
  const { push } = useRouter();
  const {
    data: currentUser,
    refetch,
    isLoading: isLoadingUserData,
  } = useUser();

  const { mutateAsync: updateBasicInfo, isLoading } = useMutate({
    ...authQuery.updateBasicInfo,
    successMessage: '	完了しました。',
  });

  const defaultValues: BasicProfileFormValues = useMemo(
    () => ({
      avatarKey: currentUser?.avatar ? [currentUser.avatar] : [],
      firstName: currentUser?.firstName || '',
      lastName: currentUser?.lastName || '',
      firstNameKata: currentUser?.firstNameKata || '',
      lastNameKata: currentUser?.lastNameKata || '',
      phone: currentUser?.phone || '',
    }),
    [currentUser],
  );

  const methods = useForm<BasicProfileFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
    defaultValues: {
      avatarKey: currentUser?.avatar ? [currentUser.avatar] : [],
      firstName: currentUser?.firstName || '',
      lastName: currentUser?.lastName || '',
      firstNameKata: currentUser?.firstNameKata || '',
      lastNameKata: currentUser?.lastNameKata || '',
      phone: currentUser?.phone || '',
    },
  });

  const { isValid, isDirty } = methods.formState;

  useEffect(() => {
    if (currentUser) {
      methods.reset(defaultValues);
    }
  }, [currentUser, defaultValues, methods]);

  const handleOnCancel = () => {
    if (methods.formState.isDirty) {
      helpers.confirm({
        title: '入力内容を破棄',
        children: (
          <Text color="black" fz="16px" lh="24px" p={24}>
            入力内容を破棄しますか？
          </Text>
        ),
        onConfirm: () => {
          push('/my-page/profile');
        },
      });
    } else {
      push('/my-page/profile');
    }
  };

  const handleOnSubmit: SubmitHandler<BasicProfileFormValues> = async ({
    avatarKey,
    ...values
  }) => {
    await updateBasicInfo({
      ...values,
      avatarKey: avatarKey?.length ? avatarKey[0].key : null,
    });
    await refetch();
    push('/my-page/profile');
  };

  return (
    <FormProvider {...methods}>
      <Box pb={{ base: 0, sm: 64 }} pos="relative" pt={{ base: 24, sm: 32 }}>
        <CardLayout
          cancelButtonProps={{
            onClick: handleOnCancel,
          }}
          footerProps={{
            sx: (theme) => ({
              borderTop: `1px solid ${theme.colors['gray-300']}`,
              [theme.fn.smallerThan('sm')]: {
                display: 'none',
              },
            }),
          }}
          isLoading={isLoadingUserData}
          submitButtonProps={{
            onClick: methods.handleSubmit(handleOnSubmit),
            loading: isLoading,
            disabled: isLoadingUserData || !isDirty || !isValid,
          }}
          title={'基本情報'}
        >
          <BasicInfomationForm />
        </CardLayout>
        <Flex
          bg="white"
          gap={8}
          justify="flex-end"
          mt={42}
          px={16}
          py={12}
          sx={(theme) => ({
            boxShadow: theme.shadows.sm,
            position: 'sticky',
            bottom: 0,
            gap: 8,
            '& > *': {
              flex: 1,
            },
            [theme.fn.largerThan('sm')]: {
              display: 'none',
            },
          })}
        >
          <Button miw={100} onClick={handleOnCancel} variant="outline">
            キャンセル
          </Button>
          <Button
            disabled={isLoadingUserData || !isDirty || !isValid}
            loading={isLoading}
            miw={100}
            onClick={methods.handleSubmit(handleOnSubmit)}
          >
            保存する
          </Button>
        </Flex>
      </Box>
    </FormProvider>
  );
};

EditBasicInfoPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default EditBasicInfoPage;
