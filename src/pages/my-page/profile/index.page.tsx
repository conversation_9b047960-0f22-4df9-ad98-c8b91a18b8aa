import type { MantineTheme } from '@mantine/core';
import { Flex, rem } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import Layout from 'components/Layout';
import React from 'react';

import BasicProfile from './BasicProfile';
import DetailProfile from './DetailProfile';

const ProfilePage = () => {
  return (
    <Flex
      align="center"
      direction={'column'}
      gap={24}
      justify="center"
      mb={64}
      pt={32}
      sx={(theme: MantineTheme) => ({
        fontSize: theme.fontSizes.md,
        [theme.fn.smallerThan('sm')]: {
          paddingTop: '24px',
          marginBottom: '40px',
          gap: rem(16),
          fontSize: theme.fontSizes.sm,
        },
      })}
    >
      <BasicProfile />
      <DetailProfile />
    </Flex>
  );
};

ProfilePage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default ProfilePage;
