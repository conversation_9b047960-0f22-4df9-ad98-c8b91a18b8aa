import IconEdit from '@icons/icon-edit.svg';
import IconEmail from '@icons/icon-email.svg';
import IconTel from '@icons/icon-tel.svg';
import type { MantineTheme } from '@mantine/core';
import { ActionIcon, Card, Flex, Text } from '@mantine/core';
import { useUser } from 'hooks';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const BasicProfile = () => {
  const { data: userData } = useUser();
  return (
    <Card
      display="flex"
      maw={784}
      mx={'auto'}
      pb={{ base: 18, sm: 32 }}
      pt={{ base: 32, sm: 40 }}
      sx={(theme: MantineTheme) => ({
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        [theme.fn.smallerThan('sm')]: {
          borderRadius: 0,
        },
      })}
      w="100%"
    >
      <Flex align="center" direction={'column'} justify="center" mx={'auto'}>
        <Image
          alt="user-avatar"
          height={120}
          src={userData?.avatar?.originUrl || '/images/default-avatar.png'}
          style={{ borderRadius: '50%' }}
          width={120}
        />
        <Flex
          align={'center'}
          direction={'column'}
          gap={{ base: 4, sm: 8 }}
          justify={'center'}
          mb={{ base: 12, sm: 16 }}
          mt={16}
        >
          <Text
            color="black"
            size={'24px'}
            sx={(theme: MantineTheme) => ({
              [theme.fn.smallerThan('sm')]: {
                fontSize: '18px',
              },
            })}
            weight={700}
          >
            {userData?.fullName}
          </Text>
          <Text
            color="gray-original"
            size={'16px'}
            sx={(theme: MantineTheme) => ({
              [theme.fn.smallerThan('sm')]: {
                fontSize: '14px',
              },
            })}
          >
            {userData?.fullNameKata}
          </Text>
        </Flex>
        <Flex direction={'column'} gap={4}>
          <Flex
            align={'center'}
            direction={'row'}
            gap={{ base: 4, sm: 8 }}
            justify={'center'}
          >
            <IconEmail color="#4B5563" height={24} width={24} />
            <Text
              color="gray-700"
              h={24}
              size={'16px'}
              sx={(theme: MantineTheme) => ({
                lineHeight: '24px',
                [theme.fn.smallerThan('sm')]: {
                  fontSize: '14px',
                },
              })}
            >
              {userData?.email}
            </Text>
          </Flex>
          {userData?.phone && (
            <Flex
              align={'center'}
              direction={'row'}
              gap={{ base: 4, sm: 8 }}
              justify={'center'}
            >
              <IconTel height={24} width={24} />
              <Text
                color="gray-700"
                h={24}
                size={'16px'}
                sx={(theme: MantineTheme) => ({
                  lineHeight: '24px',
                  [theme.fn.smallerThan('sm')]: {
                    fontSize: '14px',
                  },
                })}
              >
                {userData.phone}
              </Text>
            </Flex>
          )}
        </Flex>
      </Flex>
      <ActionIcon
        component={Link}
        href="/my-page/profile/basic-edit"
        size="sm"
        sx={(theme: MantineTheme) => ({
          position: 'absolute',
          top: '16px',
          right: '24px',
          [theme.fn.smallerThan('sm')]: {
            right: '14px',
          },
        })}
        variant="subtle"
      >
        <IconEdit height={32} width={32} />
      </ActionIcon>
    </Card>
  );
};
export default BasicProfile;
