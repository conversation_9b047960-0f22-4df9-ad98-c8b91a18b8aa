import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Flex, Text } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import CardLayout from 'components/CardLayout';
import Layout from 'components/Layout';
import WorkerProfileForm from 'components/Profile/WorkerProfileForm';
import type { WorkerProfileFormValues } from 'components/Profile/WorkerProfileForm/schema';
import schema from 'components/Profile/WorkerProfileForm/schema';
import { useFetch, useMutate, useUser } from 'hooks';
import { omit } from 'lodash';
import authQuery from 'models/auth';
import {
  type IWorkerProfileRequest,
  NoNationalCertification,
  NoRequired,
} from 'models/auth/type';
import jobQuery from 'models/job';
import type { IJobType } from 'models/job/type';
import resourceQuery from 'models/resource';
import type { IPrefecture, ISelectableOption } from 'models/resource/type';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import { COMPANY_PROPS, WorkerType } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

const WorkerProfileEditPage = () => {
  const { data: userData, refetch, isLoading: isFetchingUser } = useUser();
  const { push } = useRouter();

  const defaultValues = useMemo(() => {
    const noConstructionCertifications = !helpers.checkCertificationRequired(
      userData?.constructionCertifications,
    );
    const noInsurances = !helpers.checkCertificationRequired(
      userData?.insurances,
    );
    const noNationalCertification = !helpers.checkCertificationRequired(
      userData?.nationalCertifications,
    );

    return {
      companyName: userData?.companyName || '',
      companyPhone: userData?.companyPhone || '',
      tradeName: userData?.tradeName || '',
      postCode: userData?.postCode || '',
      city: userData?.city || '',
      prefecture: userData?.prefecture || '',
      building: userData?.building || '',
      district: userData?.district || '',
      type: userData?.type
        ? WorkerType[userData?.type]
        : WorkerType.COMPANY_WORKER,
      birthDate: dayjs(userData?.birthDate).toDate(),
      jobTypes: userData?.jobTypes?.map((job) => job._id) || [],
      nationalCertifications: noNationalCertification
        ? undefined
        : (userData?.nationalCertifications as WorkerProfileFormValues['nationalCertifications']),
      hasConstructionCertifications: noConstructionCertifications
        ? 'false'
        : 'true',
      hasInsurances: noInsurances ? 'false' : 'true',
      hasNationalCertifications: noNationalCertification ? 'false' : 'true',
      constructionCertifications: noConstructionCertifications
        ? undefined
        : (userData?.constructionCertifications as WorkerProfileFormValues['constructionCertifications']),
      insurances: noInsurances
        ? undefined
        : (userData?.insurances as WorkerProfileFormValues['insurances']),
      selfIntroduction: userData?.selfIntroduction,
    } satisfies WorkerProfileFormValues;
  }, [userData]);

  const methods = useForm<WorkerProfileFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  useEffect(() => {
    if (userData) {
      methods.reset(defaultValues);
    }
  }, [methods, userData, defaultValues]);

  const { data: jobTypes } = useFetch<
    { jobTypes: IJobType[] },
    ISelectableOption[]
  >({
    ...jobQuery.getJobTypes,
    select: (data) =>
      data.jobTypes.map((item) => ({ label: item.name, value: item._id })),
  });

  const { data: prefectureOptions } = useFetch<
    IPrefecture[],
    ISelectableOption[]
  >({
    ...resourceQuery.prefectures,
    select: (data) =>
      data.map((item) => ({
        label: item.prefecture_kanji,
        value: item.prefecture_kanji,
      })),
  });

  const { mutateAsync: onSetWorkerProfile, isLoading: isSubmitting } =
    useMutate<IWorkerProfileRequest>({
      ...authQuery.updateWorkerInfo,
    });

  const handleCancel = () => {
    if (methods.formState.isDirty) {
      helpers.confirm({
        title: '入力内容を破棄',
        children: (
          <Text color="black" fz="16px" lh="24px" p={24}>
            入力内容を破棄しますか？
          </Text>
        ),
        onConfirm: () => {
          push('/my-page/profile');
        },
      });
    } else {
      push('/my-page/profile');
    }
  };

  const onSubmit: SubmitHandler<WorkerProfileFormValues> = async (values) => {
    const params: IWorkerProfileRequest = {
      ...omit(values, [
        'certificationDocuments',
        'cvDocuments',
        'jobTypes',
        'birthDate',
        'type',
        'hasConstructionCertifications',
        'constructionCertifications',
        'hasInsurances',
        'insurances',
        'hasNationalCertifications',
        'nationalCertifications',
        'applicationDocuments',
        ...(values.type === WorkerType.COMPANY_WORKER ? [] : COMPANY_PROPS),
      ]),
      type: values.type || WorkerType.COMPANY_WORKER,
      jobTypes: values.jobTypes.map((j) => j || '') || [],
      cvDocumentKeys: [],
      applicationDocumentKeys: [],
      constructionCertifications:
        values.hasConstructionCertifications === 'true' &&
        values.type === WorkerType.COMPANY_WORKER
          ? values.constructionCertifications
          : [NoRequired],
      nationalCertifications:
        values.hasNationalCertifications === 'true'
          ? values.nationalCertifications
          : [NoNationalCertification],
      insurances:
        values.hasInsurances === 'true' ? values.insurances : [NoRequired],
      birthDate: values.birthDate.toISOString(),
    };

    await onSetWorkerProfile(params);
    await refetch();
    push('/my-page/profile');
  };

  return (
    <FormProvider {...methods}>
      <Box pb={{ base: 0, sm: 64 }} pos="relative" pt={{ base: 24, sm: 32 }}>
        <CardLayout
          cancelButtonProps={{ onClick: handleCancel }}
          footerProps={{
            sx: (theme) => ({
              borderTop: `1px solid ${theme.colors['gray-300']}`,
              [theme.fn.smallerThan('sm')]: {
                display: 'none',
              },
            }),
          }}
          isLoading={isFetchingUser}
          submitButtonProps={{
            onClick: methods.handleSubmit(onSubmit),
            loading: isSubmitting,
            disabled:
              !methods.formState.isValid ||
              !methods.formState.isDirty ||
              isFetchingUser,
          }}
          title="その他の情報"
        >
          <WorkerProfileForm
            jobTypeOptions={jobTypes}
            prefectureOptions={prefectureOptions}
          />
        </CardLayout>
        <Flex
          bg="white"
          gap={8}
          justify="flex-end"
          mt={42}
          px={16}
          py={12}
          sx={(theme) => ({
            boxShadow: theme.shadows.sm,
            position: 'sticky',
            bottom: 0,
            gap: 8,
            '& > *': {
              flex: 1,
            },
            [theme.fn.largerThan('sm')]: {
              display: 'none',
            },
          })}
        >
          <Button miw={100} onClick={handleCancel} variant="outline">
            キャンセル
          </Button>
          <Button
            disabled={
              isFetchingUser ||
              !methods.formState.isDirty ||
              !methods.formState.isValid
            }
            loading={isSubmitting}
            miw={100}
            onClick={methods.handleSubmit(onSubmit)}
          >
            保存する
          </Button>
        </Flex>
      </Box>
    </FormProvider>
  );
};

WorkerProfileEditPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default WorkerProfileEditPage;
