import IconEdit from '@icons/icon-edit.svg';
import type { MantineTheme } from '@mantine/core';
import {
  ActionIcon,
  Badge,
  Box,
  Card,
  Divider,
  Flex,
  Grid,
  Group,
  Stack,
  Text,
} from '@mantine/core';
import { useUser } from 'hooks';
import { get } from 'lodash';
import type { IWorker } from 'models/auth/type';
import Link from 'next/link';
import React from 'react';
import { PatternFormat } from 'react-number-format';
import {
  NationalCertificationsLabel,
  WorkerType,
  WorkerTypeLabel,
} from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

const OtherInfo = ({ worker }: { worker?: IWorker }) => (
  <Stack
    spacing={'24px'}
    sx={(theme: MantineTheme) => ({
      [theme.fn.smallerThan('sm')]: { gap: '16px' },
    })}
    w={'100%'}
  >
    {[
      {
        label: '生年月日',
        dataIndex: 'birthDate',
        render: () =>
          dayjs(worker?.birthDate ?? undefined).format('YYYY年MM月DD日'),
      } as const,
      {
        label: '職種',
        dataIndex: 'jobTypes.name',
        render: () => {
          return (
            <Flex gap={8} wrap="wrap">
              {worker?.jobTypes?.length &&
                worker.jobTypes.map((job, index) => (
                  <Badge color={'neutral-50'} key={index}>
                    <Text color="primary" size={14} weight={400}>
                      {job.name}
                    </Text>
                  </Badge>
                ))}
            </Flex>
          );
        },
      } as const,
      {
        label: '建築システムの会員状況',
        dataIndex: 'nationalCertifications',
        render: () => (
          <Flex gap={8} wrap="wrap">
            {worker?.nationalCertifications?.length &&
            helpers.checkCertificationRequired(worker?.nationalCertifications)
              ? worker.nationalCertifications.map((cert, index) => (
                  <Badge color={'neutral-50'} key={index}>
                    <Text color="primary" size={14} weight={400}>
                      {NationalCertificationsLabel[cert]}
                    </Text>
                  </Badge>
                ))
              : '---'}
          </Flex>
        ),
      } as const,
      {
        label: '建設業許可証の有無',
        dataIndex: '',
        render: () =>
          worker?.type === WorkerType.COMPANY_WORKER ? (
            <Flex gap={8} wrap="wrap">
              {worker?.constructionCertifications?.length &&
              helpers.checkCertificationRequired(
                worker?.constructionCertifications,
              )
                ? worker.constructionCertifications.map((cert, index) => (
                    <Badge color={'neutral-50'} key={index}>
                      <Text color="primary" size={14} weight={400}>
                        {cert}
                      </Text>
                    </Badge>
                  ))
                : '---'}
            </Flex>
          ) : undefined,
      } as const,
      {
        label: '社会保険の有無',
        dataIndex: '',
        render: () => (
          <Flex gap={8} wrap="wrap">
            {worker?.insurances?.length &&
            helpers.checkCertificationRequired(worker?.insurances)
              ? worker.insurances.map((cert, index) => (
                  <Badge color={'neutral-50'} key={index}>
                    <Text color="primary" size={14} weight={400}>
                      {cert}
                    </Text>
                  </Badge>
                ))
              : '---'}
          </Flex>
        ),
      } as const,
      {
        label: '自己紹介',
        dataIndex: 'selfIntroduction',
        render: () => (
          <Text ml={8} size={14} weight={400}>
            {worker?.selfIntroduction}
          </Text>
        ),
      } as const,
    ].map((item, index) => {
      const content = item.render?.() ?? get(worker, item.dataIndex);
      if (!content) {
        return null;
      }

      return (
        <Box
          key={index}
          sx={(theme) => ({
            color: theme.colors.black,
          })}
        >
          <Text color="gray-600" size={'sm'} weight={700}>
            {item.label}
          </Text>
          {content}
        </Box>
      );
    })}
  </Stack>
);

const ProfileInformation = ({ worker }: { worker?: IWorker }) => (
  <Stack mt={16} w={'100%'}>
    {[
      ...(worker?.type === WorkerType.COMPANY_WORKER
        ? [
            {
              label: '会社名',
              dataIndex: 'companyName',
            },
            {
              label: '電話番号',
              dataIndex: 'companyPhone',
            },
          ]
        : [
            {
              label: '屋号',
              dataIndex: 'tradeName',
            },
          ]),
      {
        label: '郵便番号',
        dataIndex: 'postCode',
        render: () => (
          <PatternFormat
            displayType="text"
            format={'〒 ###-####'}
            value={worker?.postCode}
          />
        ),
      },
      {
        label: '都道府県',
        dataIndex: 'prefecture',
        value: 'Prefecture',
      },
      {
        label: '市区町村',
        dataIndex: 'city',
        value: 'Cities',
      },
      {
        label: '町名・番地',
        dataIndex: 'district',
        value: 'District',
      },
      {
        label: '建物名・部屋番号',
        dataIndex: 'building',
        value: 'Building name and room number',
      },
    ].map((item, index) => {
      const content =
        item.render?.() ??
        (item.dataIndex && get(worker, item.dataIndex, item.value));
      if (!content) {
        return null;
      }
      return (
        <Grid
          gutter={0}
          key={index}
          sx={(theme) => ({
            color: theme.colors.black,
          })}
        >
          <Grid.Col sm={3} xs={12}>
            <Text color="gray-600" size={'sm'} weight={700}>
              {item.label}
            </Text>
          </Grid.Col>
          <Grid.Col sm={9} xs={12}>
            {content}
          </Grid.Col>
        </Grid>
      );
    })}
  </Stack>
);

const DetailProfile = () => {
  const { data: userData } = useUser<IWorker>();
  return (
    <Card
      display="flex"
      h={'100%'}
      maw={784}
      mx={'auto'}
      pb={32}
      sx={(theme: MantineTheme) => ({
        flexDirection: 'column',
        [theme.fn.smallerThan('sm')]: {
          borderRadius: 0,
        },
      })}
      w="100%"
    >
      <Group pb={0} position="apart" pt={16} px={{ base: 16, sm: 24 }}>
        <Group position="right">
          <Text color="black" size={'lg'} weight={700}>
            {'その他の情報'}
          </Text>
          {userData?.type && (
            <Badge
              className={'rounded'}
              color={
                userData.type === 'COMPANY_WORKER'
                  ? 'aqua-squeeze'
                  : 'lily-white'
              }
            >
              <Text
                color={
                  userData.type === 'COMPANY_WORKER' ? 'keppel' : 'dodger-blue'
                }
                weight={500}
              >
                {WorkerTypeLabel[userData.type]}
              </Text>
            </Badge>
          )}
        </Group>
        <ActionIcon
          color="primary"
          component={Link}
          href="/my-page/profile/worker-edit"
          size="sm"
          variant="subtle"
        >
          <IconEdit height={32} width={32} />
        </ActionIcon>
      </Group>

      <Divider color="gray-300" my={16} />

      <Flex
        align={'center'}
        direction={'column'}
        h="100%"
        justify={'center'}
        mx={'auto'}
        px={24}
        w="100%"
      >
        <ProfileInformation worker={userData} />
        <Divider
          color="gray-300"
          my={'32px'}
          sx={(theme: MantineTheme) => ({
            [theme.fn.smallerThan('sm')]: {
              marginTop: '24px',
              marginBottom: '24px',
            },
          })}
          w="100%"
        />

        <OtherInfo worker={userData} />
      </Flex>
    </Card>
  );
};
export default DetailProfile;
