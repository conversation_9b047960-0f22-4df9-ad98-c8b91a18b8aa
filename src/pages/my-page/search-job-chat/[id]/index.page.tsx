import IconEmptyMessage from '@icons/icon-empty-messages.svg';
import IconReset from '@icons/icon-reset.svg';
import { ActionIcon, Box, Flex, LoadingOverlay, Text } from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import { useMutation, useQuery } from '@tanstack/react-query';
import Layout from 'components/Layout';
import { MessageList } from 'components/Messages';
import type { TSendMessagePayload } from 'components/Messages/MessageActions';
import MessageActions from 'components/Messages/MessageActions';
import Utils from 'components/Messages/utils';
import { collection, doc, getDoc } from 'firebase/firestore';
import { useUser } from 'hooks';
import debounce from 'lodash/debounce';
import type { IUser } from 'models/auth/type';
import { chatService } from 'models/chat/chatService';
import type { IMessageAttachment, IRoom } from 'models/chat/type';
import { useRouter } from 'next/router';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { db } from 'utils/firebase';
import helpers from 'utils/helpers';

const ResetChatRoomButton = () => {
  const router = useRouter();

  const handleReset = () => {
    helpers.confirm({
      title: 'チャットをリセット',
      children: (
        <Text px={24} py={12}>
          チャット履歴をリセットしますか？この操作は元に戻せません。
        </Text>
      ),
      onConfirm: () => {
        router.push('/my-page/search-job-chat');
      },
    });
  };

  return (
    <ActionIcon
      color="error"
      onClick={handleReset}
      size="lg"
      sx={() => ({
        position: 'absolute',
        right: 24,
        top: 24,
        zIndex: 10,
        boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
        opacity: 0.7,
        transition: 'all 0.2s ease',
        '&:hover': {
          opacity: 1,
          transform: 'translateY(-2px)',
          boxShadow: '0 6px 12px rgba(0, 0, 0, 0.25)',
        },
      })}
      variant="filled"
    >
      <IconReset height={24} width={24} />
    </ActionIcon>
  );
};

const ChatRoomPage = () => {
  const router = useRouter();
  const { id: chatRoomId } = router.query;
  const { data: userData } = useUser();
  const firebaseUserId = userData?.firebaseUserId;
  const hasMessageRef = useRef<boolean>(true);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (
      chatRoomId &&
      typeof chatRoomId === 'string' &&
      chatRoomId.startsWith('ai_')
    ) {
      // Store the room ID in localStorage for future use
      localStorage.setItem('searchJobChatRoomId', chatRoomId);
      setIsLoading(false);
    }
  }, [chatRoomId]);

  const { data: firebaseMembersInfo } = useQuery<unknown, unknown, IUser[]>({
    queryKey: ['users', 'firebase', chatRoomId],
    refetchOnReconnect: false,
    queryFn: () => chatService.getMembersInfo(chatRoomId as string),
    enabled: !!chatRoomId && !!firebaseUserId,
  });

  const { data: unsubscribe, remove } = useQuery({
    enabled: !!chatRoomId && !!firebaseUserId,
    refetchOnReconnect: true,
    queryFn: async () => {
      return chatService.attachRoomListener(chatRoomId as string);
    },
  });

  useEffect(() => {
    return () => {
      if (unsubscribe) {
        unsubscribe();
        remove();
      }
    };
  }, [remove, unsubscribe]);

  const membersMap = firebaseMembersInfo?.reduce<Record<string, IUser>>(
    (acc, v) => ({ ...acc, [v.id || '']: v }),
    {},
  );

  const { mutateAsync: sendMessage, isLoading: isSendingMessage } = useMutation<
    unknown,
    unknown,
    {
      value: string;
      roomId: string;
      senderId: string;
      attachments: IMessageAttachment[];
    }
  >({
    mutationFn: (payload) =>
      chatService.sendMessage({
        ...payload,
      }),
  });

  // Fetch current room data
  const { data: roomData } = useQuery<unknown, unknown, IRoom>({
    queryKey: ['room', chatRoomId],
    queryFn: async () => {
      const roomRef = doc(collection(db, 'rooms'), chatRoomId as string);
      const roomDoc = await getDoc(roomRef);
      return { ...roomDoc.data(), id: roomDoc.id } as IRoom;
    },
    enabled: !!chatRoomId && !!firebaseUserId,
  });

  // Use roomData for message history check
  useEffect(() => {
    if (roomData && firebaseUserId) {
      hasMessageRef.current = roomData.allMembers.includes(firebaseUserId);
    }
  }, [roomData, firebaseUserId]);

  const handleSendMessage = useCallback(
    async (payload: TSendMessagePayload, onSuccess: () => void) => {
      if (firebaseUserId && chatRoomId) {
        await sendMessage({
          value: payload.message,
          roomId: chatRoomId as string,
          senderId: firebaseUserId,
          attachments: payload.attachments.map((file) => ({
            id: file.key,
            contentType: file.file?.type || '',
            name: file.originalName,
            url: file.originUrl,
            size: file.file?.size || 0,
            status: 'ACTIVE',
            type: Utils.getFileTypeFromContentType(file.file?.type || ''),
          })),
        });
        onSuccess?.();

        if (!hasMessageRef.current) {
          hasMessageRef.current = true;
        }
      }
    },
    [firebaseUserId, chatRoomId, sendMessage],
  );

  const debounceResize = useMemo(
    () =>
      debounce(() => {
        // First we get the viewport height and we multiple it by 1% to get a value for a vh unit
        const vh = window.innerHeight * 0.01;
        // Then we set the value in the --vh custom property to the root of the document
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      }, 300),
    [],
  );

  useWindowEvent('resize', debounceResize);

  if (isLoading) {
    return (
      <Box h="calc(100 * var(--vh, 1vh))" pos="relative">
        <LoadingOverlay visible />
      </Box>
    );
  }

  if (!userData || !firebaseUserId || !chatRoomId) {
    return (
      <Flex
        align="center"
        direction="column"
        h="calc(100 * var(--vh, 1vh))"
        justify="center"
        w="100%"
      >
        <IconEmptyMessage />
        <Text mt={16}>エラーが発生しました。もう一度お試しください。</Text>
      </Flex>
    );
  }

  return (
    <Flex
      align="stretch"
      direction="column"
      h="100%"
      sx={{
        maxHeight: 'calc(var(--vh, 1vh) * 100 - var(--mantine-header-height))',
      }}
    >
      <Flex
        bg="White"
        direction="column"
        mih={0}
        pos="relative"
        py={16}
        sx={{ flex: '1' }}
        w="100%"
      >
        {/* Reset button */}
        <ResetChatRoomButton />

        {/* Message list */}
        <MessageList
          EmptyMessage={
            <Flex align="center" direction="column" gap={8} justify="center">
              <IconEmptyMessage />
              <Text lh={1.5} size={16}>
                チャットでジョブを検索できます。
              </Text>
            </Flex>
          }
          members={membersMap}
          pb={{ base: 28, sm: 0 }}
          pt={24}
          px={{ base: 16, sm: 24 }}
          roomId={chatRoomId as string}
        />
        {/* Message input */}
        <MessageActions
          disabled={!firebaseUserId}
          displayAttachButton={false}
          loading={isSendingMessage}
          maxInputRow={4}
          onSendMessage={handleSendMessage}
          readonly={isSendingMessage}
        />
      </Flex>
    </Flex>
  );
};

ChatRoomPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default ChatRoomPage;
