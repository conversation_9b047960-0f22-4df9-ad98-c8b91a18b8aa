import IconEmptyMessage from '@icons/icon-empty-messages.svg';
import { Box, Flex, LoadingOverlay, Text } from '@mantine/core';
import Layout from 'components/Layout';
import { useMutate, useUser } from 'hooks';
import chatQuery from 'models/chat';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

interface SearchJobChatResponse {
  id: string;
  aiThreadId: string;
  memberFirebaseIds: string[];
}

const SearchJobChatPage = () => {
  const router = useRouter();
  const { data: userData } = useUser();
  const firebaseUserId = userData?.firebaseUserId;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { mutateAsync: createSearchJobChatRoom } = useMutate<
    { type: string },
    SearchJobChatResponse
  >(chatQuery.createSearchJobChatRoom());

  useEffect(() => {
    const createChat = async () => {
      if (!!userData && !!firebaseUserId) {
        try {
          setIsLoading(true);
          const response = await createSearchJobChatRoom(
            { type: 'AI_WORKER_SEARCH_JOBS' },
            {
              onSuccess: () => {
                // Success callback if needed
              },
            },
          );

          // Redirect to the chat room page with the room ID
          router.replace(`/my-page/search-job-chat/${response.id}`);
        } catch (err) {
          setError(
            'チャットルームの作成に失敗しました。もう一度お試しください。',
          );
          setIsLoading(false);
        }
      } else {
        setError(
          'ユーザー情報が取得できませんでした。もう一度お試しください。',
        );
        setIsLoading(false);
      }
    };

    createChat();
  }, [createSearchJobChatRoom, firebaseUserId, router, userData]);

  if (isLoading) {
    return (
      <Box h="calc(100 * var(--vh, 1vh))" pos="relative">
        <LoadingOverlay visible />
      </Box>
    );
  }

  // This will only show if there's an error
  return (
    <Flex
      align="center"
      direction="column"
      h="calc(100 * var(--vh, 1vh))"
      justify="center"
      w="100%"
    >
      <IconEmptyMessage />
      <Text mt={16}>
        {error || 'エラーが発生しました。もう一度お試しください。'}
      </Text>
    </Flex>
  );
};

SearchJobChatPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default SearchJobChatPage;
