import { yupResolver } from '@hookform/resolvers/yup';
import {
  <PERSON>,
  But<PERSON>,
  Card,
  Divider,
  Flex,
  LoadingOverlay,
  Text,
} from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import { CombinedField, DateInputField, TextField } from 'components/Form';
import Layout from 'components/Layout';
import { useFetch, useMutate } from 'hooks';
import jobQuery from 'models/job';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { DateFormat, INSURANCE_TYPE_OPTIONS } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';
import queryClient from 'utils/queryClient';

import {
  type ContractedWorkerFormValues,
  createEditOtherSchema,
} from './schema';
import useStyles from './styles';

// Type for contracted worker detail from API
interface IContractedWorkerDetail {
  _id: string;
  firstName: string;
  firstNameKata: string;
  lastName: string;
  lastNameKata: string;
  birthDate: string;
  gender: string;
  address: string;
  phone: string;
  bloodType: string;
  employmentDate: string;
  yearsOfExperience: number;
  jobTitle: string;
  emergencyContact: {
    name: string;
    relationship: string;
    address: string;
    phone: string;
  };
  specialMedicalExamDate?: string;
  insuranceExamDate: string;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  employmentInsurance: {
    type: string;
    number: string;
  };
  licenseCertEducation: string;
}

const EditOtherForm = () => {
  const router = useRouter();
  const { id: jobId, workerId } = router.query;
  const { classes } = useStyles();

  // Fetch job matching data to get job type
  const {
    data: jobMatching,
    isLoading: isLoadingJobMatching,
    refetch,
  } = useFetch<{
    jobPostInfo: { jobType: string };
  }>({
    ...jobQuery.getMatchingDetail(jobId as string),
    enabled: !!jobId,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false,
    cacheTime: 0,
  });

  // Fetch worker data for initial values
  const { data: worker, isLoading: isLoadingWorker } =
    useFetch<IContractedWorkerDetail>({
      ...jobQuery.getContractedWorkerDetail({
        matchingId: jobId as string,
        contractedWorkerId: workerId as string,
      }),
      enabled: !!jobId && !!workerId,
    });

  // Create dynamic schema based on job post's job type
  const dynamicSchema = createEditOtherSchema(
    jobMatching?.jobPostInfo?.jobType,
  );

  const { control, handleSubmit, setValue } =
    useForm<ContractedWorkerFormValues>({
      resolver: yupResolver(dynamicSchema),
      mode: 'onBlur',
    });

  // Set initial values when worker data is loaded
  useEffect(() => {
    if (worker) {
      // Set other information fields
      setValue('emergencyContact', worker.emergencyContact?.name || '');
      setValue('relationship', worker.emergencyContact?.relationship || '');
      setValue('emergencyAddress', worker.emergencyContact?.address || '');
      setValue('emergencyPhoneNumber', worker.emergencyContact?.phone || '');

      if (worker.specialMedicalExamDate) {
        setValue(
          'specialMedicalExamDate',
          dayjs(worker.specialMedicalExamDate).toDate(),
        );
      }

      if (worker.insuranceExamDate) {
        setValue('insuranceExamDate', dayjs(worker.insuranceExamDate).toDate());
      }

      setValue('bloodPressure', worker.bloodPressure || '');
      setValue('healthInsurance', worker.healthInsurance || '');
      setValue('pensionInsurance', worker.pensionInsurance || '');
      setValue('insuranceType', worker.employmentInsurance?.type || '');
      setValue('insuranceNumber', worker.employmentInsurance?.number || '');
      setValue('otherQualifications', worker.licenseCertEducation || '');
    }
  }, [worker, setValue]);

  const { mutateAsync: updateContractedWorker, isLoading: isSubmitting } =
    useMutate({
      ...jobQuery.editContractedWorker({
        matchingId: jobId as string,
        contractedWorkerId: workerId as string,
      }),
      successMessage: '	完了しました。',
      onSuccess: async () => {
        // Show toast message manually since we override the default onSuccess
        helpers.toast({
          message: '完了しました。',
        });

        // Invalidate worker detail cache to ensure fresh data when navigating back
        await queryClient.invalidateQueries([
          'worker',
          jobId as string,
          workerId as string,
        ]);

        refetch();

        // Add a small delay to ensure toast message is shown before navigation
        setTimeout(() => {
          router.back();
        }, 500);
      },
    });

  const onSubmit: SubmitHandler<ContractedWorkerFormValues> = async (
    values,
  ) => {
    helpers.confirm({
      title: '内容を保存しますか？',
      children: (
        <Text color="black" fz="16px" lh="24px" p={24}>
          確定する場合は、保存してください。
        </Text>
      ),
      labels: { confirm: '保存する', cancel: 'キャンセル' },
      onConfirm: async () => {
        try {
          // Convert form values to API request format
          const apiRequest = {
            emergencyContact: {
              name: values.emergencyContact,
              relationship: values.relationship,
              address: values.emergencyAddress,
              phone: values.emergencyPhoneNumber,
            },
            specialMedicalExamDate: values.specialMedicalExamDate
              ? dayjs(values.specialMedicalExamDate).format('YYYY-MM-DD')
              : null,
            insuranceExamDate: dayjs(values.insuranceExamDate).format(
              'YYYY-MM-DD',
            ),
            bloodPressure: values.bloodPressure,
            healthInsurance: values.healthInsurance,
            pensionInsurance: values.pensionInsurance,
            employmentInsurance: {
              type: values.insuranceType,
              number: values.insuranceNumber,
            },
            licenseCertEducation: values.otherQualifications,
          };

          await updateContractedWorker(apiRequest);
        } catch (error) {
          // Error will be handled by the mutation hook
        }
      },
    });
  };

  if (isLoadingWorker || isLoadingJobMatching) {
    return (
      <Box
        maw={784}
        mx="auto"
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            borderRadius: 0,
          },
        })}
        w="100%"
      >
        <Box pos="relative" px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
          <LoadingOverlay loaderProps={{ size: 'lg' }} visible={true} />
        </Box>
      </Box>
    );
  }

  return (
    <Box
      maw={784}
      mx="auto"
      sx={(theme) => ({
        [theme.fn.smallerThan('sm')]: {
          borderRadius: 0,
        },
      })}
      w="100%"
    >
      <Box pos="relative">
        <form onSubmit={handleSubmit(onSubmit)}>
          <Card
            mb={24}
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                borderRadius: 0,
              },
            })}
            w="100%"
          >
            <Flex
              align="center"
              justify="space-between"
              pb={20}
              pt={16}
              px={{ base: 16, sm: 24 }}
            >
              <Text fw={700} fz={18} lh="26px">
                その他の情報
              </Text>
            </Flex>
            <Divider color="gray-400" />
            <Box px={{ base: 16, sm: 24 }} py={{ base: 16, sm: 24 }}>
              <Box p={0}>
                <Flex
                  className={classes.customClassUpload}
                  direction={{
                    base: 'column',
                  }}
                  gap={16}
                >
                  <TextField
                    control={control}
                    label="緊急連絡先"
                    name="emergencyContact"
                    required
                  />
                  <TextField
                    control={control}
                    label="続柄"
                    name="relationship"
                    required
                  />
                  <TextField
                    control={control}
                    label="現住所"
                    name="emergencyAddress"
                    required
                  />
                  <TextField
                    control={control}
                    format="###-####-####"
                    label="電話番号"
                    name="emergencyPhoneNumber"
                    placeholder="080-1234-5678"
                    required
                  />
                  <Box>
                    <DateInputField
                      clearable
                      control={control}
                      label="特殊健康診断日ー種類"
                      name="specialMedicalExamDate"
                      placeholder="yyyy年mm月dd日"
                      required={
                        !!jobMatching?.jobPostInfo?.jobType &&
                        [
                          '塗装工事',
                          '溶接工事',
                          'Nuclear power plant',
                        ].includes(jobMatching.jobPostInfo.jobType)
                      }
                      subTitle="原子力発電所に応募される方、溶接工の方は必ずご入力ください"
                      valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
                    />
                  </Box>
                  <DateInputField
                    control={control}
                    label="最近の保険診断日"
                    name="insuranceExamDate"
                    placeholder="yyyy年mm月dd日"
                    required
                    valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
                  />
                  <TextField
                    control={control}
                    label="血圧"
                    name="bloodPressure"
                    placeholder="120"
                    required
                  />
                  <TextField
                    control={control}
                    label="健康保険"
                    name="healthInsurance"
                    required
                  />
                  <TextField
                    control={control}
                    label="年金保険"
                    name="pensionInsurance"
                    required
                  />
                  <CombinedField
                    control={control}
                    label="雇用保険/一人親方労災保険番号"
                    maxLength={20}
                    placeholder="123456789"
                    required
                    selectData={INSURANCE_TYPE_OPTIONS}
                    selectName="insuranceType"
                    selectPlaceholder="雇用保険"
                    textName="insuranceNumber"
                  />
                  <TextField
                    control={control}
                    label="免許・資格・教育"
                    name="otherQualifications"
                    required
                  />
                </Flex>
              </Box>
            </Box>

            <Flex
              gap={16}
              justify="flex-end"
              px={24}
              py={16}
              sx={{
                borderTop: '1px solid #E5E7EB',
              }}
            >
              <Button
                disabled={isSubmitting}
                onClick={() => router.back()}
                variant="outline"
                w={100}
              >
                キャンセル
              </Button>
              <Button loading={isSubmitting} type="submit" w={100}>
                保存する
              </Button>
            </Flex>
          </Card>
        </form>
      </Box>
    </Box>
  );
};
EditOtherForm.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};
export default EditOtherForm;
