import * as yup from 'yup';

export interface ContractedWorkerFormValues {
  // Section 2: Other Information
  emergencyContact: string;
  relationship: string;
  emergencyAddress: string;
  emergencyPhoneNumber: string;
  specialMedicalExamDate: Date | null;
  insuranceExamDate: Date;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  insuranceType: string; // New field: dropdown for insurance type
  insuranceNumber: string; // New field: text field for insurance number
  otherQualifications: string;
}

const GENERAL_MAX_LENGTH = 200;

const schema = yup.object().shape({
  // Section 2: Other Information
  emergencyContact: yup
    .string()
    .required('緊急連絡先が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `緊急連絡先は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  relationship: yup
    .string()
    .required('続柄が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `続柄は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  emergencyAddress: yup
    .string()
    .required('緊急連絡先住所が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `緊急連絡先住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  emergencyPhoneNumber: yup
    .string()
    .required('緊急連絡先電話番号が必要です')
    .min(9, '電話番号は9文字以上でなければなりません')
    .max(11, '電話番号は11文字以下でなければなりません')
    .matches(/^[0-9-]+$/, '電話番号は数字とハイフンのみを含めることができます'),

  specialMedicalExamDate: yup
    .date()
    .nullable()
    .test({
      name: 'conditional-required',
      message:
        '特殊健康診断日が必要です（塗装工事/溶接工事/Nuclear power plantの場合）',
      test(_value) {
        // This validation is now handled by the createEditOtherSchema function
        // which has access to the worker's job title
        return true;
      },
    }),

  insuranceExamDate: yup.date().required('最近の保険診断日が必要です'),

  bloodPressure: yup.string().required('血圧が必要です'),
  // .test(
  //   'blood-pressure-format',
  //   '血圧は正しい形式で入力してください（例：180/120、140/90）',
  //   (value) => {
  //     if (!value) return true; // Let required validation handle empty values
  //     // Accept formats like "180/120", "140/90", or simple numbers like "180"
  //     const bloodPressureRegex = /^\d{2,3}(\/\d{2,3})?$/;
  //     return bloodPressureRegex.test(value);
  //   },
  // ),

  healthInsurance: yup
    .string()
    .required('健康保険が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `健康保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  pensionInsurance: yup
    .string()
    .required('年金保険が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `年金保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  insuranceType: yup
    .string()
    .required('雇用保険/一人親方労災保険の種類が必要です'),

  insuranceNumber: yup
    .string()
    .required('雇用保険/一人親方労災保険番号が必要です')
    .max(20, '雇用保険/一人親方労災保険番号は20文字以下でなければなりません'),

  otherQualifications: yup
    .string()
    .required('免許・資格・教育が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `免許・資格・教育は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),
});

// Function to create schema with conditional validation based on job type
export const createEditOtherSchema = (jobType?: string) => {
  // Define the job types that require special medical exam
  const SPECIAL_EXAM_REQUIRED_JOB_TYPES = [
    '塗装工事',
    '溶接工事',
    'Nuclear power plant',
  ];

  // Check if the job type requires special medical exam
  const isSpecialExamRequired = jobType
    ? SPECIAL_EXAM_REQUIRED_JOB_TYPES.includes(jobType)
    : false;

  return yup.object().shape({
    // Section 2: Other Information
    emergencyContact: yup
      .string()
      .required('緊急連絡先が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `緊急連絡先は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    relationship: yup
      .string()
      .required('続柄が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `続柄は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    emergencyAddress: yup
      .string()
      .required('緊急連絡先住所が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `緊急連絡先住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    emergencyPhoneNumber: yup
      .string()
      .required('緊急連絡先電話番号が必要です')
      .min(9, '電話番号は9文字以上でなければなりません')
      .max(11, '電話番号は11文字以下でなければなりません')
      .matches(
        /^[0-9-]+$/,
        '電話番号は数字とハイフンのみを含めることができます',
      ),

    // Conditional validation for special medical exam
    specialMedicalExamDate: isSpecialExamRequired
      ? yup
          .date()
          .nullable()
          .required(
            '特殊健康診断日が必要です（塗装工事/溶接工事/Nuclear power plantの場合）',
          )
      : yup.date().nullable().notRequired(),

    insuranceExamDate: yup.date().required('最近の保険診断日が必要です'),

    bloodPressure: yup.string().required('血圧が必要です'),
    // .test(
    //   'blood-pressure-format',
    //   '血圧は正しい形式で入力してください（例：180/120、140/90）',
    //   (value) => {
    //     if (!value) return true; // Let required validation handle empty values
    //     // Accept formats like "180/120", "140/90", or simple numbers like "180"
    //     const bloodPressureRegex = /^\d{2,3}(\/\d{2,3})?$/;
    //     return bloodPressureRegex.test(value);
    //   },
    // ),

    healthInsurance: yup
      .string()
      .required('健康保険が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `健康保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    pensionInsurance: yup
      .string()
      .required('年金保険が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `年金保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    insuranceType: yup
      .string()
      .required('雇用保険/一人親方労災保険の種類が必要です'),

    insuranceNumber: yup
      .string()
      .required('雇用保険/一人親方労災保険番号が必要です')
      .max(20, '雇用保険/一人親方労災保険番号は20文字以下でなければなりません'),

    otherQualifications: yup
      .string()
      .required('免許・資格・教育が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `免許・資格・教育は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),
  });
};

export default schema;
