import type { InferType } from 'yup';
import { array, boolean, object, string } from 'yup';

const schema = object({
  beforeIdentification: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
        readOnly: boolean().default(false),
      }),
    )
    .required(),
  afterIdentification: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
        readOnly: boolean().default(false),
      }),
    )
    .required(),
  beforeHealthInsurance: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
        readOnly: boolean().default(false),
      }),
    )
    .required(),
  afterHealthInsurance: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
        readOnly: boolean().default(false),
      }),
    )
    .required(),
  qualifications: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
        readOnly: boolean().default(false),
      }),
    )
    .max(40)
    .required(),
  healthCertificate: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
        readOnly: boolean().default(false),
      }),
    )
    .max(40)
    .required(),
});

export default schema;
export type MatchingWorkerUpdateFormValues = InferType<typeof schema>;
