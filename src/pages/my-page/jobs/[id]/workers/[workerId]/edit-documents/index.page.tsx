/* eslint-disable react/jsx-no-duplicate-props */
/* eslint-disable no-irregular-whitespace */
import { yupResolver } from '@hookform/resolvers/yup';
import IconWarningCircle from '@icons/icon-warning-circle.svg';
import {
  Box,
  Button,
  Card,
  Divider,
  Flex,
  LoadingOverlay,
  Text,
  ThemeIcon,
} from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import { FileUpload } from 'components/Form';
import UploadImageFileList from 'components/Form/UploadImageFileList';
import Layout from 'components/Layout';
import { useFetch, useMutate } from 'hooks';
import jobQuery from 'models/job';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';
import { useForm } from 'react-hook-form';
import { FILE_UPLOAD_IMAGE_TYPES } from 'utils/constants';
import helpers from 'utils/helpers';
import queryClient from 'utils/queryClient';

import type { MatchingWorkerUpdateFormValues } from './schema';
import schema from './schema';
import useStyles from './styles';

const EditDocuments = () => {
  const { control, handleSubmit, watch, setValue } =
    useForm<MatchingWorkerUpdateFormValues>({
      resolver: yupResolver(schema),
      mode: 'onBlur',
    });
  const router = useRouter();
  const { id: jobId, workerId } = router.query;
  const { classes } = useStyles();

  // Fetch worker data to get existing documents
  const { data: worker, isLoading } = useFetch({
    ...jobQuery.getContractedWorkerDetail({
      matchingId: jobId as string,
      contractedWorkerId: workerId as string,
    }),
    enabled: !!jobId && !!workerId,
  });

  // API mutation for updating worker documents using editContractedWorker
  const { mutateAsync: updateWorkerDocuments, isLoading: isSubmitting } =
    useMutate({
      ...jobQuery.editContractedWorker({
        matchingId: jobId as string,
        contractedWorkerId: workerId as string,
      }),
      onSuccess: async () => {
        helpers.toast({
          message: '完了しました。',
        });

        // Invalidate worker detail cache to ensure fresh data when navigating back
        await queryClient.invalidateQueries([
          'worker',
          jobId as string,
          workerId as string,
        ]);

        router.back();
      },
    });
  const beforeIdentification = watch('beforeIdentification');
  const afterIdentification = watch('afterIdentification');
  const beforeHealthInsurance = watch('beforeHealthInsurance');
  const afterHealthInsurance = watch('afterHealthInsurance');
  const qualifications = watch('qualifications');
  const healthCertificate = watch('healthCertificate');
  const countUploadFileIdentification =
    (beforeIdentification || []).length + (afterIdentification || []).length;
  const countUploadFileHealthInsurance =
    (beforeHealthInsurance || []).length + (afterHealthInsurance || []).length;
  const countQualifications = (qualifications || []).length;
  const healthCertificateCount = (healthCertificate || []).length;

  // Load existing document data when worker data is available
  React.useEffect(() => {
    if (worker && typeof worker === 'object') {
      // For now, let's work with the applicationDocuments array that we know exists
      // and categorize them based on naming conventions or use all for qualifications
      const applicationDocs = (worker as any).applicationDocuments || [];

      // Initialize empty arrays for all document types
      const idFrontDocs: any[] = [];
      const idBackDocs: any[] = [];
      const healthInsuranceFrontDocs: any[] = [];
      const healthInsuranceBackDocs: any[] = [];
      const licenseDocs: any[] = [];
      const healthExamDocs: any[] = [];

      // ID Documents
      if ((worker as any).identificationCard?.front) {
        idFrontDocs.push({
          _id: (worker as any).identificationCard.front._id,
          key: (worker as any).identificationCard.front.key,
          originUrl: (worker as any).identificationCard.front.originUrl,
          url: (worker as any).identificationCard.front.originUrl, // Use originUrl as url
          originalName: (worker as any).identificationCard.front.originalName,
          readOnly: false,
        });
      }

      if ((worker as any).identificationCard?.back) {
        idBackDocs.push({
          _id: (worker as any).identificationCard.back._id,
          key: (worker as any).identificationCard.back.key,
          originUrl: (worker as any).identificationCard.back.originUrl,
          url: (worker as any).identificationCard.back.originUrl, // Use originUrl as url
          originalName: (worker as any).identificationCard.back.originalName,
          readOnly: false,
        });
      }

      // Health Insurance Documents
      if ((worker as any).healthInsuranceCard?.front) {
        healthInsuranceFrontDocs.push({
          _id: (worker as any).healthInsuranceCard.front._id,
          key: (worker as any).healthInsuranceCard.front.key,
          originUrl: (worker as any).healthInsuranceCard.front.originUrl,
          url: (worker as any).healthInsuranceCard.front.originUrl, // Use originUrl as url
          originalName: (worker as any).healthInsuranceCard.front.originalName,
          readOnly: false,
        });
      }

      if ((worker as any).healthInsuranceCard?.back) {
        healthInsuranceBackDocs.push({
          _id: (worker as any).healthInsuranceCard.back._id,
          key: (worker as any).healthInsuranceCard.back.key,
          originUrl: (worker as any).healthInsuranceCard.back.originUrl,
          url: (worker as any).healthInsuranceCard.back.originUrl, // Use originUrl as url
          originalName: (worker as any).healthInsuranceCard.back.originalName,
          readOnly: false,
        });
      }

      if ((worker as any).licenses?.length > 0) {
        licenseDocs.push(
          ...(worker as any).licenses.map((doc: any) => ({
            _id: doc._id,
            key: doc.key,
            originUrl: doc.originUrl,
            url: doc.url || doc.originUrl,
            originalName: doc.originalName,
            readOnly: false,
          })),
        );
      } else if (applicationDocs.length > 0) {
        // Fallback to applicationDocuments for licenses if no specific license documents
        licenseDocs.push(
          ...applicationDocs.map((doc: any) => ({
            _id: doc._id,
            key: doc.key,
            originUrl: doc.originUrl,
            url: doc.url || doc.originUrl,
            originalName: doc.originalName,
            readOnly: false,
          })),
        );
      }

      if ((worker as any).healthExamReports?.length > 0) {
        healthExamDocs.push(
          ...(worker as any).healthExamReports.map((doc: any) => ({
            _id: doc._id,
            key: doc.key,
            originUrl: doc.originUrl,
            url: doc.url || doc.originUrl,
            originalName: doc.originalName,
            readOnly: false,
          })),
        );
      }

      // Set form values with existing data
      setValue('beforeIdentification', idFrontDocs);
      setValue('afterIdentification', idBackDocs);
      setValue('beforeHealthInsurance', healthInsuranceFrontDocs);
      setValue('afterHealthInsurance', healthInsuranceBackDocs);
      setValue('qualifications', licenseDocs);
      setValue('healthCertificate', healthExamDocs);
    }
  }, [worker, setValue]);

  // Submit handler with correct data format
  const onSubmit = async (values: MatchingWorkerUpdateFormValues) => {
    helpers.confirm({
      title: '内容を保存しますか？',
      children: (
        <Text color="black" fz="16px" lh="24px" p={24}>
          確定する場合は、保存してください。
        </Text>
      ),
      labels: { confirm: '保存する', cancel: 'キャンセル' },
      onConfirm: async () => {
        try {
          // Convert form values to the required API format
          const documentData = {
            identificationCard: {
              frontKey: values.beforeIdentification?.[0]?.key || '',
              backKey: values.afterIdentification?.[0]?.key || '',
            },
            licensesKeys: values.qualifications?.map((doc) => doc.key) || [],
            healthInsuranceCard: {
              frontKey: values.beforeHealthInsurance?.[0]?.key || '',
              backKey: values.afterHealthInsurance?.[0]?.key || '',
            },
            healthExamReportKeys:
              values.healthCertificate?.map((doc) => doc.key) || [],
          };

          await updateWorkerDocuments(documentData);
          // Remove router.back() from here since it's now handled in onSuccess
        } catch (error) {
          // Error will be handled by the mutation hook
        }
      },
    });
  };

  return (
    <Box
      maw={784}
      mx="auto"
      sx={(theme) => ({
        [theme.fn.smallerThan('sm')]: {
          borderRadius: 0,
        },
      })}
      w="100%"
    >
      <Box pos="relative">
        <LoadingOverlay
          loaderProps={{ size: 'lg' }}
          visible={isLoading || isSubmitting}
        />

        <form onSubmit={handleSubmit(onSubmit)}>
          <Card
            mb={24}
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                borderRadius: 0,
              },
            })}
            w="100%"
          >
            <Flex
              align="center"
              justify="space-between"
              pb={20}
              pt={16}
              px={{ base: 16, sm: 24 }}
            >
              <Text fw={700} fz={18} lh="26px">
                提出書類
              </Text>
            </Flex>
            <Divider color="gray-400" />
            <Box px={{ base: 16, sm: 24 }} py={{ base: 16, sm: 24 }}>
              <Text>
                必ずこちらをご確認のうえ、撮影、およびアップロードをしてください
              </Text>
              <Link
                className={classes.link}
                href="/uploading-guide"
                target="_blank"
              >
                確認書類の撮影・アップロードガイド
              </Link>

              <Box pb={0} pt={{ base: 16, sm: 24 }} px={16}>
                <Text c="#7B8492" fw={700} fz={14} mb={16}>
                  ①身分証 ({countUploadFileIdentification || 0}/2)
                </Text>
                <Flex
                  className={classes.customClassUpload}
                  direction={{
                    base: 'column',
                    sm: 'row',
                  }}
                  gap={16}
                >
                  <Flex direction="column" w="100%">
                    <FileUpload
                      accept={FILE_UPLOAD_IMAGE_TYPES}
                      control={control}
                      maxSize={10}
                      name="beforeIdentification"
                      render={(props) => (
                        <UploadImageFileList
                          acceptTypes={props.accept}
                          data={props.values}
                          error={props.error}
                          loading={props.isUploading}
                          max={1}
                          onAdd={() => props.ref.current?.click()}
                          onRemove={props.remove}
                          progress={props?.progress || 0}
                          {...{
                            customClass: 'customClassUploadFile',
                          }}
                        />
                      )}
                    />
                    <Text
                      align="center"
                      bg="neutral-50"
                      color="#111827"
                      fz={14}
                      lh="24px"
                      sx={{
                        borderRadius: 4,
                      }}
                    >
                      表面
                    </Text>
                  </Flex>
                  <Flex direction="column" w="100%">
                    <FileUpload
                      accept={FILE_UPLOAD_IMAGE_TYPES}
                      control={control}
                      maxSize={10}
                      name="afterIdentification"
                      render={(props) => (
                        <UploadImageFileList
                          acceptTypes={props.accept}
                          data={props.values}
                          error={props.error}
                          loading={props.isUploading}
                          max={1}
                          onAdd={() => props.ref.current?.click()}
                          onRemove={props.remove}
                          progress={props?.progress || 0}
                          {...{
                            customClass: 'customClassUploadFile',
                          }}
                        />
                      )}
                    />
                    <Text
                      align="center"
                      bg="neutral-50"
                      color="#111827"
                      fz={14}
                      lh="24px"
                      sx={{
                        borderRadius: 4,
                      }}
                    >
                      裏面
                    </Text>
                  </Flex>
                </Flex>
              </Box>
              <Box pb={0} pt={{ base: 16, sm: 24 }} px={16}>
                <Text c="#7B8492" fw={700} fz={14}>
                  ②資格証 ({countQualifications}/40)
                </Text>
                <Text c="#101211" fz={12}>
                  ※ひとつの案件で登録できる資格証の上限は20個です
                  <br />
                  ※裏面に重要事項や、個人情報などが表記されている場合は、裏面もアップロードしてください
                  <br />
                  ※度にアップロードできる画像は10件まで、最大40枚の画像が登録できます
                  <br />
                  ※応募案件に関連のある資格から、優先して登録をしてください
                  <br />
                  ※添付ファイルはJPG、JPEG、PNG形式でアップロードしてください。
                </Text>

                {/* Warning when limit reached */}
                {countQualifications >= 40 && (
                  <Flex align="center" gap={8} mt={8}>
                    <ThemeIcon color="error-500" size={24}>
                      <IconWarningCircle />
                    </ThemeIcon>
                    <Text c="error-500" fw={500} fz={12}>
                      アップロード数の上限 40枚に達しました
                    </Text>
                  </Flex>
                )}

                <Flex
                  className={classes.customClassMultiUpload}
                  direction={{
                    base: 'column',
                    sm: 'row',
                  }}
                  gap={16}
                >
                  <Flex direction="column" w="100%">
                    <FileUpload
                      accept={FILE_UPLOAD_IMAGE_TYPES}
                      control={control}
                      max={40}
                      maxSize={10}
                      multiple
                      name="qualifications"
                      render={(props) => (
                        <UploadImageFileList
                          acceptTypes={props.accept}
                          data={props.values}
                          error={props.error}
                          hideUploadButton={countQualifications >= 40}
                          loading={props.isUploading}
                          max={40}
                          onAdd={
                            countQualifications >= 40
                              ? undefined
                              : () => props.ref.current?.click()
                          }
                          onRemove={props.remove}
                          progress={props?.progress || 0}
                          {...{
                            multiple: true,
                            customClass: 'customClassMultiUpload',
                          }}
                        />
                      )}
                    />
                  </Flex>
                </Flex>
              </Box>
              <Box pb={0} pt={{ base: 16, sm: 24 }} px={16}>
                <Text c="#7B8492" fw={700} fz={14} mb={16}>
                  ③健康保険証 ({countUploadFileHealthInsurance}/2)
                </Text>
                <Flex
                  className={classes.customClassUpload}
                  direction={{
                    base: 'column',
                    sm: 'row',
                  }}
                  gap={16}
                >
                  <Flex direction="column" w="100%">
                    <FileUpload
                      accept={FILE_UPLOAD_IMAGE_TYPES}
                      control={control}
                      maxSize={10}
                      name="beforeHealthInsurance"
                      render={(props) => (
                        <UploadImageFileList
                          acceptTypes={props.accept}
                          data={props.values}
                          error={props.error}
                          loading={props.isUploading}
                          max={1}
                          onAdd={() => props.ref.current?.click()}
                          onRemove={props.remove}
                          progress={props?.progress || 0}
                          {...{
                            customClass: 'customClassUploadFile',
                          }}
                        />
                      )}
                    />
                    <Text
                      align="center"
                      bg="neutral-50"
                      color="#111827"
                      fz={14}
                      lh="24px"
                      sx={{
                        borderRadius: 4,
                      }}
                    >
                      表面
                    </Text>
                  </Flex>
                  <Flex direction="column" w="100%">
                    <FileUpload
                      accept={FILE_UPLOAD_IMAGE_TYPES}
                      control={control}
                      maxSize={10}
                      name="afterHealthInsurance"
                      render={(props) => (
                        <UploadImageFileList
                          acceptTypes={props.accept}
                          data={props.values}
                          error={props.error}
                          loading={props.isUploading}
                          max={1}
                          onAdd={() => props.ref.current?.click()}
                          onRemove={props.remove}
                          progress={props?.progress || 0}
                          {...{
                            customClass: 'customClassUploadFile',
                          }}
                        />
                      )}
                    />
                    <Text
                      align="center"
                      bg="neutral-50"
                      color="#111827"
                      fz={14}
                      lh="24px"
                      sx={{
                        borderRadius: 4,
                      }}
                    >
                      裏面
                    </Text>
                  </Flex>
                </Flex>
              </Box>
              <Box pt={{ base: 16, sm: 24 }} px={16}>
                <Text c="#7B8492" fw={700} fz={14}>
                  ④健康診断証 ({healthCertificateCount}/40)
                </Text>
                <Text c="#101211" fz={12}>
                  ※登録できる健康保険証のページ数は、40ページ(40枚)までです
                  <br />
                  ※1度にアップロードできる画像は10点まで、最大40枚の画像が登録できます
                  <br />
                  ※健康診断書が3ページ以上にわたる場合は、2ページ目以降のすべてのページを［裏面］としてアップロードしてください。
                  <br />
                  ※添付ファイルはJPG、JPEG、PNG形式でアップロードしてください。
                </Text>

                {/* Warning when limit reached */}
                {healthCertificateCount >= 40 && (
                  <Flex align="center" gap={8} mt={8}>
                    <ThemeIcon color="error-500" size={24}>
                      <IconWarningCircle />
                    </ThemeIcon>
                    <Text c="error-500" fw={500} fz={12}>
                      アップロード数の上限 40枚に達しました
                    </Text>
                  </Flex>
                )}

                <Flex
                  className={classes.customClassMultiUpload}
                  direction={{
                    base: 'column',
                    sm: 'row',
                  }}
                  gap={16}
                >
                  <Flex direction="column" w="100%">
                    <FileUpload
                      accept={FILE_UPLOAD_IMAGE_TYPES}
                      control={control}
                      max={40}
                      maxSize={10}
                      multiple
                      name="healthCertificate"
                      render={(props) => (
                        <UploadImageFileList
                          acceptTypes={props.accept}
                          data={props.values}
                          error={props.error}
                          hideUploadButton={healthCertificateCount >= 40}
                          loading={props.isUploading}
                          max={40}
                          onAdd={
                            healthCertificateCount >= 40
                              ? undefined
                              : () => props.ref.current?.click()
                          }
                          onRemove={props.remove}
                          progress={props?.progress || 0}
                          {...{
                            multiple: true,
                            customClass: 'customClassMultiUpload',
                          }}
                        />
                      )}
                    />
                  </Flex>
                </Flex>
              </Box>
            </Box>

            <Flex
              gap={16}
              justify="flex-end"
              px={24}
              py={16}
              sx={{
                borderTop: '1px solid #E5E7EB',
              }}
            >
              <Button onClick={() => router.back()} variant="outline" w={100}>
                キャンセル
              </Button>
              <Button
                disabled={
                  countUploadFileIdentification < 2 ||
                  countUploadFileHealthInsurance < 2
                }
                loading={isSubmitting}
                type="submit"
                w={100}
              >
                保存する
              </Button>
            </Flex>
          </Card>
        </form>
      </Box>
    </Box>
  );
};
EditDocuments.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};
export default EditDocuments;
