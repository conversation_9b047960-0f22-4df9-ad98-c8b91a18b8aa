import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Flex, LoadingOverlay, Text } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import Layout from 'components/Layout';
import { useFetch, useMutate } from 'hooks';
import jobQuery from 'models/job';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';
import queryClient from 'utils/queryClient';

import CombinedWorkerEditForm from './CombinedWorkerEditForm';
import {
  type CombinedWorkerEditFormValues,
  convertCombinedFormToApiRequest,
  createCombinedSchema,
} from './schema';

// Type for contracted worker detail from API
interface IContractedWorkerDetail {
  _id: string;
  firstName: string;
  firstNameKata: string;
  lastName: string;
  lastNameKata: string;
  birthDate: string;
  gender: string;
  address: string;
  phone: string;
  bloodType: string;
  employmentDate: string;
  yearsOfExperience: number;
  jobTitle: string;
  emergencyContact: {
    name: string;
    relationship: string;
    address: string;
    phone: string;
  };
  specialMedicalExamDate?: string;
  insuranceExamDate: string;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  employmentInsurance: {
    type: string;
    number: string;
  };
  licenseCertEducation: string;
}

const CombinedWorkerEditPage = () => {
  const router = useRouter();
  const { id: jobId, workerId } = router.query;

  // Fetch job matching data to get job type
  const { data: jobMatching, isLoading: isLoadingJobMatching } = useFetch<{
    jobPostInfo: { jobType: string };
  }>({
    ...jobQuery.getMatchingDetail(jobId as string),
    enabled: !!jobId,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false,
    cacheTime: 0,
  });

  // Fetch worker data for initial values
  const { data: worker, isLoading: isLoadingWorker } =
    useFetch<IContractedWorkerDetail>({
      ...jobQuery.getContractedWorkerDetail({
        matchingId: jobId as string,
        contractedWorkerId: workerId as string,
      }),
      enabled: !!jobId && !!workerId,
    });

  // Create dynamic schema based on job type
  const dynamicSchema = useMemo(
    () => createCombinedSchema(jobMatching?.jobPostInfo?.jobType),
    [jobMatching?.jobPostInfo?.jobType],
  );

  const methods = useForm<CombinedWorkerEditFormValues>({
    resolver: yupResolver(dynamicSchema),
    mode: 'onBlur',
  });

  // Set initial values when worker data is loaded
  useEffect(() => {
    if (worker) {
      // Profile section
      methods.setValue('firstName', worker.firstName || '');
      methods.setValue('lastName', worker.lastName || '');
      methods.setValue('firstNameKata', worker.firstNameKata || '');
      methods.setValue('lastNameKata', worker.lastNameKata || '');

      if (worker.birthDate) {
        const birthDateValue = dayjs(worker.birthDate).toDate();
        methods.setValue('birthDate', birthDateValue);
        const age = dayjs().diff(dayjs(worker.birthDate), 'year');
        methods.setValue('age', age);
      }

      methods.setValue('gender', worker.gender || '');
      methods.setValue('address', worker.address || '');
      methods.setValue('phoneNumber', worker.phone || '');
      methods.setValue('bloodType', worker.bloodType || '');

      if (worker.employmentDate) {
        methods.setValue(
          'employmentDate',
          dayjs(worker.employmentDate).toDate(),
        );
      }

      methods.setValue('yearsOfExperience', worker.yearsOfExperience || 0);
      methods.setValue('jobTitle', worker.jobTitle || '');

      // Other information section
      methods.setValue('emergencyContact', worker.emergencyContact?.name || '');
      methods.setValue(
        'relationship',
        worker.emergencyContact?.relationship || '',
      );
      methods.setValue(
        'emergencyAddress',
        worker.emergencyContact?.address || '',
      );
      methods.setValue(
        'emergencyPhoneNumber',
        worker.emergencyContact?.phone || '',
      );

      if (worker.specialMedicalExamDate) {
        methods.setValue(
          'specialMedicalExamDate',
          dayjs(worker.specialMedicalExamDate).toDate(),
        );
      }

      if (worker.insuranceExamDate) {
        methods.setValue(
          'insuranceExamDate',
          dayjs(worker.insuranceExamDate).toDate(),
        );
      }

      methods.setValue('bloodPressure', worker.bloodPressure || '');
      methods.setValue('healthInsurance', worker.healthInsurance || '');
      methods.setValue('pensionInsurance', worker.pensionInsurance || '');
      methods.setValue('insuranceType', worker.employmentInsurance?.type || '');
      methods.setValue(
        'insuranceNumber',
        worker.employmentInsurance?.number || '',
      );
      methods.setValue(
        'otherQualifications',
        worker.licenseCertEducation || '',
      );
    }
  }, [worker, methods]);

  const { mutateAsync: updateContractedWorker, isLoading: isSubmitting } =
    useMutate({
      ...jobQuery.editContractedWorker({
        matchingId: jobId as string,
        contractedWorkerId: workerId as string,
      }),
      onSuccess: async () => {
        // Invalidate worker detail cache
        await queryClient.invalidateQueries([
          'worker',
          jobId as string,
          workerId as string,
        ]);
        
        // Also invalidate the matching detail to update isDoneDetail status
        await queryClient.invalidateQueries([
          'matchingDetail',
          jobId as string,
        ]);
      },
    });

  const handleCancel = () => {
    if (methods.formState.isDirty) {
      helpers.confirm({
        title: '入力内容を破棄',
        children: (
          <Text color="black" fz="16px" lh="24px" p={24}>
            入力内容を破棄しますか？
          </Text>
        ),
        onConfirm: () => {
          router.push(`/my-page/jobs/${jobId}`);
        },
      });
    } else {
      router.push(`/my-page/jobs/${jobId}`);
    }
  };

  const onSubmit: SubmitHandler<CombinedWorkerEditFormValues> = async (
    values,
  ) => {
    // Show confirmation modal with Save/Return options
    helpers.confirm({
      title: '内容を保存しますか？',
      children: (
        <Text color="black" fz="16px" lh="24px" p={24}>
          確定する場合は、保存してください。
        </Text>
      ),
      labels: { confirm: '保存する', cancel: '戻る' },
      onConfirm: async () => {
        try {
          // Convert form values to API request format and mark as reviewed
          const apiRequest = {
            ...convertCombinedFormToApiRequest(values),
            isDoneDetail: true, // Mark as reviewed when saving
          };
          
          // Update the worker data
          await updateContractedWorker(apiRequest);
          
          helpers.toast({
            message: '完了しました。',
          });

          // Navigate back to matching details page
          setTimeout(() => {
            router.push(`/my-page/jobs/${jobId}`);
          }, 300);
        } catch (error) {
          // Error will be handled by the mutation hook
        }
      },
      onCancel: () => {
        // Do nothing - stay on the edit page
      },
    });
  };

  if (isLoadingWorker || isLoadingJobMatching) {
    return (
      <Box
        maw={784}
        mx="auto"
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            borderRadius: 0,
          },
        })}
        w="100%"
      >
        <Box pos="relative" px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
          <LoadingOverlay loaderProps={{ size: 'lg' }} visible={true} />
        </Box>
      </Box>
    );
  }

  return (
    <FormProvider {...methods}>
      <Box
        maw={784}
        mx="auto"
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            borderRadius: 0,
          },
        })}
        w="100%"
      >
        <Box pos="relative" pt={{ base: 24, sm: 32 }}>
          <form onSubmit={methods.handleSubmit(onSubmit)}>
            <CombinedWorkerEditForm
              jobType={jobMatching?.jobPostInfo?.jobType}
            />
            
            {/* Action Buttons - Following Figma Design */}
            <Flex
              bg="white"
              gap={16}
              justify="center"
              mt={24}
              p={16}
              sx={(_theme) => ({
                boxShadow: '2px 4px 10px 0px rgba(117, 138, 147, 0.1)',
                borderRadius: '0px',
                position: 'sticky',
                bottom: 0,
                zIndex: 10,
              })}
            >
              <Button
                disabled={isSubmitting}
                onClick={handleCancel}
                size="lg"
                sx={{
                  width: 167,
                  height: 48,
                  borderRadius: 10,
                  fontSize: 14,
                  fontWeight: 500,
                }}
                variant="outline"
              >
                キャンセル
              </Button>
              <Button
                loading={isSubmitting}
                size="lg"
                sx={{
                  width: 168,
                  height: 48,
                  borderRadius: 10,
                  fontSize: 14,
                  fontWeight: 500,
                }}
                type="submit"
              >
                保存する
              </Button>
            </Flex>
          </form>
        </Box>
      </Box>
    </FormProvider>
  );
};

CombinedWorkerEditPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb
        hiddenPaths={['/my-page/jobs/[id]', '/my-page/jobs/[id]/workers']}
      />
      {page}
    </Layout>
  );
};

export default CombinedWorkerEditPage;
