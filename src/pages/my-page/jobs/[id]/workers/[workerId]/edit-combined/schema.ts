import dayjs from 'dayjs';
import { REG_EXP } from 'utils/constants';
import * as yup from 'yup';

// Combined form values interface
export interface CombinedWorkerEditFormValues {
  // Section 1: Profile
  firstName: string;
  lastName: string;
  firstNameKata: string;
  lastNameKata: string;
  birthDate: Date;
  age: number;
  gender: string;
  address: string;
  phoneNumber: string;
  bloodType: string;
  employmentDate: Date;
  yearsOfExperience: number;
  jobTitle: string;

  // Section 2: Other Information
  emergencyContact: string;
  relationship: string;
  emergencyAddress: string;
  emergencyPhoneNumber: string;
  specialMedicalExamDate: Date | null;
  insuranceExamDate: Date;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  insuranceType: string;
  insuranceNumber: string;
  otherQualifications: string;
}

const NAME_MAX_LENGTH = 10;
const GENERAL_MAX_LENGTH = 200;

// Function to create combined schema with conditional validation based on job type
export const createCombinedSchema = (jobType?: string) => {
  // Define the job types that require special medical exam
  const SPECIAL_EXAM_REQUIRED_JOB_TYPES = [
    '塗装工事',
    '溶接工事',
    'Nuclear power plant',
  ];

  // Check if the job type requires special medical exam
  const isSpecialExamRequired = jobType
    ? SPECIAL_EXAM_REQUIRED_JOB_TYPES.includes(jobType)
    : false;

  return yup.object().shape({
    // Section 1: Profile
    firstName: yup
      .string()
      .trim()
      .required('名が必要です')
      .max(NAME_MAX_LENGTH, '名は10文字以下でなければなりません')
      .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

    lastName: yup
      .string()
      .trim()
      .required('姓が必要です')
      .max(NAME_MAX_LENGTH, '姓は10文字以下でなければなりません')
      .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

    firstNameKata: yup
      .string()
      .trim()
      .required('フリガナ(名)が必要です')
      .max(NAME_MAX_LENGTH, 'フリガナ(名)は10文字以下でなければなりません')
      .matches(REG_EXP.KATAKANA, '無効な形式です。'),

    lastNameKata: yup
      .string()
      .trim()
      .required('フリガナ(姓)が必要です')
      .max(NAME_MAX_LENGTH, 'フリガナ(姓)は10文字以下でなければなりません')
      .matches(REG_EXP.KATAKANA, '無効な形式です。'),

    birthDate: yup
      .date()
      .required('生年月日が必要です')
      .test('min-age', '18歳以上でなければなりません', (value) => {
        if (!value) return true;
        const today = new Date();
        const birthDate = new Date(value);
        const age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        // Check if birthday has occurred this year
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && today.getDate() < birthDate.getDate())
        ) {
          return age - 1 >= 18;
        }
        return age >= 18;
      }),

    age: yup.number(),

    gender: yup.string().required('性別が必要です'),

    address: yup
      .string()
      .required('住所が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    phoneNumber: yup
      .string()
      .required('電話番号が必要です')
      .min(9, '電話番号は9文字以上でなければなりません')
      .max(11, '電話番号は11文字以下でなければなりません')
      .matches(
        /^[0-9-]+$/,
        '電話番号は数字とハイフンのみを含めることができます',
      ),

    bloodType: yup.string().required('血液型が必要です'),

    employmentDate: yup.date().required('雇用年月日が必要です'),

    yearsOfExperience: yup
      .number()
      .required('経験年数が必要です')
      .min(0, '経験年数は0年以上でなければなりません')
      .max(100, '無効な形式です。')
      .test('digit-format', '無効な形式です。', (value) => {
        if (value === undefined || value === null) return true;
        const wholePart = Math.floor(Math.abs(value)).toString();
        return wholePart.length <= 2;
      })
      .test('decimal-places', '無効な形式です。', (value) => {
        if (value === undefined || value === null) return true;
        const decimalPlaces = (value.toString().split('.')[1] || '').length;
        return decimalPlaces <= 1;
      })
      .typeError('無効な形式です。'),

    jobTitle: yup
      .string()
      .required('職種が必要です')
      .min(1, '少なくとも一つの職種を選択してください'),

    // Section 2: Other Information
    emergencyContact: yup
      .string()
      .required('緊急連絡先が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `緊急連絡先は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    relationship: yup
      .string()
      .required('続柄が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `続柄は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    emergencyAddress: yup
      .string()
      .required('緊急連絡先住所が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `緊急連絡先住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    emergencyPhoneNumber: yup
      .string()
      .required('緊急連絡先電話番号が必要です')
      .min(9, '電話番号は9文字以上でなければなりません')
      .max(11, '電話番号は11文字以下でなければなりません')
      .matches(
        /^[0-9-]+$/,
        '電話番号は数字とハイフンのみを含めることができます',
      ),

    // Conditional validation for special medical exam
    specialMedicalExamDate: isSpecialExamRequired
      ? yup
          .date()
          .nullable()
          .required(
            '特殊健康診断日が必要です（塗装工事/溶接工事/Nuclear power plantの場合）',
          )
      : yup.date().nullable().notRequired(),

    insuranceExamDate: yup.date().required('最近の保険診断日が必要です'),

    bloodPressure: yup.string().required('血圧が必要です'),

    healthInsurance: yup
      .string()
      .required('健康保険が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `健康保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    pensionInsurance: yup
      .string()
      .required('年金保険が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `年金保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    insuranceType: yup
      .string()
      .required('雇用保険/一人親方労災保険の種類が必要です'),

    insuranceNumber: yup
      .string()
      .required('雇用保険/一人親方労災保険番号が必要です')
      .max(20, '雇用保険/一人親方労災保険番号は20文字以下でなければなりません'),

    otherQualifications: yup
      .string()
      .required('免許・資格・教育が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `免許・資格・教育は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),
  });
};

// API Request type for combined edit
export interface CombinedEditContractedWorkerRequest {
  // Profile section
  firstName: string;
  firstNameKata: string;
  lastName: string;
  lastNameKata: string;
  birthDate: string;
  gender: string;
  address: string;
  phone: string;
  bloodType: string;
  employmentDate: string;
  yearsOfExperience: number;
  jobTitle: string;

  // Other information section
  emergencyContact: {
    name: string;
    relationship: string;
    address: string;
    phone: string;
  };
  specialMedicalExamDate?: string | null;
  insuranceExamDate: string;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  employmentInsurance: {
    type: string;
    number: string;
  };
  licenseCertEducation: string;
}

// Helper function to convert combined form values to API request format
export const convertCombinedFormToApiRequest = (
  values: CombinedWorkerEditFormValues,
): CombinedEditContractedWorkerRequest => {
  return {
    // Profile section
    firstName: values.firstName,
    firstNameKata: values.firstNameKata,
    lastName: values.lastName,
    lastNameKata: values.lastNameKata,
    birthDate: dayjs(values.birthDate).format('YYYY-MM-DD'),
    gender: values.gender,
    address: values.address,
    phone: values.phoneNumber,
    bloodType: values.bloodType,
    employmentDate: dayjs(values.employmentDate).format('YYYY-MM-DD'),
    yearsOfExperience: values.yearsOfExperience,
    jobTitle: values.jobTitle,

    // Other information section
    emergencyContact: {
      name: values.emergencyContact,
      relationship: values.relationship,
      address: values.emergencyAddress,
      phone: values.emergencyPhoneNumber,
    },
    specialMedicalExamDate: values.specialMedicalExamDate
      ? dayjs(values.specialMedicalExamDate).format('YYYY-MM-DD')
      : null,
    insuranceExamDate: dayjs(values.insuranceExamDate).format('YYYY-MM-DD'),
    bloodPressure: values.bloodPressure,
    healthInsurance: values.healthInsurance,
    pensionInsurance: values.pensionInsurance,
    employmentInsurance: {
      type: values.insuranceType,
      number: values.insuranceNumber,
    },
    licenseCertEducation: values.otherQualifications,
  };
};
