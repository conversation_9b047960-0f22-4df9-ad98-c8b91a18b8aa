import { Box, Card, Divider, Flex, Text } from '@mantine/core';
import {
  CombinedField,
  DateInputField,
  SelectField,
  TextField,
} from 'components/Form';
import TextareaField from 'components/Form/TextareaField';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  BLOOD_TYPE_OPTIONS,
  DateFormat,
  GENDER_OPTION,
  INSURANCE_TYPE_OPTIONS,
} from 'utils/constants';

import type { CombinedWorkerEditFormValues } from './schema';

interface CombinedWorkerEditFormProps {
  jobType?: string;
}

const CombinedWorkerEditForm: React.FC<CombinedWorkerEditFormProps> = ({
  jobType,
}) => {
  const { control, watch, setValue } =
    useFormContext<CombinedWorkerEditFormValues>();

  // Watch birthDate to automatically calculate age
  const birthDate = watch('birthDate');

  // Automatically calculate age when birthDate changes
  useEffect(() => {
    if (birthDate) {
      const age = dayjs().diff(dayjs(birthDate), 'year');
      setValue('age', age);
    }
  }, [birthDate, setValue]);

  const isSpecialExamRequired =
    jobType &&
    ['塗装工事', '溶接工事', 'Nuclear power plant'].includes(jobType);

  return (
    <Box w="100%">
      {/* Profile Section */}
      <Card
        mb={24}
        sx={(theme) => ({
          borderRadius: 10,
          [theme.fn.smallerThan('sm')]: {
            borderRadius: 0,
          },
        })}
      >
        <Flex
          align="center"
          justify="space-between"
          pb={20}
          pt={16}
          px={{ base: 16, sm: 24 }}
          sx={(theme) => ({
            borderBottom: `1px solid ${theme.colors['gray-300']}`,
          })}
        >
          <Text fw={700} fz={18} lh="26px">
            プロフィール編集
          </Text>
        </Flex>
        <Divider color="gray-400" />
        <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
          <Flex direction="column" gap={24}>
            <TextField
              control={control}
              label="姓"
              name="lastName"
              placeholder="姓"
              required
            />
            <TextField
              control={control}
              label="名"
              name="firstName"
              placeholder="名"
              required
            />
            <TextField
              control={control}
              label="フリガナ(姓)"
              name="lastNameKata"
              placeholder="フリガナ"
              required
            />
            <TextField
              control={control}
              label="フリガナ(名)"
              name="firstNameKata"
              placeholder="フリガナ"
              required
            />
            <DateInputField
              control={control}
              label="生年月日"
              maxDate={dayjs().subtract(18, 'year').toDate()}
              minDate={dayjs().subtract(100, 'year').toDate()}
              name="birthDate"
              placeholder="yyyy年mm月dd日"
              required
              valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
            />
            <TextField
              control={control}
              label="年齢"
              name="age"
              placeholder="年齢"
              readOnly
              required
            />
            <SelectField
              control={control}
              data={GENDER_OPTION}
              label="性別"
              name="gender"
              placeholder="性別"
              required
            />
            <TextareaField
              control={control}
              label="現住所"
              name="address"
              placeholder="現住所"
              required
            />
            <TextField
              control={control}
              format="###-####-####"
              label="電話番号"
              name="phoneNumber"
              placeholder="080-1234-5678"
            />
            <SelectField
              control={control}
              data={BLOOD_TYPE_OPTIONS}
              label="血液型"
              name="bloodType"
              placeholder="Type A"
              required
            />
            <DateInputField
              control={control}
              label="雇用年月日"
              name="employmentDate"
              placeholder="yyyy年mm月dd日"
              required
              valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
            />
            <TextField
              control={control}
              label="経験年数"
              name="yearsOfExperience"
              placeholder="経験年数"
              required
            />
            <TextField
              control={control}
              label="職種"
              name="jobTitle"
              placeholder="職種を入力してください"
              required
            />
          </Flex>
        </Box>
      </Card>

      {/* Other Information Section */}
      <Card
        sx={(theme) => ({
          borderRadius: 10,
          [theme.fn.smallerThan('sm')]: {
            borderRadius: 0,
          },
        })}
      >
        <Flex
          align="center"
          justify="space-between"
          pb={20}
          pt={16}
          px={{ base: 16, sm: 24 }}
          sx={(theme) => ({
            borderBottom: `1px solid ${theme.colors['gray-300']}`,
          })}
        >
          <Text fw={700} fz={18} lh="26px">
            その他の情報の編集
          </Text>
        </Flex>
        <Divider color="gray-400" />
        <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
          <Flex direction="column" gap={24}>
            <TextField
              control={control}
              label="緊急連絡先"
              name="emergencyContact"
              placeholder="お名前"
              required
            />
            <TextField
              control={control}
              label="続柄"
              name="relationship"
              placeholder="続柄"
              required
            />
            <TextField
              control={control}
              label="現住所"
              name="emergencyAddress"
              placeholder="現住所"
              required
            />
            <TextField
              control={control}
              format="###-####-####"
              label="電話番号"
              name="emergencyPhoneNumber"
              placeholder="080-1234-5678"
            />
            <Box>
              <DateInputField
                clearable
                control={control}
                label="特殊健康診断日ー種類"
                name="specialMedicalExamDate"
                placeholder="yyyy年mm月dd日"
                required={!!isSpecialExamRequired}
                subTitle={
                  isSpecialExamRequired
                    ? '原子力発電所に応募される方、溶接工の方は必ずご入力ください'
                    : undefined
                }
                valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
              />
            </Box>
            <DateInputField
              control={control}
              label="最近の保険診断日"
              name="insuranceExamDate"
              placeholder="yyyy年mm月dd日"
              required
              valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
            />
            <TextField
              control={control}
              label="血圧"
              name="bloodPressure"
              placeholder="120"
              required
            />
            <TextField
              control={control}
              label="健康保険"
              name="healthInsurance"
              placeholder="健康保険"
              required
            />
            <TextField
              control={control}
              label="年金保険"
              name="pensionInsurance"
              placeholder="年金保険"
              required
            />
            <CombinedField
              control={control}
              label="雇用保険/一人親方労災保険番号"
              maxLength={20}
              placeholder="123456789"
              required
              selectData={INSURANCE_TYPE_OPTIONS}
              selectName="insuranceType"
              selectPlaceholder="雇用保険"
              textName="insuranceNumber"
            />
            <TextField
              control={control}
              label="免許・資格・教育"
              name="otherQualifications"
              placeholder="免許・資格・教育"
              required
            />
          </Flex>
        </Box>
      </Card>
    </Box>
  );
};

export default CombinedWorkerEditForm;
