import { Box, Text } from '@mantine/core';

const InfoItem = ({
  label,
  value,
}: {
  label: string;
  value?: React.ReactNode;
}) => {
  if (!value) return null;

  return (
    <Box mb={16}>
      <Text color="gray-600" fw={700} fz="sm" mb={4}>
        {label}
      </Text>
      {typeof value === 'string' ? (
        <Text color="black" fw={400} fz={14} lh="20px">
          {value}
        </Text>
      ) : (
        value
      )}
    </Box>
  );
};
export default InfoItem;
