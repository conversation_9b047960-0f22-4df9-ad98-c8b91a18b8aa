import IconEdit from '@icons/icon-edit.svg';
import {
  ActionIcon,
  Box,
  Card,
  Divider,
  Flex,
  LoadingOverlay,
  Text,
} from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import DocumentSection from 'components/DocumentSection';
import Layout from 'components/Layout';
import { useFetch } from 'hooks';
import jobQuery from 'models/job';
import router, { useRouter } from 'next/router';
import React from 'react';
import dayjs from 'utils/dayjs';
import { formatGenderJpText } from 'utils/helpers';
import type { IError } from 'utils/type';

import InfoItem from './InfoItem';

// Type for contracted worker detail from API
interface IContractedWorkerDetail {
  _id: string;
  firstName: string;
  firstNameKata: string;
  lastName: string;
  lastNameKata: string;
  birthDate: string;
  gender: string;
  address: string;
  phone: string;
  bloodType: string;
  employmentDate: string;
  yearsOfExperience: number;
  jobTitle: string;
  emergencyContact: {
    name: string;
    relationship: string;
    address: string;
    phone: string;
  };
  specialMedicalExamDate?: string;
  insuranceExamDate: string;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  employmentInsurance: {
    type: string;
    number: string;
  };
  licenseCertEducation: string;
  applicationDocuments?: Array<{
    _id: string;
    key: string;
    originalName: string;
    originUrl: string;
    url: string;
  }>;
  // Categorized document fields from API
  identificationCard?: {
    frontKey?: string;
    backKey?: string;
    frontDocument?: {
      _id: string;
      key: string;
      originalName: string;
      originUrl: string;
      url: string;
    };
    backDocument?: {
      _id: string;
      key: string;
      originalName: string;
      originUrl: string;
      url: string;
    };
  };
  licensesKeys?: string[];
  licenses?: Array<{
    _id: string;
    key: string;
    originalName: string;
    originUrl: string;
    url: string;
  }>;
  healthInsuranceCard?: {
    frontKey?: string;
    backKey?: string;
    frontDocument?: {
      _id: string;
      key: string;
      originalName: string;
      originUrl: string;
      url: string;
    };
    backDocument?: {
      _id: string;
      key: string;
      originalName: string;
      originUrl: string;
      url: string;
    };
  };
  healthExamReportKeys?: string[];
  healthExamReports?: Array<{
    _id: string;
    key: string;
    originalName: string;
    originUrl: string;
    url: string;
  }>;
}

const WorkerDetailPage = () => {
  const { query } = useRouter();
  const workerId = typeof query.workerId === 'string' ? query.workerId : '';
  const jobId = typeof query.id === 'string' ? query.id : '';

  // Fetch worker data
  const { data: worker, isLoading } = useFetch<IContractedWorkerDetail>({
    ...jobQuery.getContractedWorkerDetail({
      matchingId: jobId,
      contractedWorkerId: workerId,
    }),
    enabled: !!workerId && !!jobId,
    refetchOnMount: 'always',
    refetchOnWindowFocus: true, // Changed to true to refetch when page becomes focused
    cacheTime: 0,
    meta: {
      notToastErrorCodes: [400],
    },
    onError: (error: unknown) => {
      const { error: err, statusCode } = error as IError;
      if (err === 'OBJECT_NOT_FOUND' && statusCode === 400) {
        router.replace('/not-found');
      }
    },
  });

  // Fetch job matching data to determine status
  const { data: jobMatching } = useFetch({
    ...jobQuery.getMatchingDetail(jobId),
    enabled: !!jobId,
  });

  // Determine if document section should be hidden
  const shouldHideDocumentSection = React.useMemo(() => {
    // Hide document section when status is COLLECTING
    return (jobMatching as any)?.status === 'COLLECTING';
  }, [jobMatching]);

  // Helper function to format birthday
  const formatBirthday = (birthDate: string | undefined) => {
    if (!birthDate) return '';
    return dayjs(birthDate).format('YYYY年MM月DD日');
  };

  // Helper function to calculate age
  const calculateAge = (birthDate: string | undefined) => {
    if (!birthDate) return '';
    const age = dayjs().diff(dayjs(birthDate), 'year');
    return `${age}歳`;
  };

  // Helper function to return documents or mockup
  const getOrMockDocuments = (docs: any[] | undefined, mockKey: string) => {
    if (docs && docs.length > 0) return docs;
    return [
      {
        key: mockKey,
        originUrl: '',
        originalName: 'まだファイルがアップロードされていません',
        url: '',
        _id: 'mockup',
      },
    ];
  };

  // Helper function to prepare ID documents
  const getIdDocuments = () => {
    const documents = [];

    if ((worker as any)?.identificationCard?.front) {
      documents.push({
        ...(worker as any).identificationCard.front,
        originalName:
          (worker as any).identificationCard.front.originalName ||
          '表面(Front).PNG',
      });
    }

    if ((worker as any)?.identificationCard?.back) {
      documents.push({
        ...(worker as any).identificationCard.back,
        originalName:
          (worker as any).identificationCard.back.originalName || '-',
      });
    }

    return getOrMockDocuments(documents, 'id_card_mockup');
  };

  // Helper function to prepare license documents
  const getLicenseDocuments = () => {
    return getOrMockDocuments(worker?.licenses, 'license_mockup');
  };

  // Helper function to prepare health insurance documents
  const getHealthInsuranceDocuments = () => {
    const documents = [];

    if ((worker as any)?.healthInsuranceCard?.front) {
      documents.push({
        ...(worker as any).healthInsuranceCard.front,
        originalName:
          (worker as any).healthInsuranceCard.front.originalName || '-',
      });
    }

    if ((worker as any)?.healthInsuranceCard?.back) {
      documents.push({
        ...(worker as any).healthInsuranceCard.back,
        originalName:
          (worker as any).healthInsuranceCard.back.originalName || '-',
      });
    }

    return getOrMockDocuments(documents, 'health_insurance_mockup');
  };

  // Helper function to prepare health exam documents
  const getHealthExamDocuments = () => {
    if (worker?.healthExamReports && worker.healthExamReports.length > 0) {
      return worker.healthExamReports;
    }

    // Return mockup data if no real data available
    return [
      {
        key: 'healthExamDocuments',
        originUrl: '',
        originalName: 'まだファイルがアップロードされていません',
        url: '',
        _id: 'mockup',
      },
    ];
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <Box h={400}>
          <LoadingOverlay visible />
        </Box>
      );
    }

    return (
      <Box>
        {/* Profile Section */}
        <Card
          mb={24}
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              borderRadius: 0,
            },
          })}
          w="100%"
        >
          <Flex
            align="center"
            justify="space-between"
            pb={20}
            pt={16}
            px={{ base: 16, sm: 24 }}
          >
            <Text fw={700} fz={18} lh="26px">
              {worker?.lastName || ''} {worker?.firstName || ''}
            </Text>
          </Flex>
          <Divider color="gray-400" />
          <Flex
            align="center"
            justify="space-between"
            pb={20}
            pt={16}
            px={{ base: 16, sm: 24 }}
          >
            <Text fw={700} fz={18} lh="26px">
              プロフィール
            </Text>
            <ActionIcon
              color="primary"
              component="a"
              href={`/my-page/jobs/${jobId}/workers/${
                worker?._id || workerId
              }/edit`}
              size="sm"
              variant="subtle"
            >
              <IconEdit height={32} width={32} />
            </ActionIcon>
          </Flex>
          <Divider color="gray-400" />
          <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
            <InfoItem
              label="氏名"
              value={`${worker?.lastName || ''} ${worker?.firstName || ''}`}
            />
            <InfoItem
              label="フリガナ"
              value={`${worker?.lastNameKata || ''} ${
                worker?.firstNameKata || ''
              }`}
            />
            <InfoItem
              label="生年月日"
              value={formatBirthday(worker?.birthDate)}
            />
            <InfoItem label="年齢" value={calculateAge(worker?.birthDate)} />
            <InfoItem
              label="性別"
              value={
                worker?.gender
                  ? formatGenderJpText(worker.gender as 'MALE' | 'FEMALE')
                  : '-'
              }
            />
            <InfoItem label="現住所" value={worker?.address || '-'} />
            <InfoItem label="電話番号" value={worker?.phone || '-'} />
            <InfoItem label="血液型" value={worker?.bloodType || '-'} />
            <InfoItem
              label="雇用年月日"
              value={
                worker?.employmentDate
                  ? dayjs(worker.employmentDate).format('YYYY年MM月DD日')
                  : '-'
              }
            />
            <InfoItem
              label="経験年数"
              value={
                worker?.yearsOfExperience
                  ? `${worker.yearsOfExperience}年`
                  : '-'
              }
            />
            <InfoItem label="職種" value={worker?.jobTitle || '-'} />
          </Box>
        </Card>

        {/* Other Information Section */}
        <Card
          mb={24}
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              borderRadius: 0,
            },
          })}
          w="100%"
        >
          <Flex
            align="center"
            justify="space-between"
            pb={20}
            pt={16}
            px={{ base: 16, sm: 24 }}
          >
            <Text fw={700} fz={18} lh="26px">
              その他の情報
            </Text>
            <ActionIcon
              color="primary"
              component="a"
              href={`/my-page/jobs/${jobId}/workers/${
                worker?._id || workerId
              }/edit-other`}
              size="sm"
              variant="subtle"
            >
              <IconEdit height={32} width={32} />
            </ActionIcon>
          </Flex>
          <Divider color="gray-400" />
          <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
            <InfoItem
              label="緊急連絡先"
              value={
                worker?.emergencyContact ? (
                  <>
                    <Text color="black" fw={400} fz={14} lh="20px">
                      {worker.emergencyContact.name} ·{' '}
                      {worker.emergencyContact.relationship}
                    </Text>
                    <Text color="black" fw={400} fz={14} lh="20px">
                      {worker.emergencyContact.address} ·{' '}
                      {worker.emergencyContact.phone}
                    </Text>
                  </>
                ) : (
                  '-'
                )
              }
            />
            <InfoItem
              label="特殊健康診断日一種類"
              value={
                worker?.specialMedicalExamDate
                  ? dayjs(worker.specialMedicalExamDate).format(
                      'YYYY年MM月DD日',
                    )
                  : '-'
              }
            />
            <InfoItem
              label="最近の保険診断日"
              value={
                worker?.insuranceExamDate
                  ? dayjs(worker.insuranceExamDate).format('YYYY年MM月DD日')
                  : '-'
              }
            />
            <InfoItem label="血圧" value={worker?.bloodPressure || '-'} />
            <InfoItem label="健康保険" value={worker?.healthInsurance || '-'} />
            <InfoItem
              label="年金保険"
              value={worker?.pensionInsurance || '-'}
            />
            <InfoItem
              label="雇用保険番号/一人親方労災保険番号"
              value={
                worker?.employmentInsurance
                  ? `${worker.employmentInsurance.type} - ${worker.employmentInsurance.number}`
                  : '-'
              }
            />
            <InfoItem
              label="免許・資格・教育"
              value={worker?.licenseCertEducation || '-'}
            />
          </Box>
        </Card>

        {/* Documents Section , Hide for COLLECTING status */}
        {!shouldHideDocumentSection && (
          <Card
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                borderRadius: 0,
              },
            })}
            w="100%"
          >
            <Flex
              align="center"
              justify="space-between"
              pb={20}
              pt={16}
              px={{ base: 16, sm: 24 }}
            >
              <Text fw={700} fz={18} lh="26px">
                提出書類
              </Text>
              <ActionIcon
                color="primary"
                component="a"
                href={`/my-page/jobs/${jobId}/workers/${
                  worker?._id || workerId
                }/edit-documents`}
                size="sm"
                variant="subtle"
              >
                <IconEdit height={32} width={32} />
              </ActionIcon>
            </Flex>
            <Divider color="gray-400" />
            <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
              {/* ID Documents */}
              <DocumentSection
                files={getIdDocuments()}
                isWrapped={false}
                title="①身分証"
              />

              {/* Certification Documents */}
              <DocumentSection
                files={getLicenseDocuments()}
                isWrapped={true}
                title="②資格証"
              />

              {/* Health Insurance Documents */}
              <DocumentSection
                files={getHealthInsuranceDocuments()}
                isWrapped={false}
                title="③健康保険証"
              />

              {/* Health Check Documents */}
              <DocumentSection
                files={getHealthExamDocuments()}
                isWrapped={true}
                title="④健康診断証"
              />
            </Box>
          </Card>
        )}
      </Box>
    );
  };

  return (
    <Box
      maw={784}
      mx="auto"
      sx={(theme) => ({
        [theme.fn.smallerThan('sm')]: {
          borderRadius: 0,
        },
      })}
      w="100%"
    >
      <Box pos="relative" px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
        {renderContent()}
        <LoadingOverlay loaderProps={{ size: 'lg' }} visible={isLoading} />
      </Box>
    </Box>
  );
};

WorkerDetailPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default WorkerDetailPage;
