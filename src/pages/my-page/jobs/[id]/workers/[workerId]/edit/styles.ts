import { createStyles } from '@mantine/core';

const useStyles = createStyles({
  customClassUpload: {
    minHeight: 200,
    '.customClassUploadFile': {
      height: 200,
      alignItems: 'center',
      '> div': {
        minHeight: 200,
        borderRadius: 8,
      },
    },
    '.uploadMediaItem': {
      height: 200,
    },
    '.inlineError': {
      marginBottom: 8,
    },
  },
  customClassMultiUpload: {
    height: '100%',
    '.customClassMultiUpload': {
      '.inlineError': {
        marginBottom: 0,
      },
      '.uploadMediaItem': {
        width: '100%',
        minHeight: 162,
        '.mantine-CloseButton-root': {
          top: 6.5,
          right: 6.5,
        },
      },
    },
  },
  link: {
    fontSize: 14,
    overflow: 'hidden',
    color: '#0162D1',
    '&:hover': {
      textDecoration: 'underline',
    },
  },
});
export default useStyles;
