import { Box, Flex, Text } from '@mantine/core';
import { DateInputField, SelectField, TextField } from 'components/Form';
import TextareaField from 'components/Form/TextareaField';
import dayjs from 'dayjs';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { BLOOD_TYPE_OPTIONS, DateFormat, GENDER_OPTION } from 'utils/constants';

import type { ContractedWorkerEditFormValues } from './schema';

const ContractedWorkerEditForm = () => {
  const { control, watch, setValue } =
    useFormContext<ContractedWorkerEditFormValues>();

  // Watch birthDate to automatically calculate age
  const birthDate = watch('birthDate');

  // Automatically calculate age when birthDate changes
  React.useEffect(() => {
    if (birthDate) {
      const age = dayjs().diff(dayjs(birthDate), 'year');
      setValue('age', age);
    }
  }, [birthDate, setValue]);

  return (
    <Box component="form" w={'100%'}>
      {/* Section 1: Profile */}
      <Text color="black" fw={700} fz={20} mb={24}>
        プロフィール
      </Text>
      <Flex direction={'column'} gap={24} mb={40}>
        <Flex
          align={'flex-start'}
          gap={16}
          justify="stretch"
          sx={{ '& > *': { flex: 1 } }}
        >
          <TextField
            control={control}
            label="姓"
            name="lastName"
            placeholder="姓"
            required
          />
          <TextField
            control={control}
            label="名"
            name="firstName"
            placeholder="名"
            required
          />
        </Flex>
        <Flex align={'flex-start'} gap={16} sx={{ '& > *': { flex: 1 } }}>
          <TextField
            control={control}
            label="フリガナ(姓)"
            name="lastNameKata"
            placeholder="フリガナ"
            required
          />
          <TextField
            control={control}
            label="フリガナ(名)"
            name="firstNameKata"
            placeholder="フリガナ"
            required
          />
        </Flex>
        <DateInputField
          control={control}
          label="生年月日"
          maxDate={dayjs().subtract(18, 'year').toDate()}
          minDate={dayjs().subtract(100, 'year').toDate()}
          name="birthDate"
          placeholder="yyyy年mm月dd日"
          required
          valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
        />
        <TextField
          control={control}
          label="年齢"
          name="age"
          placeholder="年齢"
          readOnly
          required
        />
        <SelectField
          control={control}
          data={GENDER_OPTION}
          label="性別"
          name="gender"
          placeholder="性別"
          required
        />
        <TextareaField
          control={control}
          label="現住所"
          name="address"
          placeholder="現住所"
          required
        />
        <TextField
          control={control}
          format="###-####-####"
          label="電話番号"
          name="phoneNumber"
          placeholder="080-1234-5678"
          required
        />
        <SelectField
          control={control}
          data={BLOOD_TYPE_OPTIONS}
          label="血液型"
          name="bloodType"
          placeholder="Type A"
          required
        />
        <DateInputField
          control={control}
          label="雇用年月日"
          name="employmentDate"
          placeholder="yyyy年mm月dd日"
          required
          valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
        />
        <TextField
          control={control}
          label="経験年数"
          name="yearsOfExperience"
          placeholder="経験年数"
          required
        />
        <TextField
          control={control}
          label="職種"
          name="jobTitle"
          placeholder="職種を入力してください"
          required
        />
      </Flex>
    </Box>
  );
};

export default ContractedWorkerEditForm;
