import dayjs from 'dayjs';
import { REG_EXP } from 'utils/constants';
import * as yup from 'yup';

// Form values interface for edit form (profile section only)
export interface ContractedWorkerEditFormValues {
  // Section 1: Profile
  firstName: string;
  lastName: string;
  firstNameKata: string;
  lastNameKata: string;
  birthDate: Date;
  age: number;
  gender: string;
  address: string;
  phoneNumber: string;
  bloodType: string;
  employmentDate: Date;
  yearsOfExperience: number;
  jobTitle: string;
}

const NAME_MAX_LENGTH = 10;
const GENERAL_MAX_LENGTH = 200;

// Schema for edit form (profile section only)
const schema = yup.object().shape({
  // Section 1: Profile
  firstName: yup
    .string()
    .trim()
    .required('名が必要です')
    .max(NAME_MAX_LENGTH, '名は10文字以下でなければなりません')
    .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

  lastName: yup
    .string()
    .trim()
    .required('姓が必要です')
    .max(NAME_MAX_LENGTH, '姓は10文字以下でなければなりません')
    .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

  firstNameKata: yup
    .string()
    .trim()
    .required('フリガナ(名)が必要です')
    .max(NAME_MAX_LENGTH, 'フリガナ(名)は10文字以下でなければなりません')
    .matches(REG_EXP.KATAKANA, '無効な形式です。'),

  lastNameKata: yup
    .string()
    .trim()
    .required('フリガナ(姓)が必要です')
    .max(NAME_MAX_LENGTH, 'フリガナ(姓)は10文字以下でなければなりません')
    .matches(REG_EXP.KATAKANA, '無効な形式です。'),

  birthDate: yup
    .date()
    .required('生年月日が必要です')
    .test('min-age', '18歳以上でなければなりません', (value) => {
      if (!value) return true;
      const today = new Date();
      const birthDate = new Date(value);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      // Check if birthday has occurred this year
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < birthDate.getDate())
      ) {
        return age - 1 >= 18;
      }
      return age >= 18;
    }),

  age: yup.number(),

  gender: yup.string().required('性別が必要です'),

  address: yup
    .string()
    .required('住所が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  phoneNumber: yup
    .string()
    .required('電話番号が必要です')
    .min(9, '電話番号は9文字以上でなければなりません')
    .max(11, '電話番号は11文字以下でなければなりません')
    .matches(/^[0-9-]+$/, '電話番号は数字とハイフンのみを含めることができます'),

  bloodType: yup.string().required('血液型が必要です'),

  employmentDate: yup.date().required('雇用年月日が必要です'),

  yearsOfExperience: yup
    .number()
    .required('経験年数が必要です')
    .min(0, '経験年数は0年以上でなければなりません')
    .max(100, '無効な形式です。')
    .test('digit-format', '無効な形式です。', (value) => {
      if (value === undefined || value === null) return true;
      // Check if the number has more than 2 digits before decimal point
      const wholePart = Math.floor(Math.abs(value)).toString();
      return wholePart.length <= 2;
    })
    .test('decimal-places', '無効な形式です。', (value) => {
      if (value === undefined || value === null) return true;
      // Check if the number has more than 1 decimal place
      const decimalPlaces = (value.toString().split('.')[1] || '').length;
      return decimalPlaces <= 1;
    })
    .typeError('無効な形式です。'),

  jobTitle: yup
    .string()
    .required('職種が必要です')
    .min(1, '少なくとも一つの職種を選択してください'),
});

export default schema;

// API Request type for edit (profile section only)
export interface EditContractedWorkerRequest {
  firstName: string;
  firstNameKata: string;
  lastName: string;
  lastNameKata: string;
  birthDate: string; // ISO date string
  gender: string;
  address: string;
  phone: string;
  bloodType: string;
  employmentDate: string; // ISO date string
  yearsOfExperience: number;
  jobTitle: string;
}

// Helper function to convert edit form values to API request format
export const convertEditFormToApiRequest = (
  values: ContractedWorkerEditFormValues,
): EditContractedWorkerRequest => {
  return {
    firstName: values.firstName,
    firstNameKata: values.firstNameKata,
    lastName: values.lastName,
    lastNameKata: values.lastNameKata,
    birthDate: dayjs(values.birthDate).format('YYYY-MM-DD'),
    gender: values.gender,
    address: values.address,
    phone: values.phoneNumber,
    bloodType: values.bloodType,
    employmentDate: dayjs(values.employmentDate).format('YYYY-MM-DD'),
    yearsOfExperience: values.yearsOfExperience,
    jobTitle: values.jobTitle,
  };
};
