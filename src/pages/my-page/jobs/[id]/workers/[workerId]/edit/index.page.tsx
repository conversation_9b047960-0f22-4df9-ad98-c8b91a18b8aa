import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Flex, LoadingOverlay, Text } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import CardLayout from 'components/CardLayout';
import Layout from 'components/Layout';
import { useFetch, useMutate } from 'hooks';
import jobQuery from 'models/job';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';
import queryClient from 'utils/queryClient';

import ContractedWorkerEditForm from './ContractedWorkerEditForm';
import schema, {
  type ContractedWorkerEditFormValues,
  convertEditFormToApiRequest,
} from './schema';

// Type for contracted worker detail from API
interface IContractedWorkerDetail {
  _id: string;
  firstName: string;
  firstNameKata: string;
  lastName: string;
  lastNameKata: string;
  birthDate: string;
  gender: string;
  address: string;
  phone: string;
  bloodType: string;
  employmentDate: string;
  yearsOfExperience: number;
  jobTitle: string;
  emergencyContact: {
    name: string;
    relationship: string;
    address: string;
    phone: string;
  };
  specialMedicalExamDate?: string;
  insuranceExamDate: string;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  employmentInsurance: {
    type: string;
    number: string;
  };
  licenseCertEducation: string;
}

const ContractedWorkerEditPage = () => {
  const router = useRouter();
  const { id: jobId, workerId } = router.query;

  // Fetch worker data for initial values
  const { data: worker, isLoading: isLoadingWorker } =
    useFetch<IContractedWorkerDetail>({
      ...jobQuery.getContractedWorkerDetail({
        matchingId: jobId as string,
        contractedWorkerId: workerId as string,
      }),
      enabled: !!jobId && !!workerId,
    });

  const methods = useForm<ContractedWorkerEditFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  // Set initial values when worker data is loaded
  useEffect(() => {
    if (worker) {
      // Set profile values only (since we're only editing profile section)
      methods.setValue('firstName', worker.firstName || '');
      methods.setValue('lastName', worker.lastName || '');
      methods.setValue('firstNameKata', worker.firstNameKata || '');
      methods.setValue('lastNameKata', worker.lastNameKata || '');

      if (worker.birthDate) {
        const birthDateValue = dayjs(worker.birthDate).toDate();
        methods.setValue('birthDate', birthDateValue);
        const age = dayjs().diff(dayjs(worker.birthDate), 'year');
        methods.setValue('age', age);
      }

      methods.setValue('gender', worker.gender || '');
      methods.setValue('address', worker.address || '');
      methods.setValue('phoneNumber', worker.phone || '');
      methods.setValue('bloodType', worker.bloodType || '');

      if (worker.employmentDate) {
        methods.setValue(
          'employmentDate',
          dayjs(worker.employmentDate).toDate(),
        );
      }

      methods.setValue('yearsOfExperience', worker.yearsOfExperience || 0);
      methods.setValue('jobTitle', worker.jobTitle || '');
    }
  }, [worker, methods]);

  const { mutateAsync: updateContractedWorker, isLoading: isSubmitting } =
    useMutate({
      ...jobQuery.editContractedWorker({
        matchingId: jobId as string,
        contractedWorkerId: workerId as string,
      }),
      onSuccess: async () => {
        helpers.toast({
          message: '完了しました。',
        });

        // Invalidate worker detail cache to ensure fresh data when navigating back
        await queryClient.invalidateQueries([
          'worker',
          jobId as string,
          workerId as string,
        ]);

        setTimeout(() => {
          router.back();
        }, 300);
      },
    });

  const handleCancel = () => {
    router.back();
  };

  const onSubmit: SubmitHandler<ContractedWorkerEditFormValues> = async (
    values,
  ) => {
    helpers.confirm({
      title: '内容を保存しますか？',
      children: (
        <Text color="black" fz="16px" lh="24px" p={24}>
          確定する場合は、保存してください。
        </Text>
      ),
      labels: { confirm: '保存する', cancel: 'キャンセル' },
      onConfirm: async () => {
        try {
          // Convert form values to API request format using helper function
          const apiRequest = convertEditFormToApiRequest(values);

          await updateContractedWorker(apiRequest);
        } catch (error) {
          // Error will be handled by the mutation hook
        }
      },
    });
  };

  if (isLoadingWorker) {
    return (
      <Box
        maw={784}
        mx="auto"
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            borderRadius: 0,
          },
        })}
        w="100%"
      >
        <Box pos="relative" px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
          <LoadingOverlay loaderProps={{ size: 'lg' }} visible={true} />
        </Box>
      </Box>
    );
  }

  return (
    <FormProvider {...methods}>
      <Box pb={{ base: 0, sm: 64 }} pos="relative" pt={{ base: 24, sm: 32 }}>
        <CardLayout
          cancelButtonProps={{ onClick: handleCancel }}
          footerProps={{
            sx: (theme) => ({
              borderTop: `1px solid ${theme.colors['gray-300']}`,
              [theme.fn.smallerThan('sm')]: {
                display: 'none',
              },
            }),
          }}
          submitButtonProps={{
            onClick: methods.handleSubmit(onSubmit),
            loading: isSubmitting,
            disabled: !methods.formState.isValid,
          }}
          title="登録作業者編集"
        >
          <ContractedWorkerEditForm />
        </CardLayout>
        <Flex
          bg="white"
          gap={8}
          justify="flex-end"
          mt={42}
          px={16}
          py={12}
          sx={(theme) => ({
            boxShadow: theme.shadows.sm,
            position: 'sticky',
            bottom: 0,
            gap: 8,
            '& > *': {
              flex: 1,
            },
            [theme.fn.largerThan('sm')]: {
              display: 'none',
            },
          })}
        >
          <Button miw={100} onClick={handleCancel} variant="outline">
            キャンセル
          </Button>
          <Button
            loading={isSubmitting}
            miw={100}
            onClick={methods.handleSubmit(onSubmit)}
          >
            保存する
          </Button>
        </Flex>
      </Box>
    </FormProvider>
  );
};

ContractedWorkerEditPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb
        hiddenPaths={['/my-page/jobs/[id]', '/my-page/jobs/[id]/workers']}
      />
      {page}
    </Layout>
  );
};

export default ContractedWorkerEditPage;
