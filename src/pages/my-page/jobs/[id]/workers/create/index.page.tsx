import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Flex, Text } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import CardLayout from 'components/CardLayout';
import ContractedWorkerForm from 'components/ContractedWorker/ContractedWorkerForm';
import type { ContractedWorkerFormValues } from 'components/ContractedWorker/ContractedWorkerForm/schema';
import {
  convertFormToApiRequest,
  createContractedWorkerSchema,
} from 'components/ContractedWorker/ContractedWorkerForm/schema';
import Layout from 'components/Layout';
import dayjs from 'dayjs';
import { useFetch, useMutate } from 'hooks';
import jobQuery from 'models/job';
import { useRouter } from 'next/router';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import helpers from 'utils/helpers';

const defaultValues: ContractedWorkerFormValues = {
  firstName: '',
  lastName: '',
  firstNameKata: '',
  lastNameKata: '',
  birthDate: dayjs().subtract(18, 'year').toDate(), // Default to 18 years old
  age: 18, // Default age of 18
  gender: '',
  address: '',
  phoneNumber: '',
  bloodType: '',
  employmentDate: new Date(),
  yearsOfExperience: undefined as unknown as number,
  jobTitle: '',
  emergencyContact: '',
  relationship: '',
  emergencyAddress: '',
  emergencyPhoneNumber: '',
  specialMedicalExamDate: null as unknown as Date,
  insuranceExamDate: new Date(),
  bloodPressure: '',
  healthInsurance: '',
  pensionInsurance: '',
  insuranceType: '',
  insuranceNumber: '',
  otherQualifications: '',
};

const ContractedWorkerCreatePage = () => {
  const router = useRouter();
  const { id: jobId } = router.query;

  // Fetch job matching data to get job type
  const { data: jobMatching } = useFetch<{ jobPostInfo: { jobType: string } }>({
    ...jobQuery.getMatchingDetail(jobId as string),
    enabled: !!jobId,
  });

  // Create dynamic schema based on job post's job type
  const dynamicSchema = createContractedWorkerSchema(
    jobMatching?.jobPostInfo?.jobType ? [jobMatching.jobPostInfo.jobType] : [],
  );

  const methods = useForm<ContractedWorkerFormValues>({
    resolver: yupResolver(dynamicSchema),
    mode: 'onBlur',
    defaultValues,
  });

  const { mutateAsync: createContractedWorker, isLoading: isSubmitting } =
    useMutate({
      ...jobQuery.createContractedWorker(jobId as string),
    });

  const handleCancel = () => {
    if (methods.formState.isDirty) {
      helpers.confirm({
        title: '入力内容を破棄',
        children: (
          <Text color="black" fz="16px" lh="24px" p={24}>
            入力内容を破棄しますか？
          </Text>
        ),
        onConfirm: () => {
          router.back();
        },
      });
    } else {
      router.back();
    }
  };

  const onSubmit: SubmitHandler<ContractedWorkerFormValues> = async (
    values,
  ) => {
    helpers.confirm({
      title: '内容を保存しますか？',
      children: (
        <Text color="black" fz="16px" lh="24px" p={24}>
          確定する場合は、保存してください。
        </Text>
      ),
      labels: { confirm: '保存する', cancel: 'キャンセル' },
      onConfirm: async () => {
        try {
          // Convert form values to API request format using helper function
          const apiRequest = convertFormToApiRequest(values);

          await createContractedWorker(apiRequest);

          router.back();
        } catch (error) {
          // Error will be handled by the mutation hook
        }
      },
    });
  };

  return (
    <FormProvider {...methods}>
      <Box pb={{ base: 0, sm: 64 }} pos="relative" pt={{ base: 24, sm: 32 }}>
        <CardLayout
          cancelButtonProps={{ onClick: handleCancel }}
          footerProps={{
            sx: (theme) => ({
              borderTop: `1px solid ${theme.colors['gray-300']}`,
              [theme.fn.smallerThan('sm')]: {
                display: 'none',
              },
            }),
          }}
          submitButtonProps={{
            onClick: methods.handleSubmit(onSubmit),
            loading: isSubmitting,
            disabled: !methods.formState.isValid,
          }}
          title="登録作業者追加"
        >
          <ContractedWorkerForm jobType={jobMatching?.jobPostInfo?.jobType} />
        </CardLayout>
        <Flex
          bg="white"
          gap={8}
          justify="flex-end"
          mt={42}
          px={16}
          py={12}
          sx={(theme) => ({
            boxShadow: theme.shadows.sm,
            position: 'sticky',
            bottom: 0,
            gap: 8,
            '& > *': {
              flex: 1,
            },
            [theme.fn.largerThan('sm')]: {
              display: 'none',
            },
          })}
        >
          <Button miw={100} onClick={handleCancel} variant="outline">
            キャンセル
          </Button>
          <Button
            loading={isSubmitting}
            miw={100}
            onClick={methods.handleSubmit(onSubmit)}
          >
            保存する
          </Button>
        </Flex>
      </Box>
    </FormProvider>
  );
};

ContractedWorkerCreatePage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb
        hiddenPaths={['/my-page/jobs/[id]', '/my-page/jobs/[id]/workers']}
      />
      {page}
    </Layout>
  );
};

export default ContractedWorkerCreatePage;
