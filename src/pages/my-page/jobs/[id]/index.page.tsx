import { yupResolver } from '@hookform/resolvers/yup';
import IconDownload from '@icons/icon-download.svg';
import IconEdit from '@icons/icon-edit.svg';
import IconFile from '@icons/icon-file.svg';
import {
  ActionIcon,
  Anchor,
  Box,
  Button,
  Flex,
  List,
  LoadingOverlay,
  Text,
} from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import FormLayout from 'components/CardLayout';
import Layout from 'components/Layout';
import JobMatchingItem from 'components/Matching/JobMatchingItem';
import WorkerList from 'components/Matching/WorkerDetailItem';
import MatchingUpdateForm from 'components/MatchingUpdateForm';
import type { MatchingUpdateFormValues } from 'components/MatchingUpdateForm/schema';
import schema from 'components/MatchingUpdateForm/schema';
import type { MatchingWorkerUpdateFormValues } from 'components/MatchingWorkerUpdateForm/schema';
import Modal from 'components/Modal';
import StatusTag from 'components/StatusTag';
import { useFetch, useMutate } from 'hooks';
import jobQuery from 'models/job';
import type { IJobMatchingItem } from 'models/job/type';
import { MATCHING_STATUS } from 'models/job/type';
import router, { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import helpers from 'utils/helpers';
import type { IError } from 'utils/type';

function ApplicationInfoComponent() {
  return (
    <Box mb={16}>
      <Text c="black" mb="md" size={16} weight={700}>
        応募者情報
      </Text>
      <Text mb="sm">
        発注企業へご紹介するために、応募者全員分の以下4点の書類をアップロードしてください。
      </Text>
      <List withPadding>
        <List.Item>身分証</List.Item>
        <List.Item>資格証</List.Item>
        <List.Item>健康保険証</List.Item>
        <List.Item>健康診断証</List.Item>
      </List>
      <Text mb={16} mt="md" size="sm">
        ※書類をアップロードする際の注意点、および撮影のコツについてをご案内いたします。
      </Text>
      <Text size="sm">
        必ずこちらをご確認のうえ撮影、およびアップロードをしてください
      </Text>
      <Anchor
        color="blue"
        href="/uploading-guide"
        size="sm"
        target="_blank"
        underline
      >
        確認書類の撮影・アップロードガイド
      </Anchor>
      <Text mt="md" size="sm">
        ※すべての書類をご提出いただき、最終選考へと進めさせていただきます。
      </Text>
    </Box>
  );
}
const MatchingDetailPage = () => {
  const { query } = useRouter();
  const id = typeof query.id === 'string' ? query.id : '';
  const [modalVisible, setModalVisibility] = useState(false);

  const { data, isLoading, refetch } = useFetch<IJobMatchingItem>({
    ...jobQuery.getMatchingDetail(id),
    enabled: !!id,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false,
    cacheTime: 0,
    meta: {
      notToastErrorCodes: [400],
    },
    onError: (error: unknown) => {
      const { error: err, statusCode } = error as IError;
      if (err === 'OBJECT_NOT_FOUND' && statusCode === 400) {
        router.replace('/not-found');
      }
    },
  });

  const { mutateAsync: updateDocuments, isLoading: isEditing } = useMutate<{
    cvDocumentKeys?: string[];
    workerContracts?: {
      _id: string;
      applicationDocumentKeys: string[];
    }[];
  }>({
    ...jobQuery.updateMatchingDocuments(id),
  });

  const { mutateAsync: openContactAIAssistant, isLoading: isOpenContact } =
    useMutate<
      {
        jobPostId: string;
      },
      IJobMatchingItem
    >(jobQuery.openContactAIAssistant);

  const { mutateAsync: submitMatching, isLoading: isSubmitting } = useMutate({
    ...jobQuery.submitMatching(id),
  });

  const [isDownloading, setIsDownloading] = useState(false);

  const defaultValues: MatchingUpdateFormValues = useMemo(
    () => ({
      cvDocuments: data?.cvDocuments || [],
    }),
    [data],
  );
  const methods = useForm<MatchingUpdateFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
    defaultValues,
  });
  const { reset, formState, handleSubmit } = methods;

  useEffect(() => {
    if (defaultValues && !modalVisible) {
      reset(defaultValues);
    }
  }, [defaultValues, reset, modalVisible]);

  const Header = () => {
    return (
      <Flex
        align={'center'}
        direction={'row'}
        gap={12}
        justify={'flex-start'}
        pb={{ base: 20, sm: 24 }}
        pt={16}
        px={{ base: 16, sm: 24 }}
        sx={(theme) => ({
          borderBottom: `1px solid ${theme.colors['gray-300']}`,
        })}
      >
        <Text fw={700} fz={18} lh="26px">
          案件詳細
        </Text>
        {!!data?.status && (
          <StatusTag
            label={MATCHING_STATUS[data.status]?.label}
            type={MATCHING_STATUS[data.status]?.color}
          />
        )}
      </Flex>
    );
  };

  const handleUpdateMatchingDocuments: SubmitHandler<
    MatchingUpdateFormValues
  > = async (values) => {
    const addedDocuments = values.cvDocuments.map((document) => document.key);
    await updateDocuments({
      cvDocumentKeys: addedDocuments,
    });
    await refetch();
    setModalVisibility(false);
  };

  const handleUpdateWorkerDocuments: SubmitHandler<
    MatchingWorkerUpdateFormValues
  > = async (values) => {
    const addedDocuments = values.applicationDocuments.filter(
      (document) => document.readOnly === false,
    );
    await updateDocuments({
      workerContracts: [
        {
          _id: values._id,
          applicationDocumentKeys: addedDocuments.map(
            (document) => document.key,
          ),
        },
      ],
    });
    await refetch();
  };

  const handleSubmitMatching = async () => {
    await submitMatching({});
    await refetch();
  };

  const handleDownloadWorkerContractsPdf = async () => {
    try {
      setIsDownloading(true);
      await helpers.downloadWorkerContractsPdf(id);
    } catch (error) {
      // Error handling is already done in the helper function
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  // Check if all worker contracts have isDoneDetail = true
  const isSubmitEnabled =
    (data?.workerContracts?.length ?? 0) > 0 &&
    data?.workerContracts?.every((contract) => contract.isDoneDetail === true);

  const renderContent = () => {
    if (isLoading) {
      return (
        <Box h={400}>
          <LoadingOverlay visible />
        </Box>
      );
    }

    if (!data) {
      return <></>;
    }

    return (
      <>
        <Box
          sx={{
            '& > *': {
              borderRadius: 0,
              border: `0 !important`,
            },
          }}
        >
          <JobMatchingItem
            containerProps={{ bg: 'gray-100', radius: 0 }}
            hideApplyButton
            hideMatchingStatus
            isLoadingChat={isOpenContact}
            onChat={async () => {
              await openContactAIAssistant(
                {
                  jobPostId: data.jobPostId,
                },
                {
                  onSuccess: (res) => {
                    if (res?.newGroupChat) {
                      helpers.logEventTracking('start_group_chat', {
                        recruiter_id: res?.jobPostInfo?.createdBy,
                        job_id: res?.jobPostId,
                        status: 'contact',
                      });
                    } else {
                      helpers.logEventTracking('view_group_chat', {
                        chat_id: res?._id,
                      });
                    }
                  },
                },
              );
              router.push(`/my-page/jobs/${data._id}/messages`);
            }}
            {...data}
          />
        </Box>
        <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
          <Box mb={24} pos={'relative'}>
            {data.status === 'APPLYING' ? (
              <ActionIcon
                color="primary"
                onClick={() => setModalVisibility(true)}
                size="sm"
                sx={{
                  position: 'absolute',
                  top: -4,
                  right: 0,
                }}
                variant="subtle"
              >
                <IconEdit />
              </ActionIcon>
            ) : null}
          </Box>

          {/* Worker Information Download Section */}
          <Box mb={24}>
            <Text color="black" fw={700} fz={16} lh={'24px'} mb={16}>
              作業員情報
            </Text>
            <Flex align="center" gap={8}>
              <Flex align="center" gap={4}>
                <Box
                  sx={{
                    width: 20,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <IconFile
                    style={{
                      width: 20,
                      height: 20,
                      color: '#4B5563',
                    }}
                  />
                </Box>
                <Text color="black" fz={16} lh={'24px'}>
                  contractedWorkerlist.pdf
                </Text>
              </Flex>
              <Button
                disabled={(data?.workerContracts?.length ?? 0) === 0}
                leftIcon={<IconDownload style={{ width: 24, height: 24 }} />}
                loading={isDownloading}
                onClick={handleDownloadWorkerContractsPdf}
                size="sm"
                variant="outline"
              >
                ダウンロード
              </Button>
            </Flex>
          </Box>
          {data.status === 'APPLYING' && <ApplicationInfoComponent />}
          <Box>
            <WorkerList
              data={data.workerContracts || []}
              isEditing={isEditing}
              jobId={id}
              jobStatus={data.status}
              // onAddWorker={() => {
              //   // Handle adding a new worker
              //   // Implementation for adding a new worker will go here

              // }}
              onEditWorker={handleUpdateWorkerDocuments}
              showAddButton={true}
            />
          </Box>
        </Box>
      </>
    );
  };

  return (
    <Box pb={{ base: 40, sm: 64 }} pt={{ base: 24, sm: 32 }}>
      <FormLayout
        HeaderComponent={<Header />}
        bodyProps={{ py: 0, px: 0, mih: 64 }}
        isFooterShown={false}
      >
        {renderContent()}
      </FormLayout>
      {/* Submit Button Section - Only show for COLLECTING and CONTACT status */}
      {(data?.status === 'COLLECTING' || data?.status === 'CONTACT') && (
        <Box
          bg="white"
          mb={{ base: -40, sm: -64 }}
          mt={{ base: 40, sm: 64 }}
          sx={{
            boxShadow: '2px 4px 10px 0px rgba(117, 138, 147, 0.1)',
          }}
          w="100%"
        >
          <Flex
            align="center"
            gap={16}
            justify="center"
            p={16}
            sx={{
              alignSelf: 'stretch',
            }}
          >
            <Button
              disabled={!isSubmitEnabled}
              loading={isSubmitting}
              onClick={handleSubmitMatching}
              sx={(theme) => ({
                width: 168,
                height: 48,
                borderRadius: 10,
                fontSize: 14,
                fontWeight: 500,
                fontFamily: 'Noto Sans JP',
                gap: 4,
                padding: 12,
                '&:disabled': {
                  backgroundColor: theme.colors['gray-500'],
                  color: theme.white,
                },
              })}
            >
              確認しました
            </Button>
          </Flex>
        </Box>
      )}
      <Modal
        buttonProps={{
          cancel: {
            onClick: () => setModalVisibility(false),
          },
          submit: {
            onClick: handleSubmit(handleUpdateMatchingDocuments),
            disabled: !formState.isValid || !formState.isDirty,
            loading: isEditing,
          },
        }}
        onClose={() => setModalVisibility(false)}
        opened={modalVisible}
        title={'名簿ファイルの追加'}
      >
        <FormProvider {...methods}>
          <MatchingUpdateForm />
        </FormProvider>
      </Modal>
    </Box>
  );
};

MatchingDetailPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default MatchingDetailPage;
