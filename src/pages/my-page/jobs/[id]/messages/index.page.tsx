import IconArrowPrevious from '@icons/icon-arrow-previous.svg';
import { ActionIcon, Box, Flex, MediaQuery, Text } from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import { useMutation, useQuery } from '@tanstack/react-query';
import Layout from 'components/Layout';
import { JobInfo, MessageList } from 'components/Messages';
import type { TSendMessagePayload } from 'components/Messages/MessageActions';
import MessageActions from 'components/Messages/MessageActions';
import Utils from 'components/Messages/utils';
import useAskOrApplyButtonsPanel from 'components/MessagesAI/AskOrApplyButtonsPanel';
import { useFetch, useGlobalState, useMutate, useUser } from 'hooks';
import { useAsPath } from 'hooks/useAsPath';
import { debounce } from 'lodash';
import type { IUser } from 'models/auth/type';
import { chatService } from 'models/chat/chatService';
import type { IMessageAttachment, IRoom } from 'models/chat/type';
import jobQuery from 'models/job';
import type { IJobMatchingItem } from 'models/job/type';
import Link from 'next/link';
import router, { useRouter } from 'next/router';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { FILE_UPLOAD_TYPES, OCR_FILE_UPLOAD_TYPES } from 'utils/constants';
import type { IError } from 'utils/type';

const MatchingChatDetailPage = () => {
  const { prevAsPath } = useAsPath();
  const { query } = useRouter();
  const { data: currentUser } = useUser();
  const firebaseUserId = currentUser?.firebaseUserId || '';
  const id = typeof query.id === 'string' ? query.id : '';
  const { showChatTemplate, setShowChatTemplate } = useGlobalState();
  const hasMessageRef = useRef<boolean | null>(null);
  const [isTemplateUsing, setIsTemplateUsing] = useState(false);

  const { data, refetch } = useFetch<IJobMatchingItem>({
    ...jobQuery.getMatchingDetail(id),
    enabled: !!id,
    refetchOnMount: 'always',
    refetchOnWindowFocus: false,
    cacheTime: 0,
    meta: {
      notToastErrorCodes: [400],
    },
    onError: (error: unknown) => {
      const { error: err, statusCode } = error as IError;
      if (err === 'OBJECT_NOT_FOUND' && statusCode === 400) {
        router.replace('/not-found');
      }
    },
  });

  const { data: unsubscribe, remove } = useQuery({
    enabled: !!id && !!firebaseUserId,
    refetchOnReconnect: true,
    queryFn: async () => {
      return chatService.attachRoomListener(id);
    },
  });

  useEffect(() => {
    return () => {
      if (unsubscribe) {
        unsubscribe();
        remove();
      }
    };
  }, [remove, unsubscribe]);

  // Get room data from cache (populated by the listener)
  const { data: roomData } = useQuery({
    queryKey: ['chat', 'rooms', id],
    enabled: false,
    refetchOnReconnect: false,
    select: (cacheData: any) => cacheData?.pages?.[0]?.[0] as IRoom | undefined,
    queryFn: () => undefined,
  });

  const {
    AskOrApplyButtonsPanel,
    chatActionSectionState,
    isAIAssisting,
    isReady,
  } = useAskOrApplyButtonsPanel({
    roomData,
    chatTemplateCtl: [showChatTemplate, setShowChatTemplate],
    firebaseUserId,
    matchingData: data,
    refetchMatchingData: refetch,
  });

  const { data: firebaseMembersInfo } = useQuery<unknown, unknown, IUser[]>({
    queryKey: ['users', 'firebase'],
    refetchOnReconnect: false,
    queryFn: () => chatService.getMembersInfo(id),
    enabled: !!id && !!firebaseUserId && !!data,
  });

  const membersInfo = firebaseMembersInfo?.reduce<Record<string, IUser>>(
    (members, v) => ({ ...members, [v.id || '']: v }),
    {},
  );

  const { mutateAsync: updateRoomHasMessage } = useMutate(
    jobQuery.updateRoomHasMessage(id),
  );

  const { mutateAsync: sendMessage, isLoading: isSendingMessage } = useMutation<
    unknown,
    unknown,
    {
      value: string;
      roomId: string;
      senderId: string;
      attachments: IMessageAttachment[];
    }
  >({
    mutationFn: (payload) =>
      chatService.sendMessage({
        ...payload,
      }),
  });

  const handleSendMessage = useCallback(
    async (payload: TSendMessagePayload, onSuccess: () => void) => {
      if (firebaseUserId) {
        await sendMessage({
          value: payload.message,
          roomId: id || '',
          senderId: firebaseUserId,
          attachments: payload.attachments.map((file) => ({
            id: file.key,
            contentType: file.file?.type || '',
            name: file.originalName,
            url: file.originUrl,
            size: file.file?.size || 0,
            status: 'ACTIVE',
            type: Utils.getFileTypeFromContentType(file.file?.type || ''),
          })),
        });
        if (!hasMessageRef.current) {
          await updateRoomHasMessage({});
          hasMessageRef.current = true;
        }
        if (showChatTemplate) {
          setShowChatTemplate(false);
        }
        onSuccess?.();
      }
    },
    [
      firebaseUserId,
      id,
      sendMessage,
      setShowChatTemplate,
      showChatTemplate,
      updateRoomHasMessage,
    ],
  );

  useEffect(() => {
    if (!isReady) {
      return;
    }

    async function senderHasMessageBefore() {
      const hasMessageBefore = await chatService.hasMessageBefore(
        id,
        firebaseUserId,
      );
      if (!isAIAssisting) {
        setShowChatTemplate(!hasMessageBefore);
      }
      hasMessageRef.current = hasMessageBefore;
    }
    if (!firebaseUserId && !isAIAssisting) {
      setShowChatTemplate(false);
      return;
    }
    if (firebaseUserId) {
      senderHasMessageBefore();
    }
  }, [firebaseUserId, id, setShowChatTemplate, isReady, isAIAssisting]);

  const templateText = useMemo(() => {
    return `ご担当者へ\n初めまして！${currentUser?.fullName}です。${
      currentUser?.prefecture || currentUser?.city
        ? `弊社は${currentUser.prefecture || ''}${
            currentUser.city || ''
          }に本社を置き\n`
        : ''
    }全国各地のプラント設備にて${
      data?.jobPostInfo.jobType
    }を専門として承っております。\nこの度拝見致しました工事に関してはお力になれるのではないかと思いご連絡さしあげました。\n是非マッチングさせて頂けますと幸いです。\nお忙しいとは思いますが何卒よろしくお願い致します。`;
  }, [
    currentUser?.city,
    currentUser?.prefecture,
    currentUser?.fullName,
    data?.jobPostInfo.jobType,
  ]);

  const handleSelectTemplate = () => {
    if (showChatTemplate && !isTemplateUsing) {
      setIsTemplateUsing(true);
    }
  };

  const debounceResize = useMemo(
    () =>
      debounce(() => {
        // First we get the viewport height and we multiple it by 1% to get a value for a vh unit
        const vh = window.innerHeight * 0.01;
        // Then we set the value in the --vh custom property to the root of the document
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      }, 500),
    [],
  );

  useWindowEvent('resize', debounceResize);

  return (
    <Flex
      align="stretch"
      direction={{ base: 'column', sm: 'row' }}
      h="100%"
      sx={{
        maxHeight: 'calc(var(--vh, 1vh) * 100 - var(--mantine-header-height))',
      }}
    >
      <MediaQuery
        largerThan="sm"
        styles={{
          display: 'none',
        }}
      >
        <Flex
          align="center"
          bg="white"
          h={64}
          justify="center"
          left={0}
          pos="fixed"
          sx={(theme) => ({
            zIndex: 100,
            borderBottom: `1px solid ${theme.colors['gray-300']}`,
          })}
          top={0}
          w="100%"
        >
          <ActionIcon
            color="gray-500"
            component={Link}
            href={
              prevAsPath ||
              (data?.status === 'CONTACT'
                ? `/jobs/${data?.jobPostId}`
                : `/my-page/jobs/${data?._id}`)
            }
            left={16}
            pos="absolute"
            size="sm"
          >
            <IconArrowPrevious />
          </ActionIcon>
          <Text fw={700} fz={16} lh="24px">
            Chat
          </Text>
        </Flex>
      </MediaQuery>
      <Box
        pb={16}
        pt={{ base: 12, sm: 16 }}
        px={{ base: 16, sm: 24 }}
        sx={(theme) => ({
          [theme.fn.largerThan('sm')]: {
            flex: '0 0 376px',
          },
        })}
      >
        {/* Job information */}
        <Text
          color="info-500"
          component={Link}
          display={{ base: 'none', sm: 'inline-flex' }}
          fw={400}
          fz={16}
          href={
            prevAsPath ||
            (data?.status === 'CONTACT'
              ? `/jobs/${data?.jobPostId}`
              : `/my-page/jobs/${data?._id}`)
          }
          lh="24px"
          mb={32}
          sx={{ alignItems: 'center', gap: 8 }}
        >
          <IconArrowPrevious />
          案件管理
        </Text>
        <JobInfo data={data} />
      </Box>
      <Flex
        bg="White"
        direction="column"
        mih={0}
        pos="relative"
        sx={{ flex: '1' }}
        w="100%"
      >
        {/* Message list */}
        <MessageList
          members={membersInfo}
          onSelectTemplate={handleSelectTemplate}
          pb={{ base: 28, sm: 0 }}
          pt={24}
          px={{ base: 16, sm: 24 }}
          roomId={id}
          templateText={templateText}
        />
        <AskOrApplyButtonsPanel />

        {chatActionSectionState === 'active' && (
          <MessageActions
            accept={
              data?.status === 'COLLECTING'
                ? OCR_FILE_UPLOAD_TYPES
                : FILE_UPLOAD_TYPES
            }
            disabled={!firebaseUserId}
            displayAttachButton={
              !roomData?.isAIAssisting ||
              (roomData?.isAIAssisting === true &&
                data?.status === 'COLLECTING' &&
                roomData?.collectMethod === 'UPLOAD')
            }
            isTemplateUsing={isTemplateUsing}
            loading={isSendingMessage}
            maxFiles={1}
            maxInputRow={4}
            onSendMessage={handleSendMessage}
            onTemplateUsed={() => setIsTemplateUsing(false)}
            readonly={isSendingMessage}
            templateText={templateText}
          />
        )}
      </Flex>
    </Flex>
  );
};

MatchingChatDetailPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default MatchingChatDetailPage;
