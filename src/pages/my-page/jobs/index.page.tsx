import EmptyListIcon from '@icons/empty-state-empty-info.svg';
import { Box, Flex, LoadingOverlay, Text } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import FormLayout from 'components/CardLayout';
import Layout from 'components/Layout';
import JobMatchingItem from 'components/Matching/JobMatchingItem';
import PaginationBar from 'components/Pagination';
import { useList, useMutate } from 'hooks';
import useFirebaseUser from 'hooks/useFirebaseUser';
import jobQuery from 'models/job';
import type { IJobMatchingItem } from 'models/job/type';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import helpers from 'utils/helpers';

const MatchingListPage = () => {
  useEffect(() => {
    helpers.logEventTracking('view_applied_job_list');
  }, []);

  const router = useRouter();

  const { data: firebaseUser } = useFirebaseUser();
  const unreadMessageRoomMapping = firebaseUser?.numberOfUnreadMessages || {};

  const {
    list: matchingList,
    isLoading,
    total,
    lastPage: pages,
  } = useList<IJobMatchingItem>({ ...jobQuery.getMatchingList });

  const { mutateAsync: openContactAIAssistant, isLoading: isOpenContact } =
    useMutate<
      {
        jobPostId: string;
      },
      IJobMatchingItem
    >(jobQuery.openContactAIAssistant);

  const Footer = () => {
    if (!matchingList || !matchingList.length) {
      return null;
    }

    return (
      <Box pb={{ base: 32, sm: 40 }} px={24}>
        <PaginationBar pages={pages} total={total} />
      </Box>
    );
  };

  // const fetchJobPostData = (_id: string) =>
  //   new Promise((resolve, reject) => {
  //     fetchDetail({
  //       ...jobQuery.postDetail(_id),
  //       meta: {
  //         noToastError: true,
  //       },
  //     })
  //       .then((data) => resolve(data))
  //       .catch((err) => {
  //         const { error, statusCode } = err as IError;
  //         if (error === 'OBJECT_NOT_FOUND' && statusCode === 400) {
  //           helpers.toast({
  //             type: 'error',
  //             message: ERROR_MESSAGES.JOB_STATUS_CHANGED,
  //           });
  //         }
  //         reject(err);
  //       });
  //   });

  const handleOpenContact = async (matching: IJobMatchingItem) => {
    await openContactAIAssistant(
      {
        jobPostId: matching.jobPostId,
      },
      {
        onSuccess: (res) => {
          if (res?.newGroupChat) {
            helpers.logEventTracking('start_group_chat', {
              recruiter_id: res?.jobPostInfo?.createdBy,
              job_id: res?.jobPostId,
              status: 'contact',
            });
          } else {
            helpers.logEventTracking('view_group_chat', {
              chat_id: res?._id,
            });
          }
        },
      },
    );
    router.push(`/my-page/jobs/${matching._id}/messages`);
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <Flex align={'center'} direction={'column'} h={400} justify={'center'}>
          <LoadingOverlay visible />
        </Flex>
      );
    }

    if (matchingList && matchingList.length > 0) {
      return (
        <Flex
          direction={'column'}
          gap={24}
          sx={(theme) => ({
            '& > *': {
              border: `1px solid ${theme.colors['gray-300']} !important`,
            },
          })}
        >
          {matchingList.map((matching) => (
            <JobMatchingItem
              {...matching}
              containerProps={{
                bg: 'gray-100',
              }}
              hasUnreadMessage={
                (unreadMessageRoomMapping[matching._id] || 0) > 0
              }
              href={`/my-page/jobs/${matching._id}`}
              isClickable={matching.status !== 'CONTACT'}
              isLoadingChat={isOpenContact}
              key={matching._id}
              onApply={() => handleOpenContact(matching)}
              onChat={async () => {
                await openContactAIAssistant(
                  {
                    jobPostId: matching.jobPostId,
                  },
                  {
                    onSuccess: (res) => {
                      if (res?.newGroupChat) {
                        helpers.logEventTracking('start_group_chat', {
                          recruiter_id: res?.jobPostInfo?.createdBy,
                          job_id: res?.jobPostId,
                          status: 'contact',
                        });
                      } else {
                        helpers.logEventTracking('view_group_chat', {
                          chat_id: res?._id,
                        });
                      }
                    },
                  },
                );
                router.push(`/my-page/jobs/${matching._id}/messages`);
              }}
            />
          ))}
        </Flex>
      );
    }

    return (
      <Flex
        align={'center'}
        direction={'column'}
        justify={'center'}
        pb={124}
        pt={64}
      >
        <EmptyListIcon />
        <Text
          align="center"
          size={'md'}
        >{`やり取り中案件がありません。\n案件検索画面から発注者に連絡ことや案件に応募することができます。`}</Text>
      </Flex>
    );
  };

  return (
    <FormLayout
      FooterComponent={<Footer />}
      containerProps={{ mt: { base: 24, sm: 32 }, mb: { base: 40, sm: 64 } }}
      title={total && total > 0 ? `案件管理（${total}件）` : '案件管理'}
    >
      {renderContent()}
    </FormLayout>
  );
};

MatchingListPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default MatchingListPage;
