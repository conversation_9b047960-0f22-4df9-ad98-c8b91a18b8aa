import EmptySearch from '@icons/empty-search.svg';
import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconSort from '@icons/icon-sort.svg';
import type { MantineTheme } from '@mantine/core';
import {
  Card,
  Center,
  Flex,
  Group,
  LoadingOverlay,
  Menu,
  Stack,
  Text,
  UnstyledButton,
} from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import {
  FilterJobModalCard as FilterJobSP,
  FilterJobSideCard as FilterJobPC,
} from 'components/Job';
import Layout from 'components/Layout';
import PaginationBar from 'components/Pagination';
import { useList } from 'hooks';
import _debounce from 'lodash/debounce';
import jobQuery from 'models/job';
import { type IJobPostItem, SORT_ORDER_VALUES } from 'models/job/type';
import { useRouter } from 'next/router';
import React, { useEffect, useRef } from 'react';
import helpers from 'utils/helpers';

import JobPostListItemCard from './JobPostListItemCard';

type SortValueType = keyof typeof SORT_ORDER_VALUES;

const EmptyJobPostList = () => {
  return (
    <Center h={{ base: 400, sm: '100%' }} sx={{ flexDirection: 'column' }}>
      <EmptySearch />
      <Text fw={400} fz={'md'} lh={'24px'}>
        検索結果がありません
      </Text>
    </Center>
  );
};

const JobList = () => {
  const { query, isReady, replace, pathname } = useRouter();
  const sortQuery = (query.sortField || 'nearest') as SortValueType;
  const {
    list,
    total,
    lastPage: pages,
    isFetching,
    isSuccess,
    refetch,
  } = useList<IJobPostItem>({
    ...jobQuery.postList({
      workAreas: query.workAreas,
      jobTypeIds: query.jobTypeIds,
      order: sortQuery,
    }),
    enabled: isReady,
  });

  const oldScrollPosition = useRef(0);

  useEffect(() => {
    if (isSuccess && !isFetching && (query.workAreas || query.jobTypeIds)) {
      helpers.logEventTracking('search_job', {
        work_area: query.workAreas,
        job_type_id_list: query.jobTypeIds,
        total_result: total,
        page_number: +(query.page || 1),
      });
      oldScrollPosition.current = 0;
    }
  }, [query, total, isSuccess, isFetching]);

  const updateScrollValue = _debounce((val: string) => {
    if (oldScrollPosition.current !== +val) {
      oldScrollPosition.current = +val;
      helpers.logEventTracking('scroll_job_list', {
        work_area: query.workAreas,
        job_type_id_list: query.jobTypeIds,
        total_result: total,
        percentage: +val,
        page_number: +(query.page || 1),
      });
    }
  }, 1000);

  useEffect(() => {
    const handleScroll = () => {
      const scrollProgress = window.scrollY;

      const scrollHeight = document.body.scrollHeight - window.innerHeight;

      updateScrollValue(((scrollProgress / scrollHeight) * 100).toFixed(2));
    };

    if (isSuccess && (query?.workAreas || query?.jobTypeIds)) {
      window.addEventListener('scroll', handleScroll);

      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }

    return () => {};
  }, [isSuccess, query?.jobTypeIds, query?.workAreas, updateScrollValue]);

  const handleChangeListOrder = (value: string) => {
    if (!value || !Object.keys(SORT_ORDER_VALUES).includes(value)) {
      return;
    }

    replace({
      pathname,
      query: {
        ...query,
        sortField: value,
      },
    });
  };

  return (
    <Flex
      gap={{ base: 'md', sm: '32px' }}
      justify="center"
      pos={{ base: undefined, sm: 'relative' }}
      sx={(theme: MantineTheme) => ({
        padding: '32px 16px',
        [theme.fn.smallerThan('sm')]: {
          padding: '24px 0 38px',
          flexDirection: 'column',
        },
      })}
    >
      <FilterJobPC refetch={refetch} />

      <FilterJobSP refetch={refetch} />

      <Card
        display="flex"
        maw={784}
        p={{ base: 'md', sm: 'xl' }}
        radius={10}
        sx={(theme: MantineTheme) => ({
          flexDirection: 'column',
          [theme.fn.smallerThan('sm')]: {
            borderRadius: 0,
          },
        })}
        w="100%"
      >
        <LoadingOverlay overlayBlur={2} visible={isFetching} />
        <Group
          align="center"
          mb={{ base: '16px', sm: '24px' }}
          position="apart"
        >
          <Text
            color="black"
            fw={700}
            fz={{ base: 'md', sm: 'lg' }}
          >{`案件一覧（${total}件）`}</Text>
          <Menu keepMounted position="bottom-end" shadow="sm">
            <Menu.Target>
              <UnstyledButton
                px={{ base: 8, sm: 12 }}
                py={8}
                sx={(theme) => ({
                  border: `1px solid ${theme.colors['gray-original']}`,
                  borderRadius: 10,
                })}
              >
                <Group spacing={4}>
                  <Group align="center" h={24} position="center" w={24}>
                    <IconSort />
                  </Group>
                  <Text
                    miw={148}
                    sx={(theme) => ({
                      flex: 1,
                      [theme.fn.smallerThan('sm')]: {
                        display: 'none',
                      },
                    })}
                  >
                    {Object.keys(SORT_ORDER_VALUES).includes(sortQuery)
                      ? SORT_ORDER_VALUES[sortQuery]
                      : SORT_ORDER_VALUES.nearest}
                  </Text>
                  <Group
                    align="center"
                    h={24}
                    position="center"
                    sx={(theme) => ({
                      [theme.fn.smallerThan('sm')]: {
                        display: 'none',
                      },
                    })}
                    w={24}
                  >
                    <IconArrowDown />
                  </Group>
                </Group>
              </UnstyledButton>
            </Menu.Target>
            <Menu.Dropdown>
              {Object.entries(SORT_ORDER_VALUES).map(([key, val]) => {
                const isActive = Object.keys(SORT_ORDER_VALUES).includes(
                  sortQuery,
                )
                  ? sortQuery === key
                  : key === 'nearest';
                return (
                  <Menu.Item
                    component={'button'}
                    key={key}
                    miw={204}
                    onClick={() => {
                      handleChangeListOrder(key);
                    }}
                    px={16}
                    py={14}
                    sx={(theme) => ({
                      background: isActive
                        ? theme.colors['gray-200']
                        : 'inherit',
                    })}
                  >
                    {val}
                  </Menu.Item>
                );
              })}
            </Menu.Dropdown>
          </Menu>
        </Group>
        {list?.length ? (
          <Stack mb={'xl'} spacing={'md'}>
            {list.map((item, index) => (
              <JobPostListItemCard {...item} key={index} />
            ))}
          </Stack>
        ) : (
          <EmptyJobPostList />
        )}
        {list?.length ? (
          <Flex
            align={{ base: 'center', sm: 'flex-end' }}
            justify={{ base: 'center', sm: 'flex-end' }}
            sx={{ flex: 1 }}
          >
            <PaginationBar pages={pages} total={total} />
          </Flex>
        ) : (
          <></>
        )}
      </Card>
    </Flex>
  );
};

JobList.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default JobList;
