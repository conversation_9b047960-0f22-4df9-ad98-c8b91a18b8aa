import IconChat from '@icons/icon-chat.svg';
import IconChatPrompt from '@icons/icon-chat-prompt.svg';
import IconClock from '@icons/icon-clock.svg';
import IconCommute from '@icons/icon-commute.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import type { MantineTheme } from '@mantine/core';
import { Box, Card, Stack, Text } from '@mantine/core';
import JobCardItem from 'components/JobCardItem';
import Tag from 'components/Tag';
import { useMutate, useUser } from 'hooks';
import jobQuery from 'models/job';
import type { IJobMatchingItem, IJobPostItem } from 'models/job/type';
import { useRouter } from 'next/router';
import type { MouseEvent } from 'react';
import { ERROR_MESSAGES } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';
import { fetchDetail } from 'utils/queryClient';
import type { IError } from 'utils/type';

const JobPostListItemCard = (item: IJobPostItem) => {
  const { push, query } = useRouter();
  const refType =
    query.workAreas || query.jobTypeIds ? 'search_result' : 'job_list';
  const { data: currentUser } = useUser();
  const { mutateAsync: openContactAIAssistant, isLoading: isOpenContact } =
    useMutate<
      {
        jobPostId: string;
      },
      IJobMatchingItem
    >(jobQuery.openContactAIAssistant);

  const jobAppliable =
    ((!currentUser || !item?.appliedWorkers?.includes(currentUser._id)) &&
      item?.appliedMatching?.status === 'FINISHED') ||
    item?.appliedMatching?.status === 'APPLYING';

  const handleOnMessage = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (currentUser) {
      const matchingDetail = await openContactAIAssistant(
        {
          jobPostId: item._id,
        },
        {
          onSuccess: (res) => {
            if (res?.newGroupChat) {
              helpers.logEventTracking('start_group_chat', {
                recruiter_id: res?.jobPostInfo?.createdBy,
                job_id: res?.jobPostId,
                status: 'contact',
              });
            } else {
              helpers.logEventTracking('view_group_chat', {
                chat_id: res?._id,
              });
            }
          },
        },
      );
      push(`/my-page/jobs/${matchingDetail.chatInfo.roomFirebaseId}/messages`);
    } else {
      push(`/login?referer=/jobs/${item._id}`);
    }
  };

  const fetchJobData = () =>
    new Promise((resolve, reject) => {
      fetchDetail({
        ...jobQuery.postDetail(item._id),
        meta: {
          noToastError: true,
        },
      })
        .then((data) => resolve(data))
        .catch((err) => {
          const { error, statusCode } = err as IError;
          if (error === 'OBJECT_NOT_FOUND' && statusCode === 400) {
            helpers.toast({
              type: 'error',
              message: ERROR_MESSAGES.JOB_STATUS_CHANGED,
            });
          }
          reject(err);
        });
    });

  const handleGoDetail = () => {
    fetchJobData().then(() => push(`/jobs/${item._id}?f=${refType}`));
  };

  return (
    <Card
      onClick={handleGoDetail}
      pb={16}
      pos={'relative'}
      pt={{ base: 12, sm: 20 }}
      px={{ base: 'md', sm: 'xl' }}
      shadow={'none'}
      sx={{
        cursor: 'pointer',
      }}
    >
      <Box
        bg={'#EDEEF0'}
        hidden={!!item.status && item.status !== 'FINISHED'}
        pos={'absolute'}
        px={8}
        py={4}
        right={0}
        sx={{ borderRadius: '0 0 0 10px' }}
        top={0}
      >
        <Text c={'gray-700'} fw={500} fz={12} lh={'16px'}>
          募集終了しました
        </Text>
      </Box>
      <Stack
        sx={(theme: MantineTheme) => ({
          gap: theme.spacing.md,
          [theme.fn.smallerThan('sm')]: { gap: theme.spacing.sm },
          '.mantine-Badge-root': { width: 'fit-content' },
        })}
      >
        <JobCardItem.Header {...item} />
        <JobCardItem.ContentRows
          rows={[
            {
              icon: <IconLocation color="#D23A19" height={16} width={16} />,
              text: `稼働エリア: ${item.workArea}`,
            },
            {
              icon: <IconClock />,
              text: `予定工期: ${
                item.startDate
                  ? dayjs(item.startDate).format('YYYY年MM月DD日')
                  : ''
              }${
                item.endDate
                  ? ` - ${dayjs(item.endDate).format('YYYY年MM月DD日')}`
                  : ''
              }`,
            },
          ].concat(
            item.commutingCondition
              ? [
                  {
                    icon: <IconCommute />,
                    text: `出張・通勤条件: ${item.commutingCondition}`,
                  },
                ]
              : [],
          )}
        />
        {item.jobType && <Tag text={item?.jobType?.name} />}
        <JobCardItem.Footer
          {...item}
          buttons={
            jobAppliable
              ? [
                  {
                    children: '問合せする',
                    leftIcon: <IconChat />,
                    w: { base: '100%', sm: 176 },
                    miw: { base: undefined, sm: 176 },
                    onClick: handleOnMessage,
                    variant: 'outline',
                    loading: isOpenContact,
                  },
                ]
              : [
                  {
                    children: 'ご応募・お問合せ',
                    leftIcon: <IconChatPrompt />,
                    w: { base: '100%', sm: 176 },
                    miw: { base: undefined, sm: 176 },
                    onClick: handleOnMessage,
                    variant: 'filled',
                    loading: false,
                  },
                ]
          }
        />
      </Stack>
    </Card>
  );
};

export default JobPostListItemCard;
