import IconCalendar from '@icons/icon-calendar.svg';
import IconChat from '@icons/icon-chat.svg';
import IconChatPrompt from '@icons/icon-chat-prompt.svg';
import IconClock from '@icons/icon-clock.svg';
import IconCommute from '@icons/icon-commute.svg';
import IconCommuting from '@icons/icon-commuting.svg';
import IconConstructionCert from '@icons/icon-construction-cert.svg';
import IconDeadline from '@icons/icon-deadline.svg';
import IconDormitory from '@icons/icon-dormitory.svg';
import IconFood from '@icons/icon-food.svg';
import IconInsuranceCriteria from '@icons/icon-insurance-criteria.svg';
import IconJobType from '@icons/icon-job-type.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import IconSalary from '@icons/icon-salary.svg';
import IconUser from '@icons/icon-user.svg';
import {
  Box,
  Button,
  Card,
  Flex,
  Grid,
  LoadingOverlay,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import { dehydrate } from '@tanstack/react-query';
import Breadcrumb from 'components/Breadcrumb';
import JobCardItem from 'components/JobCardItem';
import Layout from 'components/Layout';
import Tag from 'components/Tag';
import { useFetch, useMutate, useUser } from 'hooks';
import { useAsPath } from 'hooks/useAsPath';
import _debounce from 'lodash/debounce';
import jobQuery from 'models/job';
import {
  CriteriaLabel,
  type IJobMatchingItem,
  type IJobPostItem,
} from 'models/job/type';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import React, { useEffect, useRef } from 'react';
import { NationalCertificationsLabel } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';
import queryClient, { fetchDetail } from 'utils/queryClient';

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  try {
    const id = typeof query.id === 'string' ? query.id : '';
    await fetchDetail({
      ...jobQuery.postDetail(id),
      axiosConfig: {
        nextRequest: req,
        nextResponse: res,
      },
    });
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
      },
    };
  } catch {
    return {
      notFound: true,
    };
  }
};

const JobDetail = () => {
  const { query, push, isReady, replace } = useRouter();
  const { prevAsPath } = useAsPath();

  const id = typeof query.id === 'string' ? query.id : '';
  const from = prevAsPath && typeof query.f === 'string' ? query.f : undefined;
  const { data, isFetching, isSuccess } = useFetch<IJobPostItem>({
    ...jobQuery.postDetail(id),
    enabled: false,
  });
  const { data: currentUser } = useUser();

  const oldScrollPosition = useRef<number>(0);

  const isLoggedRef = useRef<boolean>(false);

  useEffect(() => {
    if (!prevAsPath && query.f) {
      replace(`/jobs/${id}`, undefined, { shallow: true });
    }
  }, [id, prevAsPath, query.f, replace]);

  useEffect(() => {
    if (isSuccess && id && isReady && !isLoggedRef.current) {
      let ref = 'direct';
      if (from) {
        ref = from;
      }
      helpers.logEventTracking('view_job_detail', {
        job_id: id,
        reference_page: ref,
      });
      isLoggedRef.current = true;
    }
  }, [id, isSuccess, from, isReady]);

  const updateScrollValue = _debounce((val: string) => {
    if (oldScrollPosition.current !== +val) {
      oldScrollPosition.current = +val;
      helpers.logEventTracking('scroll_job_detail', {
        job_id: id,
        percentage: +val,
      });
    }
  }, 1000);

  const handleScroll = () => {
    const scrollProgress = window.scrollY;

    const scrollHeight = document.body.scrollHeight - window.innerHeight;

    updateScrollValue(((scrollProgress / scrollHeight) * 100).toFixed(2));
  };

  useWindowEvent('scroll', handleScroll);

  const theme = useMantineTheme();

  const { mutateAsync: openContactAIAssistant, isLoading: isOpenContact } =
    useMutate<
      {
        jobPostId: string;
      },
      IJobMatchingItem
    >(jobQuery.openContactAIAssistant);

  const SalaryInfo = [
    {
      Icon: IconSalary,
      heading: '単価(税抜):',
      content: data?.isPrivateSalary
        ? 'メッセージにて見積もり依頼'
        : helpers.renderSalary(data?.minSalary || 0, data?.maxSalary),
    },
    {
      Icon: IconCalendar,
      heading: '支払いサイト:',
      content: data?.paymentPeriod,
    },
  ];

  const JobPostInfo = [
    {
      Icon: IconUser,
      heading: '人数:',
      content: `${data?.peopleNeeded}人`,
    },
    {
      Icon: IconJobType,
      heading: '職種:',
      content: <Tag text={data?.jobType?.name ?? ''} />,
    },
    {
      Icon: IconLocation,
      heading: '稼働エリア:',
      content: data?.workArea,
    },
    {
      Icon: IconClock,
      heading: '予定工期:',
      content: `${
        data?.startDate ? dayjs(data.startDate).format('YYYY年MM月DD日') : ''
      }${
        data?.endDate
          ? ` - ${dayjs(data.endDate).format('YYYY年MM月DD日')}`
          : ''
      }`,
    },
    {
      Icon: IconDeadline,
      heading: '募集終了日:',
      content: `${dayjs(data?.publicationEndDate).format('YYYY年MM月DD日')}`,
    },
    {
      Icon: IconDormitory,
      heading: '寮条件:',
      content: !!data?.dormitory && CriteriaLabel[data.dormitory],
    },
    {
      Icon: IconFood,
      heading: '食費条件:',
      content: !!data?.foodAllowance && CriteriaLabel[data.foodAllowance],
    },
    {
      Icon: IconCommuting,
      heading: '赴任交通費条件:',
      content:
        !!data?.commutingAllowance && CriteriaLabel[data.commutingAllowance],
    },
    {
      Icon: IconCommuting,
      heading: '通勤交通費条件:',
      content:
        !!data?.dailyCommutingAllowance &&
        CriteriaLabel[data.dailyCommutingAllowance],
    },
    {
      Icon: IconConstructionCert,
      heading: '建築システムの会員状況:',
      content: data?.nationalCertifications
        ?.map(
          (v) =>
            NationalCertificationsLabel[
              v as keyof typeof NationalCertificationsLabel
            ],
        )
        .join('・'),
    },
    {
      Icon: IconConstructionCert,
      heading: '建設業許可証の保有条件:',
      content: data?.constructionCertificationCriteria?.join('・'),
    },
    {
      Icon: IconInsuranceCriteria,
      heading: '加入保険条件:',
      content: data?.insuranceCriteria?.join('・'),
    },
  ];
  if (data?.commutingCondition) {
    JobPostInfo.splice(5, 0, {
      Icon: IconCommute,
      heading: '出張・通勤条件:',
      content: data?.commutingCondition,
    });
  }
  const isOpenChatWithRecruiter = ['FINISHED', 'APPLYING'].includes(
    data?.appliedMatching?.status ?? '',
  );

  const handleOpenContact: React.MouseEventHandler<HTMLButtonElement> = async (
    e,
  ) => {
    e.preventDefault();
    if (currentUser) {
      const matchingDetail = await openContactAIAssistant(
        {
          jobPostId: data?._id || '',
        },
        {
          onSuccess: (res) => {
            if (res?.newGroupChat) {
              helpers.logEventTracking('start_group_chat', {
                recruiter_id: res?.jobPostInfo?.createdBy,
                job_id: res?.jobPostId,
                status: 'contact',
              });
            } else {
              helpers.logEventTracking('view_group_chat', {
                chat_id: res?._id,
              });
            }
          },
        },
      );
      push(`/my-page/jobs/${matchingDetail.chatInfo.roomFirebaseId}/messages`);
    } else {
      push('/login');
    }
  };

  return (
    <>
      <NextSeo
        description={`${data?.companyId.name} が募集しています。詳細は「${data?.title}」のページをご確認ください|テクノワは、プラント工事特化の案件を通じて企業とスキルの高い一人親方や企業が繋がれるマッチングプラットフォーム`}
        openGraph={{
          title: `「 ${data?.title}」${
            data?.jobType?.name
          } ${data?.workArea.toString()}|テクノワはプラント工事案件特化のマッチングプラットフォームです。`,
          description: `${data?.companyId.name} が募集しています。詳細は「${data?.title}」のページをご確認ください|テクノワは、プラント工事特化の案件を通じて企業とスキルの高い一人親方や企業が繋がれるマッチングプラットフォーム`,
        }}
        title={`「 ${data?.title}」${
          data?.jobType?.name
        } ${data?.workArea.toString()}|テクノワはプラント工事案件特化のマッチングプラットフォームです。`}
      />
      <Flex
        gap={{ base: 'md', sm: '32px' }}
        justify="space-between"
        sx={() => ({
          padding: '32px 16px',
          [theme.fn.smallerThan('sm')]: {
            padding: '24px 0 14px',
            flexDirection: 'column',
          },
        })}
      >
        <Card
          maw={784}
          mx="auto"
          pb={{ base: '32px', sm: '40px' }}
          pt={{ base: 'xl', sm: '32px' }}
          px={{ base: 'md', sm: 'xl' }}
          sx={() => ({
            flexDirection: 'column',
            [theme.fn.smallerThan('sm')]: {
              borderRadius: 0,
              boxShadow: 'none',
            },
          })}
          w={'100%'}
          withBorder={false}
        >
          <LoadingOverlay overlayBlur={2} visible={isFetching} />

          <Stack
            sx={() => ({
              gap: '32px',
              [theme.fn.smallerThan('sm')]: { gap: theme.spacing.xl },
              '.mantine-Badge-root': { width: 'fit-content' },
            })}
          >
            <JobCardItem.Header
              {...(data as IJobPostItem)}
              extra={
                data?.status === 'FINISHED' ? (
                  <Text
                    bg={'#EDEEF0'}
                    c={'gray-700'}
                    fw={500}
                    fz={14}
                    lh={1.4}
                    mt={8}
                    px={8}
                    py={4}
                    sx={{ borderRadius: 4 }}
                  >
                    募集終了しました。
                  </Text>
                ) : undefined
              }
              textProps={[
                {
                  fz: { base: 'xs', sm: 'md' },
                  lh: { base: '16px', sm: '24px' },
                  weight: 400,
                  color: 'gray-700',
                },
                {
                  fz: { base: 'md', sm: 'xl' },
                  lh: { base: '24px', sm: '34px' },
                  weight: 700,
                  color: 'black',
                  lineClamp: 2,
                  sx: {
                    [theme.fn.smallerThan('sm')]: {
                      lineClamp: 3,
                    },
                  },
                },
              ]}
            />
            {/* Buttons */}
            <Flex gap={{ base: 8, sm: 16 }} justify="flex-end">
              {isOpenChatWithRecruiter ? (
                <Button
                  fw={500}
                  fz={14}
                  leftIcon={<IconChat />}
                  loading={isOpenContact}
                  onClick={handleOpenContact}
                  px={{ base: 16, sm: 24 }}
                  sx={{ flex: '0 0 50%' }}
                  variant="outline"
                >
                  問合せする
                </Button>
              ) : (
                <Button
                  fw={500}
                  fz={14}
                  leftIcon={<IconChatPrompt />}
                  loading={isOpenContact}
                  onClick={handleOpenContact}
                  px={{ base: 16, sm: 24 }}
                  sx={{ flex: '0 0 50%' }}
                >
                  ご応募・お問合せ
                </Button>
              )}
            </Flex>

            {/* Job detail */}
            <Flex direction={'column'} gap="md">
              <Text
                color={'black'}
                fw={700}
                fz={{ base: 'md', sm: 'lg' }}
                lh={{ base: '24px', sm: '26px' }}
              >
                案件詳細
              </Text>
              <Text
                color={'black'}
                fw={400}
                fz={{ base: 'sm', sm: 'md' }}
                lh={{ base: '20px', sm: '24px' }}
              >
                {data?.detail}
              </Text>
            </Flex>

            {/* Salary info section */}
            <Flex direction={'column'} gap="md">
              <Text
                color={'black'}
                fw={700}
                fz={{ base: 'md', sm: 'lg' }}
                lh={{ base: '24px', sm: '26px' }}
              >
                支払い情報
              </Text>
              <JobCardItem.ContentRows
                rows={SalaryInfo.map(({ Icon, heading, content }) => ({
                  children: (
                    <Grid gutter={0}>
                      <Grid.Col span="auto">
                        <Box
                          display={'flex'}
                          sx={{
                            alignItems: 'center',
                            gap: '8px',
                          }}
                        >
                          <Icon
                            color={theme.colors.primary}
                            height={'24px'}
                            width={'24px'}
                          />
                          <Text
                            color={'gray-600'}
                            fw={700}
                            fz={{ base: 'sm' }}
                            lh={{ base: '20px' }}
                          >
                            {heading}
                          </Text>
                        </Box>
                      </Grid.Col>
                      <Grid.Col sm={8} span={12}>
                        {React.isValidElement(content) ? (
                          content
                        ) : (
                          <Text
                            color={'black'}
                            fw={400}
                            fz={{ base: 14, sm: 16 }}
                            lh={{ base: '24px', sm: '26px' }}
                            sx={{
                              [theme.fn.smallerThan('sm')]: {
                                marginLeft: '32px',
                              },
                            }}
                          >
                            {content}
                          </Text>
                        )}
                      </Grid.Col>
                    </Grid>
                  ),
                }))}
                stackProps={{
                  spacing: 'md',
                  sx: {
                    'div > div:not(:last-child)': {
                      display: 'inline-flex',
                    },
                  },
                }}
              />
            </Flex>

            {/* Basic info section */}
            <Flex direction={'column'} gap="md">
              <Text
                color={'black'}
                fw={700}
                fz={{ base: 'md', sm: 'lg' }}
                lh={{ base: '24px', sm: '26px' }}
              >
                案件情報
              </Text>
              <JobCardItem.ContentRows
                rows={JobPostInfo.map(({ Icon, heading, content }) => ({
                  children: (
                    <Grid
                      gutter={0}
                      sx={{
                        [theme.fn.smallerThan('sm')]: {
                          '.mantine-Badge-root': {
                            display: 'block',
                            marginLeft: '32px',
                          },
                        },
                      }}
                    >
                      <Grid.Col span="auto">
                        <Box
                          display={'flex'}
                          sx={{
                            alignItems: 'center',
                            gap: '8px',
                          }}
                        >
                          <Icon
                            color={theme.colors.primary}
                            height={'24px'}
                            width={'24px'}
                          />
                          <Text
                            color={'gray-600'}
                            fw={700}
                            fz={{ base: 'sm' }}
                            lh={{ base: '20px' }}
                          >
                            {heading}
                          </Text>
                        </Box>
                      </Grid.Col>
                      <Grid.Col sm={8} span={12}>
                        {React.isValidElement(content) ? (
                          content
                        ) : (
                          <Text
                            color={'black'}
                            fw={400}
                            fz={{ base: 14, sm: 16 }}
                            lh={{ base: '24px', sm: '26px' }}
                            sx={{
                              [theme.fn.smallerThan('sm')]: {
                                marginLeft: '32px',
                              },
                            }}
                          >
                            {content}
                          </Text>
                        )}
                      </Grid.Col>
                    </Grid>
                  ),
                }))}
                stackProps={{
                  spacing: 'md',
                  sx: {
                    'div > div:not(:last-child)': {
                      display: 'inline-flex',
                    },
                  },
                }}
              />
            </Flex>
          </Stack>
        </Card>
      </Flex>
    </>
  );
};

JobDetail.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default JobDetail;
