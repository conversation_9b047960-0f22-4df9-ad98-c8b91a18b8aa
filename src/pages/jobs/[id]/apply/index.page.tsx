import { yupResolver } from '@hookform/resolvers/yup';
import IconClock from '@icons/icon-clock.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import IconSalary from '@icons/icon-salary.svg';
import SuccessIcon from '@icons/icon-success.svg';
import { Box, Button, Card, Flex, Text } from '@mantine/core';
import { dehydrate } from '@tanstack/react-query';
import Breadcrumb from 'components/Breadcrumb';
import CardLayout from 'components/CardLayout';
import InfoPopup from 'components/InfoPopup';
import JobCardItem from 'components/JobCardItem';
import Layout from 'components/Layout';
import { useFetch, useMutate, useUser } from 'hooks';
import { useAsPath } from 'hooks/useAsPath';
import jobQuery from 'models/job';
import type { IJobApplyRequest, IJobPostItem } from 'models/job/type';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import { DateFormat, ERROR_MESSAGES } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';
import queryClient, { fetchDetail } from 'utils/queryClient';

import ApplyForm from './ApplyForm';
import type { ApplyJobFormValues } from './schema';
import schema from './schema';

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  try {
    const id = typeof query.id === 'string' ? query.id : '';
    await fetchDetail({
      ...jobQuery.postDetail(id),
      axiosConfig: {
        nextRequest: req,
        nextResponse: res,
      },
    });
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
      },
    };
  } catch {
    return {
      notFound: true,
    };
  }
};

const JobApplyPage = () => {
  const { push, query, replace } = useRouter();
  const { prevAsPath } = useAsPath();
  const { data: userData, isLoading: isFetchingUser } = useUser();
  const id = typeof query.id === 'string' ? query.id : '';

  const from = prevAsPath && typeof query.f === 'string' ? query.f : undefined;

  useEffect(() => {
    if (!prevAsPath && query.f) {
      replace(`/jobs/${id}/apply`, undefined, { shallow: true });
    }
  }, [id, prevAsPath, query.f, replace]);

  const {
    data,
    mutateAsync: applyJob,
    isLoading: isApplying,
    isSuccess: applySuccess,
  } = useMutate<IJobApplyRequest, { _id: string }>({
    ...jobQuery.applyJob,
    onSuccess: ({ _id }) => {
      helpers.logEventTracking('apply_job', {
        matching_id: _id,
        reference_page: from || 'direct',
      });
    },
    onError: () => {
      helpers.toast({
        type: 'error',
        message: ERROR_MESSAGES.JOB_STATUS_CHANGED,
      });
    },
  });

  const { data: jobDetail, isLoading: isLoadingJobDetail } =
    useFetch<IJobPostItem>({
      ...jobQuery.postDetail(id),
      enabled: false,
    });

  const jobAppliable =
    userData &&
    !jobDetail?.appliedWorkers?.includes(userData._id) &&
    jobDetail?.status === 'PUBLISHING';

  const defaultValues: ApplyJobFormValues = useMemo(
    () => ({
      cvDocumentKeys: userData?.cvDocuments || [],
      workerContracts: [
        {
          workerName: userData?.fullName || '',
          applicationDocumentKeys: userData?.applicationDocuments || [],
        },
      ],
    }),
    [userData],
  );
  const methods = useForm<ApplyJobFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
    defaultValues,
  });
  const {
    handleSubmit,
    reset,
    trigger,
    formState: { isValid },
  } = methods;

  useEffect(() => {
    if (userData) {
      reset(defaultValues);
      trigger();
    }
  }, [userData, defaultValues, reset, trigger]);

  const handleOnCancel = () => {
    helpers.confirm({
      title: '入力内容を破棄',
      children: (
        <Text color="black" fz="16px" lh="24px" p={24}>
          入力内容を破棄しますか？
        </Text>
      ),
      onConfirm: () => {
        push(prevAsPath || `/jobs/${id}`);
      },
    });
  };

  const onSubmit: SubmitHandler<ApplyJobFormValues> = async (values) => {
    await applyJob({
      jobPostId: id,
      matchingInfo: {
        cvDocumentKeys: values.cvDocumentKeys.map((item) => item.key || ''),
        workerContracts: values.workerContracts.map((contact) => ({
          workerName: contact.workerName,
          applicationDocumentKeys: contact.applicationDocumentKeys.map(
            (item) => item.key || '',
          ),
        })),
      },
    });
  };

  if (applySuccess) {
    return (
      <Flex
        direction="column"
        justify="center"
        px={16}
        py={{ base: 61, sm: 79 }}
      >
        <Card
          maw={424}
          mx="auto"
          px={{ base: 15, sm: 31 }}
          py={{ base: 16, sm: 32 }}
          w="100%"
        >
          <InfoPopup
            description={
              '企業が応募情報を確認しますので、\nしばらくお待ちください'
            }
            href={`/my-page/jobs/${data?._id}`}
            icon={<SuccessIcon />}
            isLink
            submitButtonLabel="案件詳細確認"
            title="案件に応募しました"
          />
        </Card>
      </Flex>
    );
  }
  return (
    <FormProvider {...methods}>
      <Box h="100%" pb={{ base: 0, sm: 64 }} pt={{ base: 24, sm: 64 }}>
        <CardLayout
          bodyProps={{
            px: 0,
            py: 0,
          }}
          cancelButtonProps={{ onClick: handleOnCancel }}
          footerProps={{
            sx: (theme) => ({
              borderTop: `1px solid ${theme.colors['gray-300']}`,
              [theme.fn.smallerThan('sm')]: {
                display: 'none',
              },
            }),
          }}
          isLoading={isFetchingUser || isLoadingJobDetail}
          submitButtonLabel="応募する"
          submitButtonProps={{
            onClick: handleSubmit(onSubmit),
            loading: isApplying,
            disabled: !isValid || !jobAppliable,
          }}
          title="応募する"
        >
          <>
            {!isLoadingJobDetail && (
              <Flex
                bg="gray-100"
                direction="column"
                gap={{ base: 12, sm: 16 }}
                pb={{ base: 20, sm: 24 }}
                pt={16}
                px={{ base: 16, sm: 24 }}
              >
                <JobCardItem.Header
                  companyId={jobDetail?.companyId}
                  title={jobDetail?.title}
                />
                <JobCardItem.ContentRows
                  rows={[
                    {
                      icon: <IconLocation />,
                      text: `稼働エリア: ${jobDetail?.workArea || ''}`,
                    },
                    {
                      icon: <IconClock />,
                      text: `予定工期: ${
                        jobDetail?.startDate
                          ? dayjs(jobDetail?.startDate).format(
                              DateFormat.YEAR_MONTH_DATE_JP,
                            )
                          : ''
                      }${
                        jobDetail?.endDate
                          ? ` - ${dayjs(jobDetail?.endDate).format(
                              DateFormat.YEAR_MONTH_DATE_JP,
                            )}`
                          : ''
                      }`,
                    },
                    {
                      icon: <IconSalary />,
                      text: `単価(税抜): ${
                        jobDetail?.isPrivateSalary
                          ? 'メッセージにて見積もり依頼'
                          : helpers.renderSalary(
                              jobDetail?.minSalary || 0,
                              jobDetail?.maxSalary,
                            )
                      }`,
                    },
                  ]}
                />
              </Flex>
            )}
            <ApplyForm />
          </>
        </CardLayout>
        <Flex
          bg="white"
          gap={8}
          justify="flex-end"
          mt={40}
          px={16}
          py={12}
          sx={(theme) => ({
            boxShadow: theme.shadows.sm,
            position: 'sticky',
            bottom: 0,
            gap: 8,
            '& > *': {
              flex: 1,
            },
            [theme.fn.largerThan('sm')]: {
              display: 'none',
            },
          })}
          w="100%"
        >
          <Button miw={100} onClick={handleOnCancel} variant="outline">
            キャンセル
          </Button>
          <Button
            disabled={!isValid || !jobAppliable}
            loading={isApplying}
            miw={100}
            onClick={handleSubmit(onSubmit)}
          >
            応募する
          </Button>
        </Flex>
      </Box>
    </FormProvider>
  );
};

JobApplyPage.getLayout = (page: React.ReactNode) => {
  return (
    <Layout>
      <Breadcrumb />
      {page}
    </Layout>
  );
};

export default JobApplyPage;
