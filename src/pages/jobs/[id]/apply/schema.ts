import { REG_EXP } from 'utils/constants';
import type { InferType } from 'yup';
import { array, object, string } from 'yup';

const schema = object({
  cvDocumentKeys: array()
    .of(
      object({
        url: string().trim(),
        originUrl: string().trim(),
        originalName: string().trim(),
        key: string().trim(),
      }),
    )
    .min(1, '名簿ファイルをアップロードしてから応募できます。')
    .max(15)
    .required(),
  workerContracts: array()
    .of(
      object({
        workerName: string()
          .trim()
          .required('この項目は入力必須です。')
          .max(20, '最大 20 文字でなければなりません')
          .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),
        applicationDocumentKeys: array()
          .of(
            object({
              url: string().trim(),
              originUrl: string().trim(),
              originalName: string().trim(),
              key: string().trim(),
            }),
          )
          .min(1, '提出資料をアップロードしてから応募できます。')
          .max(15)
          .required(),
      }),
    )
    .min(1, 'この項目は入力必須です。')
    .max(10)
    .required(),
});

export type ApplyJobFormValues = InferType<typeof schema>;
export default schema;
