import IconAdd from '@icons/icon-add.svg';
import IconTrash from '@icons/icon-trash.svg';
import { ActionIcon, Box, Button, Flex, ThemeIcon } from '@mantine/core';
import { FileUpload, FormLabel, TextField } from 'components/Form';
import UploadFileList from 'components/Form/UploadFileList';
import React, { useRef } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FILE_UPLOAD_TYPES } from 'utils/constants';
import helpers from 'utils/helpers';

import type { ApplyJobFormValues } from './schema';

const defaultItemValue: ApplyJobFormValues['workerContracts'][number] = {
  workerName: '',
  applicationDocumentKeys: [],
};

const isItemValid = (item?: ApplyJobFormValues['workerContracts'][number]) => {
  return !!item && !!item.workerName && item.applicationDocumentKeys.length > 0;
};

const ApplyForm = () => {
  const { control, getValues, trigger } = useFormContext<ApplyJobFormValues>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'workerContracts',
  });

  const newItemsRef = useRef<number[]>([]);

  const handleAddItem = () => {
    newItemsRef.current.push(fields.length);
    append(defaultItemValue);
  };

  const handleLogItem = (index: number) => {
    const arrayData = getValues('workerContracts');

    if (!newItemsRef.current.includes(index)) {
      return;
    }

    if (!isItemValid(arrayData[index])) {
      return;
    }

    helpers.logEventTracking('add_new_member', {
      member_name: arrayData[index]?.workerName,
    });

    newItemsRef.current = newItemsRef.current.filter((item) => item !== index);
  };

  const handleRemoveItem = (index: number) => {
    const position = newItemsRef.current.indexOf(index);

    if (position !== -1) {
      newItemsRef.current.splice(position, 1);
    }
    newItemsRef.current = newItemsRef.current.map((item) =>
      item > index ? item - 1 : item,
    );

    remove(index);
  };

  return (
    <Box component="form" px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
      <FileUpload
        accept={FILE_UPLOAD_TYPES}
        control={control}
        max={15}
        maxSize={10}
        multiple
        name="cvDocumentKeys"
        onUploadSuccess={() => {
          helpers.logEventTracking('upload_resume', {
            reference_page: 'applying',
          });
        }}
        render={({ error, ...props }) => (
          <UploadFileList
            acceptTypes={props.accept}
            data={props.values}
            error={error}
            inlineToastMessage
            label="名簿ファイル"
            labelProps={{
              mb: { base: 12, sm: 16 },
              sx: (theme) => ({
                '& > .mantine-Text-root': {
                  fontSize: 16,
                  fontWeight: 700,
                  lineHeight: '24px',
                  color: theme.colors.black,
                  [theme.fn.smallerThan('sm')]: {
                    fontSize: 14,
                  },
                },
              }),
            }}
            loading={props.isUploading}
            max={15}
            onAdd={() => props.ref.current?.click()}
            onRemove={props.remove}
          />
        )}
      />
      <Box mt={{ base: 24, sm: 32 }}>
        <FormLabel
          label="応募者情報"
          mb={{ base: 12, sm: 16 }}
          sx={(theme) => ({
            '& > .mantine-Text-root': {
              fontSize: 16,
              fontWeight: 700,
              lineHeight: '24px',
              color: theme.colors.black,
              [theme.fn.smallerThan('sm')]: {
                fontSize: 14,
              },
            },
          })}
        />
        <Flex direction="column" gap={{ base: 12, sm: 16 }}>
          {fields.map((field, index) => {
            return (
              <Box
                key={field.id}
                pb={16}
                pos="relative"
                pt={24}
                px={{ base: 16, sm: 24 }}
                sx={(theme) => ({
                  borderRadius: '10px',
                  border: `1px solid ${theme.colors['gray-400']}`,
                })}
              >
                {index > 0 && (
                  <ActionIcon
                    color="primary"
                    h={24}
                    onClick={() => handleRemoveItem(index)}
                    pos="absolute"
                    right={24}
                    size="sm"
                    top={16}
                    variant="subtle"
                    w={24}
                  >
                    <IconTrash />
                  </ActionIcon>
                )}

                <TextField
                  control={control}
                  label="応募者の名前"
                  mb={16}
                  name={`workerContracts.${index}.workerName`}
                  onBlur={() => {
                    trigger(`workerContracts.${index}.workerName`);
                    handleLogItem(index);
                  }}
                  placeholder="名前を入力してください．"
                  required
                />
                <FileUpload
                  accept={FILE_UPLOAD_TYPES}
                  control={control}
                  max={15}
                  maxSize={10}
                  multiple
                  name={`workerContracts.${index}.applicationDocumentKeys`}
                  onUploadSuccess={() => {
                    helpers.logEventTracking('upload_certification', {
                      reference_page: 'applying',
                    });
                    handleLogItem(index);
                  }}
                  render={(props) => (
                    <UploadFileList
                      acceptTypes={props.accept}
                      data={props.values}
                      error={props.error}
                      label="提出資料"
                      loading={props.isUploading}
                      max={15}
                      onAdd={() => props.ref.current?.click()}
                      onRemove={props.remove}
                      required
                    />
                  )}
                />
              </Box>
            );
          })}
          <Button
            bg="neutral-100"
            disabled={fields.length >= 10}
            fullWidth
            leftIcon={
              <ThemeIcon size={14}>
                <IconAdd height={14} width={13} />
              </ThemeIcon>
            }
            onClick={handleAddItem}
            variant="subtle"
          >
            応募者追加
          </Button>
        </Flex>
      </Box>
    </Box>
  );
};

export default ApplyForm;
