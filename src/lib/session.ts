import type { IronSessionOptions } from 'iron-session';
import type { IUser } from 'models/auth/type';
import { COOKIE_USER_DATA_KEY } from 'utils/constants';

declare module 'iron-session' {
  interface IronSessionData {
    user?: Partial<IUser>;
  }
}

const sessionOptions: IronSessionOptions = {
  cookieName: COOKIE_USER_DATA_KEY,
  password: process.env.COOKIE_SECURE_PASSWORD || '',
  cookieOptions: {
    path: '/',
    httpOnly: false,
    secure: false,
    sameSite: undefined,
  },
};

export default sessionOptions;
