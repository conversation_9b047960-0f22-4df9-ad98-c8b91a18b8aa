import { useUser } from 'hooks';
import { useEffect, useRef } from 'react';
import helpers from 'utils/helpers';

const GALogger = () => {
  const { data: userData } = useUser();
  const isUserLogged = useRef(false);

  useEffect(() => {
    if (userData && userData._id && !isUserLogged.current) {
      helpers.setEventTrackingUserId(userData._id);
      isUserLogged.current = true;
    } else if (!userData) {
      isUserLogged.current = false;
    }
  }, [userData]);
  return null;
};

export default GALogger;
