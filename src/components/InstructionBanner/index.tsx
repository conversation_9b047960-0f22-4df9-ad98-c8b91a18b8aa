import IconInfo from '@icons/icon-info.svg';
import { Box, Flex, Text, ThemeIcon } from '@mantine/core';
import React from 'react';

interface InstructionBannerProps {
  message: string;
  show: boolean;
}

const InstructionBanner = ({ message, show }: InstructionBannerProps) => {
  if (!show) return null;

  return (
    <Box
      bg="#DEF6FF"
      mb={16}
      p={16}
      sx={{
        borderRadius: 8,
      }}
    >
      <Flex align="flex-start" gap={8}>
        <ThemeIcon
          color="transparent"
          size={20}
          sx={{
            flexShrink: 0,
            marginTop: 2, // Slight vertical alignment adjustment
          }}
        >
          <IconInfo />
        </ThemeIcon>
        <Box sx={{ flex: 1, paddingTop: 2 }}>
          <Text
            color="#0162D1"
            fw={400}
            fz={12}
            lh="16px"
            sx={{
              whiteSpace: 'pre-line', // Preserves line breaks in the text
            }}
          >
            {message}
          </Text>
        </Box>
      </Flex>
    </Box>
  );
};

export default InstructionBanner;
