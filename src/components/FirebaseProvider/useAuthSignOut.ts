import type {
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import type { Auth, AuthError } from 'firebase/auth';
import { signOut } from 'firebase/auth';

export default function useAuthSignOut(
  auth: Auth,
  useMutationOptions?: UseMutationOptions<void, AuthError, void>,
): UseMutationResult<void, AuthError, void> {
  return useMutation<void, AuthError, void>(() => {
    return signOut(auth);
  }, useMutationOptions);
}
