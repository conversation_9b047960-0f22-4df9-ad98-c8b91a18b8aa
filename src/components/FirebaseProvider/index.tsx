import { onAuthStateChanged } from 'firebase/auth';
import { useMutate, useUser } from 'hooks';
import authQuery from 'models/auth';
import type { IWorker } from 'models/auth/type';
import { useEffect } from 'react';
import { auth } from 'utils/firebase';
import queryClient from 'utils/queryClient';

import useAuthSignInWithCustomToken from './useAuthSignInWithCustomToken';
import useAuthSignOut from './useAuthSignOut';

const FirebaseProvider = () => {
  const { data: currentUser, fetchStatus } = useUser();
  const { mutateAsync: signInFirebase } = useAuthSignInWithCustomToken(auth);
  const { mutateAsync: signOutFirebase } = useAuthSignOut(auth);
  const { mutateAsync: getFirebaseToken } = useMutate<
    {},
    { firebaseToken: string }
  >(authQuery.firebaseToken);

  useEffect(() => {
    // Firebase auth state change listener
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (fetchStatus === 'fetching') return;

      if (!currentUser && firebaseUser) {
        await signOutFirebase();
        return;
      }

      if (currentUser && !firebaseUser) {
        const { firebaseToken } = await getFirebaseToken({});
        const { user } = await signInFirebase(firebaseToken);
        queryClient.setQueryData<IWorker>(['currentUser'], {
          ...currentUser,
          firebaseUserId: user.uid,
        });
        return;
      }

      if (currentUser && !currentUser?.firebaseUserId && firebaseUser?.uid) {
        queryClient.setQueryData<IWorker>(['currentUser'], {
          ...currentUser,
          firebaseUserId: firebaseUser.uid,
        });
      }
    });
    return () => {
      unsubscribe();
    };
  }, [
    currentUser,
    fetchStatus,
    getFirebaseToken,

    signInFirebase,
    signOutFirebase,
  ]);

  return null;
};

export default FirebaseProvider;
