import IconArrowDown from '@icons/icon-arrow-down.svg';
import type {
  MultiSelectProps,
  MultiSelectValueProps,
  SelectItem,
} from '@mantine/core';
import {
  Box,
  Center,
  Checkbox,
  Flex,
  Group,
  MultiSelect,
  Text,
} from '@mantine/core';
import { difference } from 'lodash';
import type { Ref } from 'react';
import React, { forwardRef, useMemo } from 'react';

export interface MultiSelectCheckboxProps {
  allowSelectAll?: boolean;
  data: MultiSelectProps['data'];
  dataInit?: Array<SelectItem>;
  multiSelectLabel?: string;
  setSelected: (vals: MultiSelectProps['data']) => void;
  value: MultiSelectProps['data'];
  renderSelectedValueComponent?: React.FC<{ selectedCount: number }>;
}

interface MultiSelectItemProps
  extends SelectItem,
    React.ComponentPropsWithoutRef<'div'> {
  checked?: boolean;
}

const MultiSelectItem = forwardRef<HTMLDivElement, MultiSelectItemProps>(
  ({ label, checked = false, selected, ...others }, ref) => {
    return (
      <Box
        {...others}
        component={'div'}
        ref={ref}
        sx={{
          '&[data-selected]': {
            backgroundColor: 'white',
          },
        }}
      >
        <Group spacing={'8px'}>
          <Checkbox
            checked={checked || selected}
            onChange={() => {}}
            size="sm"
            sx={{ input: { cursor: 'pointer' } }}
          />
          <Text color="dimmed" size="sm">
            {label}
          </Text>
        </Group>
      </Box>
    );
  },
);
MultiSelectItem.displayName = 'MultiSelectItem';

function MultiSelectCheckbox(
  {
    allowSelectAll = true,
    data: dataProps,
    dataInit = [],
    multiSelectLabel = '稼働エリア',
    setSelected,
    value: valueProps,
    renderSelectedValueComponent,
    ...props
  }: Partial<MultiSelectProps> & MultiSelectCheckboxProps,
  ref: Ref<HTMLInputElement>,
) {
  const data = useMemo<MultiSelectProps['data']>(
    () =>
      !dataProps?.length
        ? dataInit
        : ([
            ...(allowSelectAll ? [{ label: 'すべて', value: 'all' }] : []),
            ...dataProps,
          ] as MultiSelectProps['data']),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dataProps],
  );

  const Value = ({ index }: { index: number } & MultiSelectValueProps) => {
    if (index !== 0) return null;

    return (
      renderSelectedValueComponent?.({
        selectedCount: (valueProps || []).filter((item) => item !== 'all')
          .length,
      }) || (
        <Flex gap={'loose'} w={'100%'}>
          <Text color="black" fw={400} fz={'md'} lh={'24px'}>
            {multiSelectLabel}
          </Text>
          <Center
            h={'24px'}
            p={'2px'}
            sx={(theme) => ({
              backgroundColor: theme.colors.primary,
              borderRadius: '100%',
            })}
            w={'24px'}
          >
            <Text color="white" fw={700} fz={'12px'} lh={'16px'}>
              {(valueProps || []).filter((item) => item !== 'all').length}
            </Text>
          </Center>
        </Flex>
      )
    );
  };

  return (
    <MultiSelect
      data={data}
      disableSelectedItemFiltering
      itemComponent={MultiSelectItem}
      onChange={(nextSelects) => {
        if (!allowSelectAll) {
          setSelected(nextSelects);
          return;
        }
        const currentSelects = valueProps || [];
        // select more
        if (nextSelects.length > currentSelects.length) {
          if (
            // current: [max - 1], next: [max]
            nextSelects.filter((item) => item !== 'all').length ===
              dataProps.length ||
            // next: ['all']
            nextSelects.includes('all')
          ) {
            setSelected([
              'all',
              ...dataProps.map((v) => (v as SelectItem).value),
            ]);
            return;
          }
        }
        // select less
        else if (
          // unset
          difference(currentSelects, nextSelects).includes('all')
        ) {
          setSelected([]);
          return;
        }
        // not all
        setSelected(nextSelects.filter((item) => item !== 'all'));
      }}
      p={'loose'}
      ref={ref}
      rightSection={
        <IconArrowDown color="#9CA3AF" height={'24px'} width={'24px'} />
      }
      searchable={false}
      value={
        dataProps?.length === valueProps?.length
          ? ['all', ...valueProps]
          : valueProps
      }
      valueComponent={Value}
      {...props}
    />
  );
}

export default forwardRef<
  HTMLInputElement,
  Partial<MultiSelectProps> & MultiSelectCheckboxProps
>(MultiSelectCheckbox);
