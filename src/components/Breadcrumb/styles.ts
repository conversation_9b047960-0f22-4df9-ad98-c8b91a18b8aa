import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  breadcrumbWrapper: {
    display: 'flex',
    padding: '0 128px',
    alignItems: 'center',
    height: 72,
    [theme.fn.smallerThan('md')]: {
      padding: '0 16px',
    },
    [theme.fn.smallerThan('sm')]: {
      height: 48,
    },
  },
  breadcrumbs: {
    a: {
      display: 'flex',
      alignItems: 'center',
    },
    '.mantine-Text-root': {
      fontSize: '12px',
      lineHeight: '16px',
    },
    '.mantine-Breadcrumbs-separator': {
      marginLeft: '4px',
      marginRight: '4px',
    },
    '*:not(:last-child)': {
      color: theme.colors['gray-600'],
    },
  },
}));

export default useStyles;
