import IconHome from '@icons/icon-home.svg';
import {
  Anchor,
  Breadcrumbs as MantineBreadcrumbs,
  Flex,
  Text,
} from '@mantine/core';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useMemo } from 'react';

import useStyles from './styles';

const pathContent: Record<string, { title: React.ReactNode; href?: string }> = {
  '/my-page/profile': {
    title: 'プロフィール情報',
    href: '/my-page/profile',
  },
  '/my-page/profile/basic-edit': {
    title: '基本情報',
    href: '/my-page/profile/basic-edit',
  },
  '/my-page/profile/worker-edit': {
    title: 'その他の情報',
    href: '/my-page/profile/worker-edit',
  },
  '/my-page/jobs': {
    title: '案件管理',
    href: '/my-page/jobs',
  },
  '/my-page/jobs/[id]': {
    title: '案件詳細',
    href: '/my-page/jobs/[id]',
  },
  '/my-page/jobs/[id]/workers/create': {
    title: 'プロフィール',
  },
  '/jobs': {
    title: '案件を探す',
    href: '/jobs',
  },
  '/jobs/[id]': {
    title: '案件詳細',
    href: '/jobs/[id]',
  },
  '/jobs/[id]/apply': {
    title: '応募する',
  },
  '/policy': {
    title: 'プライバシーポリシー',
  },
  '/terms': {
    title: '利用規約',
  },
  '/company': {
    title: '運営会社',
  },
  '/uploading-guide': {
    title: '確認書類の撮影・アップロードガイド',
  },
};
const Breadcrumb = ({ hiddenPaths }: { hiddenPaths?: string[] }) => {
  const { pathname, query } = useRouter();
  const { classes } = useStyles();

  const id = typeof query.id === 'string' ? query.id : '';

  const paths = useMemo(() => {
    const pathSplit = pathname.split('/');
    const pathMap: string[] = [];
    let pathSum = '';
    pathSplit.forEach((path, index) => {
      if (path && path !== 'my-page') {
        pathSum +=
          pathSplit[index - 1] === 'my-page' ? `/my-page/${path}` : `/${path}`;
        pathMap.push(pathSum);
      }
    });
    return pathMap;
  }, [pathname]);

  const renderBreadcrumbItem = ({
    path,
    index,
  }: {
    path: string;
    index: number;
  }) => {
    if (hiddenPaths?.includes(path)) {
      return null;
    }
    if (index < paths.length - 1) {
      return (
        pathContent[path]?.title && (
          <Anchor
            align="center"
            component={Link}
            href={pathContent[path]?.href?.replace('[id]', id) ?? '/'}
            key={index}
            size={16}
          >
            {pathContent[path]?.title}
          </Anchor>
        )
      );
    }
    return (
      pathContent[path]?.title && (
        <Text color="black" key={index} size={12}>
          {pathContent[path]?.title}
        </Text>
      )
    );
  };
  return (
    <Flex className={classes.breadcrumbWrapper}>
      <MantineBreadcrumbs className={classes.breadcrumbs}>
        <Anchor align="center" component={Link} href="/" size={16}>
          <IconHome />
        </Anchor>
        {paths.map((path, index) => renderBreadcrumbItem({ path, index }))}
      </MantineBreadcrumbs>
    </Flex>
  );
};

export default Breadcrumb;
