import { yupResolver } from '@hookform/resolvers/yup';
import EmailIcon from '@icons/icon-email.svg';
import ResetPwSentIcon from '@icons/icon-reset-password-sent.svg';
import { Box, Button, Card, Flex, Text, Title } from '@mantine/core';
import { TextField } from 'components/Form';
import InfoPopup from 'components/InfoPopup';
import type { RegisterFormValues } from 'components/RegisterForm/schema';
import schema from 'components/RegisterForm/schema';
import { useMutate } from 'hooks';
import authQuery from 'models/auth';
import Link from 'next/link';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';

const ForgotPasswordForm = () => {
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  const {
    mutateAsync: requestPassword,
    isSuccess,
    isLoading,
  } = useMutate<RegisterFormValues>(authQuery.requestNewPassword);

  const onSubmit: SubmitHandler<RegisterFormValues> = (values) => {
    requestPassword(values);
  };

  if (isSuccess) {
    return (
      <Card
        maw={424}
        px={{ base: 15, sm: 31 }}
        py={{ base: 16, sm: 32 }}
        shadow="sm"
        w={'100%'}
      >
        <InfoPopup
          description={`パスワードを再設定するためのメールを以下 のメールアドレスに送信いたしました。 メー ルの内容に従って、パスワードを再設定して
            ください。`}
          href="/"
          icon={<ResetPwSentIcon />}
          isLink
          submitButtonLabel="ホームページに戻る"
          title="パスワード再設定メール送信完了"
        />
      </Card>
    );
  }

  return (
    <Card
      maw={464}
      px={{ base: 15, sm: 31 }}
      py={{ base: 32, sm: 40 }}
      shadow="sm"
      w={'100%'}
    >
      <Box component="form" onSubmit={handleSubmit(onSubmit)} w="100%">
        <Flex
          align={'center'}
          direction={'column'}
          gap={{ base: 24, sm: 32 }}
          w="100%"
        >
          <Box>
            <Title
              color="black"
              fw={700}
              fz={{ base: 24, sm: 32 }}
              lh={{ base: '34px', sm: '46px' }}
              mb={16}
              order={3}
            >
              パスワードをお忘れですか?
            </Title>
            <Text
              align="center"
              color="gray-700"
            >{`メールアドレスを入力して、\nパスワード再設定を行います。`}</Text>
          </Box>
          <Box w="100%">
            <TextField
              control={control}
              icon={<EmailIcon color="#9CA3AF" />}
              label="メールアドレス"
              name="email"
              placeholder="メールアドレスを入力してください"
              sx={(theme) => ({
                [theme.fn.smallerThan('sm')]: {
                  'input::placeholder': {
                    fontSize: 14,
                  },
                },
              })}
            />
          </Box>
          <Button
            disabled={!isValid}
            fullWidth
            loading={isLoading}
            type="submit"
          >
            新しいパスワードをリクエストする
          </Button>
          <Text
            color="info-500"
            component={Link}
            fw={400}
            fz={16}
            href={'/login'}
            lh="24px"
          >
            ログインに戻る
          </Text>
        </Flex>
      </Box>
    </Card>
  );
};

export default ForgotPasswordForm;
