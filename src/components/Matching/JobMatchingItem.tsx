import IconChat from '@icons/icon-chat.svg';
import IconChatPrompt from '@icons/icon-chat-prompt.svg';
import IconClock from '@icons/icon-clock.svg';
import IconCommute from '@icons/icon-commute.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import type { CardProps } from '@mantine/core';
import { Box, Card, Stack, Text } from '@mantine/core';
import JobCardItem from 'components/JobCardItem';
import Tag from 'components/Tag';
import { type IJobMatchingItem, MATCHING_STATUS } from 'models/job/type';
import Link from 'next/link';
import type { MouseEvent } from 'react';
import React from 'react';
import dayjs from 'utils/dayjs';

import StatusTag from '../StatusTag';

interface JobMatchingItemProps extends IJobMatchingItem {
  hideApplyButton?: boolean;
  hideMatchingStatus?: boolean;
  isClickable?: boolean;
  href?: string;
  onApply?: () => void;
  onChat?: () => void;
  isLoadingChat?: boolean;
  containerProps?: Omit<CardProps, 'children'>;
  hasUnreadMessage?: boolean;
}

const JobMatchingItem = ({
  hideMatchingStatus = false,
  href = '/',
  containerProps,
  isLoadingChat = false,
  onChat = () => {},
  hasUnreadMessage = false,
  ...props
}: JobMatchingItemProps) => {
  // Define the job appliable condition that matches the disabled logic of the apply button
  const isJobApplied = ['FINISHED', 'APPLYING'].includes(props.status);

  const handleChat = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    onChat();
  };

  const renderStatus = () => {
    if (hideMatchingStatus) {
      return null;
    }

    const status = MATCHING_STATUS[props.status];

    return (
      <Box mb={12}>
        <StatusTag label={status.label} type={status.color} />
      </Box>
    );
  };

  return (
    <Card
      aria-disabled={!props.isClickable}
      component={Link}
      href={href}
      pb={16}
      pos={'relative'}
      pt={{ base: 12, sm: 20 }}
      px={{ base: 'md', sm: 'xl' }}
      shadow={'none'}
      sx={{
        '&[aria-disabled=true]': {
          cursor: 'default',
          pointerEvents: 'none',
        },
        button: {
          cursor: 'pointer',
          pointerEvents: 'all',
        },
      }}
      {...containerProps}
    >
      <Box
        bg={'#EDEEF0'}
        hidden={props.jobPostInfo.status !== 'FINISHED'}
        pos={'absolute'}
        px={8}
        py={4}
        right={0}
        sx={{ borderRadius: '0 0 0 10px' }}
        top={0}
      >
        <Text c={'gray-700'} fw={500} fz={12} lh={'16px'}>
          募集終了しました
        </Text>
      </Box>
      <Stack
        sx={(theme) => ({
          gap: theme.spacing.md,
          [theme.fn.smallerThan('sm')]: { gap: theme.spacing.sm },
          '.mantine-Badge-root': { width: 'fit-content' },
        })}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
          }}
        >
          {renderStatus()}
          <Text
            color="gray-600"
            fw={400}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            lineClamp={1}
            maw={'92%'}
            mb={4}
            truncate
          >
            {props.jobPostInfo.companyName}
          </Text>
          <Text
            color="black"
            fw={700}
            fz={{ base: 14, sm: 18 }}
            lh={{ base: '20px', sm: '26px' }}
            lineClamp={2}
            maw={'100%'}
            truncate
          >
            {props.jobPostInfo.title}
          </Text>
        </Box>
        <JobCardItem.ContentRows
          rows={[
            {
              icon: <IconLocation />,
              text: `稼働エリア: ${props.jobPostInfo.workArea}`,
            },
            {
              icon: <IconClock />,
              text: `予定工期: ${
                props.jobPostInfo.startDate
                  ? dayjs(props.jobPostInfo.startDate).format('YYYY年MM月DD日')
                  : ''
              }${
                props.jobPostInfo.endDate
                  ? ` - ${dayjs(props.jobPostInfo.endDate).format(
                      'YYYY年MM月DD日',
                    )}`
                  : ''
              }`,
            },
          ].concat(
            props.jobPostInfo.commutingCondition
              ? [
                  {
                    icon: <IconCommute />,
                    text: `出張・通勤条件: ${props.jobPostInfo.commutingCondition}`,
                  },
                ]
              : [],
          )}
        />
        {!!props.jobPostInfo?.jobType && (
          <Tag text={props.jobPostInfo.jobType} />
        )}
        <JobCardItem.Footer
          {...props.jobPostInfo}
          buttons={
            isJobApplied
              ? [
                  {
                    children: '問合せする',
                    leftIcon: <IconChat />,
                    w: { base: '100%', sm: 282 },
                    miw: { base: undefined, sm: 282 },
                    onClick: handleChat,
                    variant: 'outline',
                    loading: isLoadingChat,
                    bg: 'white',
                    sx: (theme) => ({
                      position: 'relative',
                      '&::before': {
                        content: hasUnreadMessage ? '""' : 'none',
                        position: 'absolute',
                        right: 0,
                        top: 0,
                        height: 12,
                        width: 12,
                        borderRadius: '50%',
                        backgroundColor: theme.colors.primary,
                        transform: 'translateX(25%) translateY(-25%)',
                        zIndex: 10,
                      },
                    }),
                  },
                ]
              : [
                  {
                    children: 'ご応募・お問合せ',
                    leftIcon: <IconChatPrompt />,
                    w: { base: '100%', sm: 282 },
                    miw: { base: undefined, sm: 282 },
                    onClick: handleChat,
                    variant: 'filled',
                    loading: false,
                  },
                ]
          }
        />
      </Stack>
    </Card>
  );
};

export default JobMatchingItem;
