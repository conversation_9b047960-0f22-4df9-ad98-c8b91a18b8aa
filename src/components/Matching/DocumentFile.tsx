import FileIcon from '@icons/icon-file.svg';
import { Flex, Text } from '@mantine/core';
import type { Omit } from 'lodash';
import type { IFile } from 'models/auth/type';
import Link from 'next/link';
import React from 'react';

const DocumentFile = ({
  originUrl,
  originalName,
}: Omit<IFile, '_id' | 'key' | 'readOnly'>) => {
  // If the file is a mockup ("まだファイルがアップロードされていません"), render only plain text, not clickable, no icon
  if (originalName === 'まだファイルがアップロードされていません') {
    return (
      <Flex align={'center'} mb={8}>
        <Text color="gray.8" fz={14} lh={'20px'} sx={{ whiteSpace: 'nowrap' }}>
          まだファイルがアップロードされていません
        </Text>
      </Flex>
    );
  }
  return (
    <Flex
      align={'center'}
      gap={8}
      mb={8}
      sx={(theme) => ({
        '& > a': {
          color: theme.colors['info-500'],
          flex: 1,
        },
      })}
    >
      <FileIcon />
      <Link
        href={originUrl}
        style={{
          overflow: 'hidden',
        }}
        target="_blank"
      >
        <Text
          color="inherit"
          fz={14}
          lh={'20px'}
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {originalName}
        </Text>
      </Link>
    </Flex>
  );
};

export default DocumentFile;
