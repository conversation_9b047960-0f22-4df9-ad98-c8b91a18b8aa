import { yupResolver } from '@hookform/resolvers/yup';
import IconAddWorker from '@icons/icon-add-worker.svg';
import IconFile from '@icons/icon-file.svg';
import {
  Box,
  Button,
  Card,
  Flex,
  MediaQuery,
  Pagination,
  Text,
} from '@mantine/core';
import InstructionBanner from 'components/InstructionBanner';
import MatchingWorkerUpdateForm from 'components/MatchingWorkerUpdateForm';
import type { MatchingWorkerUpdateFormValues } from 'components/MatchingWorkerUpdateForm/schema';
import schema from 'components/MatchingWorkerUpdateForm/schema';
import Modal from 'components/Modal';
import StatusTag from 'components/StatusTag';
import type { IFile } from 'models/auth/type';
import { type IWorkerContract, WORKER_CONTRACT_STATUS } from 'models/job/type';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import { getInstructionBanner } from 'utils/instructionBanner';

interface WorkerListProps {
  data: IWorkerContract[];
  onEditWorker: SubmitHandler<MatchingWorkerUpdateFormValues>;
  isEditing?: boolean;
  jobId?: string;
  onAddWorker?: () => void;
  showAddButton?: boolean;
  jobStatus?: 'CONTACT' | 'APPLYING' | 'FINISHED' | 'COLLECTING';
}

const WorkerList = ({
  data,
  onEditWorker,
  isEditing,
  jobId,
  onAddWorker,
  showAddButton = false,
  jobStatus,
}: WorkerListProps) => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const [modalData, setModalData] = useState<{
    visible: boolean;
    data: {
      _id: string;
      worker: string;
      documents: IFile[];
    };
  }>({
    visible: false,
    data: {
      _id: '',
      worker: '',
      documents: [],
    },
  });

  // Calculate pagination
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = data.slice(startIndex, endIndex);

  const methods = useForm<MatchingWorkerUpdateFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });
  const { reset, formState, handleSubmit } = methods;

  useEffect(() => {
    if (modalData.data) {
      reset({
        applicationDocuments: modalData.data?.documents,
        workerName: modalData.data?.worker,
        _id: modalData.data?._id,
      });
    }
  }, [modalData.data, reset]);

  const closeEditModal = () => {
    setModalData((prev) => ({ ...prev, visible: false }));
  };

  const handleOnEditWorker = async () => {
    await handleSubmit(onEditWorker)();
    closeEditModal();
  };

  const viewWorkerDetails = (_workerId: string) => {
    // Handle viewing worker details
    // Worker details action for ID: workerId
    if (jobId) {
      router.push(`/my-page/jobs/${jobId}/workers/${_workerId}`);
    }
  };

  const handleAddWorker = () => {
    if (onAddWorker) {
      onAddWorker();
    } else if (jobId) {
      // Navigate to the create worker page with the job ID
      router.push(`/my-page/jobs/${jobId}/workers/create`);
    }
  };
  const renderDocument = (item: IWorkerContract) => {
    if (!item.isRequiredDocuments && !item.isDoneDocuments) {
      return (
        <StatusTag label="Not required" showIcon={false} type="DISABLED" />
      );
    }
    // Check if documents are done
    if (item.isDoneDocuments) {
      return (
        <Image
          alt="Worker check status"
          height={24}
          src="/icons/worker-check-done.svg"
          width={24}
        />
      );
    }
    return (
      <Button
        leftIcon={<IconFile size={16} />}
        onClick={() =>
          router.push(
            `/my-page/jobs/${jobId}/workers/${item._id}/edit-documents`,
          )
        }
        size="sm"
        sx={{
          '& .mantine-Button-leftIcon': {
            color: 'inherit',
          },
        }}
        variant="subtle"
      >
        ファイル追加
      </Button>
    );
  };

  // Calculate instruction banner data
  const bannerData = getInstructionBanner(data, jobStatus);

  return (
    <>
      {/* Header with title and Add Worker button */}
      {showAddButton && (
        <Flex align="center" justify="space-between" mb={16}>
          <Text color="black" fw={700} fz={16} lh={'24px'}>
            応募者情報
          </Text>
          <Button
            disabled={jobStatus !== 'COLLECTING'}
            leftIcon={<IconAddWorker height={16} width={16} />}
            onClick={handleAddWorker}
            size="sm"
            variant="outline"
          >
            登録作業者追加
          </Button>
        </Flex>
      )}

      {/* Instruction Banner */}
      <InstructionBanner message={bannerData.message} show={bannerData.show} />

      {/* DESKTOP */}
      <MediaQuery smallerThan={'sm'} styles={{ display: 'none' }}>
        <Box>
          {/* Header */}
          <Box bg={'neutral-100'} sx={{ borderRadius: '10px' }}>
            <Flex
              align={'center'}
              px={24}
              py={18}
              sx={{
                '& > *': {
                  flex: '1 1 0',
                  minWidth: 0,
                },
              }}
            >
              <Text color="neutral-600" fw={700} size={'md'}>
                氏名
              </Text>
              <Text color="neutral-600" fw={700} size={'md'} ta="center">
                名簿の確認
              </Text>
              <Text color="neutral-600" fw={700} size={'md'} ta="center">
                提出書類
              </Text>
              {jobStatus !== 'COLLECTING' && (
                <Text color="neutral-600" fw={700} size={'md'} ta="center">
                  ステータス
                </Text>
              )}
              <Text color="neutral-600" fw={700} size={'md'} ta="center">
                雇用保険/一人親方労災保険
              </Text>
              <Box sx={{ flex: '0 0 96px', textAlign: 'center' }}>
                <Text color="neutral-600" fw={700} size={'md'}>
                  操作
                </Text>
              </Box>
            </Flex>
          </Box>

          {/* Table Body */}
          <Box>
            {currentData.map((item, idx) => (
              <Flex
                align={'center'}
                key={item._id}
                px={24}
                py={16}
                sx={(theme) => ({
                  borderBottom: `1px solid ${theme.colors['gray-200']}`,
                  '& > *': {
                    flex: '1 1 0',
                    minWidth: 0,
                  },
                })}
              >
                <Text>{`${startIndex + idx + 1}. ${
                  item.lastName ? `${item.lastName} ` : ''
                }${item.firstName || ''}`}</Text>

                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  {item.isDoneDetail ? (
                    <Image
                      alt="Worker check status"
                      height={24}
                      src="/icons/worker-check-done.svg"
                      width={24}
                    />
                  ) : (
                    <StatusTag label="未確認" showIcon={false} type="ERROR" />
                  )}
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  {renderDocument(item)}
                </Box>

                {jobStatus !== 'COLLECTING' && (
                  <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <StatusTag
                      label={WORKER_CONTRACT_STATUS[item.status].label}
                      showIcon={false}
                      type={WORKER_CONTRACT_STATUS[item.status].color}
                    />
                  </Box>
                )}

                <Text ta="center">{item?.employmentInsurance?.type}</Text>

                <Box
                  sx={{
                    flex: '0 0 96px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  <Button
                    onClick={() => viewWorkerDetails(item._id)}
                    size="sm"
                    variant="outline"
                    w={80}
                  >
                    {!item.isDoneDetail ? '確認' : '詳細'}
                  </Button>
                </Box>
              </Flex>
            ))}
          </Box>

          {/* Desktop Pagination */}
          <Flex align="center" justify="flex-end" mt={24}>
            <Flex align="center" gap={12}>
              <Text color="gray-600" size="sm">
                レコード {startIndex + 1}-{Math.min(endIndex, data.length)} /
                合計 {data.length}
              </Text>
              <Pagination
                onChange={setCurrentPage}
                radius="sm"
                size="sm"
                spacing={4}
                sx={(theme: any) => ({
                  'button.mantine-Pagination-control': {
                    backgroundColor: 'white',
                    borderColor: theme.colors['gray-200'],
                    color: theme.colors['gray-600'],
                    minWidth: '32px',
                    height: '32px',
                  },
                  'button.mantine-Pagination-control:hover': {
                    backgroundColor: theme.colors['gray-100'],
                  },
                  'button[data-active=true]': {
                    backgroundColor: 'white',
                    borderColor: theme.colors.primary,
                    color: theme.colors.primary,
                    fontWeight: 600,
                  },
                  '.mantine-Pagination-control:not([disabled])': {
                    '&:first-of-type, &:last-child': {
                      color: theme.colors.primary,
                    },
                  },
                })}
                total={totalPages}
                value={currentPage}
              />
            </Flex>
          </Flex>
        </Box>
      </MediaQuery>

      {/* MOBILE */}
      <MediaQuery largerThan={'sm'} styles={{ display: 'none' }}>
        <Flex
          align={'stretch'}
          direction={'column'}
          gap={12}
          justify={'flex-start'}
        >
          {currentData.map((item, idx) => (
            <Box key={item._id}>
              <Card p={16} shadow="none">
                <Box>
                  <Flex
                    align={'flex-start'}
                    direction={'column'}
                    justify={'space-between'}
                  >
                    <Flex
                      align="center"
                      justify={
                        jobStatus === 'COLLECTING'
                          ? 'flex-start'
                          : 'space-between'
                      }
                      mb={8}
                      pb={8}
                      sx={{ borderBottom: '1px solid #E5E7EB' }}
                      w="100%"
                    >
                      <Text lh={'24px'} mb={8}>{`${startIndex + idx + 1}. ${
                        item.workerName
                      }`}</Text>
                      {jobStatus !== 'COLLECTING' && (
                        <StatusTag
                          label={WORKER_CONTRACT_STATUS[item.status].label}
                          showIcon={false}
                          type={WORKER_CONTRACT_STATUS[item.status].color}
                        />
                      )}
                    </Flex>
                    <Flex
                      align="center"
                      gap={8}
                      justify="space-between"
                      mb={12}
                      w="100%"
                    >
                      <Text color="gray-600" fw={700} size={'sm'}>
                        雇用保険/一人親方労災保険:
                      </Text>
                      <Text size={'sm'}>{item?.employmentInsurance?.type}</Text>
                    </Flex>
                    <Flex
                      align="center"
                      gap={8}
                      justify="space-between"
                      mb={8}
                      w="100%"
                    >
                      <Text color="gray-600" fw={700} size={'sm'}>
                        名簿の確認:
                      </Text>
                      {item.isDoneDetail ? (
                        <Image
                          alt="Worker check status"
                          height={24}
                          src="/icons/worker-check-done.svg"
                          width={24}
                        />
                      ) : (
                        <StatusTag
                          label="未確認"
                          showIcon={false}
                          type="ERROR"
                        />
                      )}
                    </Flex>
                    <Flex
                      align="center"
                      gap={8}
                      justify="space-between"
                      mb={8}
                      w="100%"
                    >
                      <Text color="gray-600" fw={700} size={'sm'}>
                        提出書類:
                      </Text>
                      {renderDocument(item)}
                    </Flex>
                    <Button
                      fullWidth
                      onClick={() => viewWorkerDetails(item._id)}
                      size="sm"
                      variant="outline"
                    >
                      詳細
                    </Button>
                  </Flex>
                </Box>
              </Card>
            </Box>
          ))}

          {/* Mobile Pagination */}
          {totalPages > 1 && (
            <Flex align="center" justify="center" mt={24}>
              <Flex align="center" direction="column" gap={12}>
                <Text color="gray-600" size="sm" ta="center">
                  レコード {startIndex + 1}-{Math.min(endIndex, data.length)} /
                  合計 {data.length}
                </Text>
                <Pagination
                  onChange={setCurrentPage}
                  radius="sm"
                  size="sm"
                  spacing={4}
                  sx={(theme: any) => ({
                    'button.mantine-Pagination-control': {
                      backgroundColor: 'white',
                      borderColor: theme.colors['gray-200'],
                      color: theme.colors['gray-600'],
                      minWidth: '32px',
                      height: '32px',
                    },
                    'button.mantine-Pagination-control:hover': {
                      backgroundColor: theme.colors['gray-100'],
                    },
                    'button[data-active=true]': {
                      backgroundColor: 'white',
                      borderColor: theme.colors.primary,
                      color: theme.colors.primary,
                      fontWeight: 600,
                    },
                    '.mantine-Pagination-control:not([disabled])': {
                      '&:first-of-type, &:last-child': {
                        color: theme.colors.primary,
                      },
                    },
                  })}
                  total={totalPages}
                  value={currentPage}
                />
              </Flex>
            </Flex>
          )}
        </Flex>
      </MediaQuery>
      <Modal
        buttonProps={{
          cancel: {
            onClick: closeEditModal,
          },
          submit: {
            onClick: handleOnEditWorker,
            disabled: !formState.isValid || !formState.isDirty,
            loading: isEditing,
          },
        }}
        onClose={closeEditModal}
        opened={modalData.visible}
        title={'提出資料の追加'}
      >
        <FormProvider {...methods}>
          <MatchingWorkerUpdateForm />
        </FormProvider>
      </Modal>
    </>
  );
};

export default WorkerList;
