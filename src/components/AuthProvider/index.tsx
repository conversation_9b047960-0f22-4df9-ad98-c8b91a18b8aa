import { BroadcastChannel } from 'broadcast-channel';
import { useGlobalState, useLogout } from 'hooks';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

const AuthProvider = () => {
  const { reload } = useRouter();
  const { logout } = useLogout();
  const { authChannel, setAuthChannel } = useGlobalState();

  useEffect(() => {
    if (authChannel) {
      authChannel.onmessage = (message) => {
        if (message === 'logout') {
          logout();
        }
        if (message === 'login') {
          reload();
        }
      };
    }
  }, [authChannel, logout, reload]);

  useEffect(() => {
    if (!authChannel) {
      setAuthChannel(new BroadcastChannel('auth'));
    }
  }, [authChannel, setAuthChannel]);

  return null;
};

export default AuthProvider;
