import IconAdd from '@icons/icon-add.svg';
import IconCheck from '@icons/icon-check.svg';
import type { MantineTheme } from '@mantine/core';
import {
  Badge,
  Text,
  ThemeIcon,
  UnstyledButton,
  useMantineTheme,
} from '@mantine/core';

export function JobTypeFilterTag<TData>({
  label,
  selected = false,
  onClick,
  value,
}: Omit<TData, 'value' | 'label'> & {
  selected?: boolean;
  label: string;
  value: string;
  onClick: (selectedValue: string) => void;
}) {
  const theme = useMantineTheme();
  return (
    <Badge
      className="curved"
      color={selected ? 'neutral-50' : 'gray-200'}
      h={'36px'}
      p={'8px 8px 8px 12px'}
      sx={() => ({
        '&:not(:last-child)': {
          marginRight: '12px',
          marginBottom: '12px',
        },
      })}
    >
      <UnstyledButton
        onClick={() => onClick(value)}
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          '> svg': {
            height: theme.spacing.md,
            width: theme.spacing.md,
            marginLeft: '6px',
          },
        }}
      >
        <Text color={selected ? 'primary' : 'gray-700'} size={14} weight={400}>
          {label.toLocaleUpperCase()}
        </Text>
        <ThemeIcon color={selected ? 'primary' : 'gray-700'} size={16}>
          {selected ? (
            <IconCheck height={16} width={16} />
          ) : (
            <IconAdd height={16} width={16} />
          )}
        </ThemeIcon>
      </UnstyledButton>
    </Badge>
  );
}

function Tag({ text }: { text: string }) {
  return (
    <Badge
      color={'neutral-50'}
      sx={(theme: MantineTheme) => ({
        '&:not(:last-child)': {
          marginRight: theme.spacing.tight,
        },
      })}
    >
      <Text color="primary" size={14} weight={400}>
        {text}
      </Text>
    </Badge>
  );
}
export default Tag;
