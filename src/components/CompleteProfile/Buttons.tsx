import { But<PERSON>, Card, Flex } from '@mantine/core';
import React from 'react';

interface IButtonsProps {
  cancelButtonLabel?: string;
  cancelButtonDisabled?: boolean;
  submitButtonLabel?: string;
  isSubmitButtonLoading?: boolean;
  submitButtonDisabled?: boolean;
  onCancelButtonClick?: () => void;
  onSubmitButtonClick?: () => void;
}

const Buttons = ({
  cancelButtonLabel,
  onCancelButtonClick,
  isSubmitButtonLoading,
  submitButtonLabel,
  submitButtonDisabled,
  onSubmitButtonClick,
}: IButtonsProps) => {
  return (
    <Card
      bottom={0}
      mt={32}
      pos="sticky"
      px={16}
      py={{ base: 12, sm: 16 }}
      radius={0}
      w={'100%'}
    >
      <Flex
        align={'center'}
        direction={'row'}
        gap={{ base: 8, sm: 16 }}
        justify={'center'}
      >
        <Button
          fullWidth
          maw={168}
          onClick={onCancelButtonClick}
          variant="outline"
        >
          {cancelButtonLabel || 'キャンセル'}
        </Button>
        <Button
          disabled={submitButtonDisabled}
          fullWidth
          loading={isSubmitButtonLoading}
          maw={168}
          onClick={onSubmitButtonClick}
        >
          {submitButtonLabel || '次へ'}
        </Button>
      </Flex>
    </Card>
  );
};

export default Buttons;
