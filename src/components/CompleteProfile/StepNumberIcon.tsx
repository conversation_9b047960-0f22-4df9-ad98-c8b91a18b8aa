import RoundedTickIcon from '@icons/icon-tick-rounded.svg';
import { Box, Text } from '@mantine/core';
import React from 'react';

interface IStepNumberIconProps {
  state?: 'active' | 'inactive' | 'done';
  label?: string;
}

const StepNumberIcon = ({
  state = 'inactive',
  label,
}: IStepNumberIconProps) => {
  if (state === 'done') {
    return (
      <Box
        sx={{
          svg: {
            display: 'flex',
            flexShrink: 0,
          },
        }}
      >
        <RoundedTickIcon />
      </Box>
    );
  }

  return (
    <Box p={4}>
      <Box
        bg={state === 'active' ? 'primary' : 'gray-original'}
        h={24}
        pos={'relative'}
        sx={(theme) => ({
          borderRadius: 12,
          '&::before': {
            content: state === 'active' ? '""' : undefined,
            position: 'absolute',
            width: 32,
            height: 32,
            zIndex: 1,
            backgroundColor: theme.colors.primary,
            top: -4,
            left: -4,
            borderRadius: 16,
            opacity: 0.2,
          },
        })}
        w={24}
      >
        <Box
          display={'flex'}
          h={'100%'}
          left={0}
          pos={'absolute'}
          sx={{ alignItems: 'center', justifyContent: 'center' }}
          top={0}
          w={'100%'}
        >
          <Text color="white" fw={700} fz={14} lh={'14px'}>
            {label}
          </Text>
        </Box>
      </Box>
    </Box>
  );
};

export default StepNumberIcon;
