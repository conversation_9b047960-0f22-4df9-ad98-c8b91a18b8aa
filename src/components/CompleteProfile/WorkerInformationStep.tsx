import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Card, Divider, Text } from '@mantine/core';
import WorkerProfileForm from 'components/Profile/WorkerProfileForm';
import type { WorkerProfileFormValues } from 'components/Profile/WorkerProfileForm/schema';
import schema from 'components/Profile/WorkerProfileForm/schema';
import { useFetch, useGlobalState, useMutate, useUser } from 'hooks';
import { omit } from 'lodash';
import authQuery from 'models/auth';
import {
  type IWorkerProfileRequest,
  NoNationalCertification,
  NoRequired,
} from 'models/auth/type';
import jobQuery from 'models/job';
import type { IJobType } from 'models/job/type';
import resourceQuery from 'models/resource';
import type { IPrefecture, ISelectableOption } from 'models/resource/type';
import { useRouter } from 'next/router';
import React, { useCallback } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import { COMPANY_PROPS, WorkerType } from 'utils/constants';
import helpers from 'utils/helpers';

import Buttons from './Buttons';

const WorkerInformationStep = () => {
  const { referrerCompleteProfile } = useGlobalState();
  const router = useRouter();
  const { refetch } = useUser();
  const methods = useForm<WorkerProfileFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
    defaultValues: {
      type: WorkerType.COMPANY_WORKER,
      hasConstructionCertifications: 'false',
      hasInsurances: 'false',
      hasNationalCertifications: 'false',
      // Any other fields that need default values should be here
    },
  });
  const { data: jobTypes } = useFetch<
    { jobTypes: IJobType[] },
    ISelectableOption[]
  >({
    ...jobQuery.getJobTypes,
    select: (data) =>
      data.jobTypes.map((item) => ({ label: item.name, value: item._id })),
  });

  const { data: prefectureOptions } = useFetch<
    IPrefecture[],
    ISelectableOption[]
  >({
    ...resourceQuery.prefectures,
    select: (data) =>
      data.map((item) => ({
        label: item.prefecture_kanji,
        value: item.prefecture_kanji,
      })),
  });

  const { mutateAsync: onSetWorkerProfile, isLoading: isSubmitting } =
    useMutate<IWorkerProfileRequest>({
      ...authQuery.updateWorkerInfo,
      successMessage: '完了しました。',
      onSuccess: () => {
        helpers.logEventTracking('complete_profile');
      },
    });

  const handleBack = useCallback(() => {
    router.replace(
      {
        pathname: router.pathname,
        query: {
          step: 1,
        },
      },
      undefined,
      {
        shallow: true,
      },
    );
  }, [router]);

  const onSubmit: SubmitHandler<WorkerProfileFormValues> = async (values) => {
    const params: IWorkerProfileRequest = {
      ...omit(values, [
        'certificationDocuments',
        'jobTypes',
        'birthDate',
        'type',
        'hasConstructionCertifications',
        'constructionCertifications',
        'hasInsurances',
        'insurances',
        'hasNationalCertifications',
        'nationalCertifications',
        ...(values.type === WorkerType.COMPANY_WORKER ? [] : COMPANY_PROPS),
      ]),
      type: values.type || WorkerType.COMPANY_WORKER,
      jobTypes: values.jobTypes.map((j) => j || '') || [],
      cvDocumentKeys: [],
      applicationDocumentKeys: [],
      constructionCertifications:
        values.hasConstructionCertifications === 'true' &&
        values.type === WorkerType.COMPANY_WORKER
          ? values.constructionCertifications
          : [NoRequired],
      nationalCertifications:
        values.hasNationalCertifications === 'true'
          ? values.nationalCertifications
          : [NoNationalCertification],
      insurances:
        values.hasInsurances === 'true' ? values.insurances : [NoRequired],
      birthDate: values.birthDate.toISOString(),
    };

    await onSetWorkerProfile(params);
    await refetch();
    router.replace(referrerCompleteProfile);
  };

  return (
    <FormProvider {...methods}>
      <Box pos="relative" sx={{ flex: 1 }} w={'100%'}>
        <Card
          maw={784}
          mx="auto"
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              borderRadius: 0,
            },
          })}
          w="100%"
        >
          <Text
            fw={700}
            fz={18}
            lh="26px"
            pb={{ base: 20, sm: 24 }}
            pt={16}
            px={{ base: 16, sm: 24 }}
          >
            受注者情報
          </Text>
          <Divider color="gray-400" />
          <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
            <WorkerProfileForm
              jobTypeOptions={jobTypes}
              prefectureOptions={prefectureOptions}
            />
          </Box>
        </Card>
        <Buttons
          cancelButtonLabel={'戻る'}
          isSubmitButtonLoading={isSubmitting}
          onCancelButtonClick={handleBack}
          onSubmitButtonClick={methods.handleSubmit(onSubmit)}
          submitButtonDisabled={!methods.formState.isValid}
          submitButtonLabel={'確定'}
        />
      </Box>
    </FormProvider>
  );
};

export default WorkerInformationStep;
