import { Box, Card, Flex, Text } from '@mantine/core';
import React from 'react';

import StepNumberIcon from './StepNumberIcon';

interface IStepProgress {
  step?: number;
}

const STEPS: { id: number; label: string }[] = [
  { id: 1, label: '基本情報' },
  { id: 2, label: 'その他の情報' },
];

const StepProgress = ({ step = 1 }: IStepProgress) => {
  return (
    <Card
      maw={784}
      py={{ base: 16, sm: 24 }}
      shadow="sm"
      sx={(theme) => ({
        [theme.fn.smallerThan('sm')]: {
          borderRadius: 0,
        },
      })}
      w="100%"
    >
      <Flex
        align={'center'}
        direction={'row'}
        gap={{ base: 12, sm: 16 }}
        justify={'center'}
      >
        {STEPS.map((s, idx) => (
          <React.Fragment key={`step-${idx}`}>
            <Flex
              align={'center'}
              direction={'row'}
              gap={{ base: 4, sm: 8 }}
              key={`step-${idx}`}
            >
              <StepNumberIcon
                label={s.id.toString()}
                state={
                  (step === s.id && 'active') ||
                  (step > s.id && 'done') ||
                  'inactive'
                }
              />
              <Text
                color={s.id === step ? 'primary' : 'gray-original'}
                fz={{ base: 14, sm: 16 }}
              >
                {s.label}
              </Text>
            </Flex>
            {idx !== STEPS.length - 1 ? (
              <Box bg={'gray-original'} h={1} w={{ base: 32, sm: 64 }} />
            ) : null}
          </React.Fragment>
        ))}
      </Flex>
    </Card>
  );
};

export default StepProgress;
