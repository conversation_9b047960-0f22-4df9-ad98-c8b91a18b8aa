import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Card, Divider, Text } from '@mantine/core';
import BasicInfomationForm from 'components/Profile/BasicInfomationForm';
import type { BasicProfileFormValues } from 'components/Profile/BasicInfomationForm/schema';
import schema from 'components/Profile/BasicInfomationForm/schema';
import { useLogout, useMutate, useUser } from 'hooks';
import authQuery from 'models/auth';
import React, { useEffect, useMemo } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import helpers from 'utils/helpers';

import Buttons from './Buttons';

const BasicInfomationStep = ({ onComplete }: { onComplete: () => void }) => {
  const { data: currentUser, refetch } = useUser();
  const { confirmLogout } = useLogout();

  const { mutateAsync: updateBasicInfo, isLoading } = useMutate(
    authQuery.updateBasicInfo,
  );

  const defaultValues = useMemo(
    () => ({
      avatarKey: currentUser?.avatar ? [currentUser.avatar] : [],
      firstName: currentUser?.firstName || '',
      lastName: currentUser?.lastName || '',
      firstNameKata: currentUser?.firstNameKata || '',
      lastNameKata: currentUser?.lastNameKata || '',
      phone: currentUser?.phone || '',
    }),
    [currentUser],
  );

  const methods = useForm<BasicProfileFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
    defaultValues: {
      avatarKey: currentUser?.avatar ? [currentUser.avatar] : [],
      firstName: currentUser?.firstName || '',
      lastName: currentUser?.lastName || '',
      firstNameKata: currentUser?.firstNameKata || '',
      lastNameKata: currentUser?.lastNameKata || '',
      phone: currentUser?.phone || '',
    },
  });

  useEffect(() => {
    if (currentUser) {
      methods.reset(defaultValues);
    }
  }, [currentUser, defaultValues, methods]);

  const onSubmit: SubmitHandler<BasicProfileFormValues> = async ({
    avatarKey,
    ...values
  }) => {
    helpers.logEventTracking('submit_basic_info');
    await updateBasicInfo({
      ...values,
      avatarKey: avatarKey?.length ? avatarKey[0].key : null,
    });
    await refetch();
    onComplete();
  };

  return (
    <FormProvider {...methods}>
      <Box pos="relative" sx={{ flex: 1 }} w={'100%'}>
        <Card
          maw={784}
          mx="auto"
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              borderRadius: 0,
            },
          })}
          w="100%"
        >
          <Text
            fw={700}
            fz={18}
            lh="26px"
            pb={{ base: 20, sm: 24 }}
            pt={16}
            px={{ base: 16, sm: 24 }}
          >
            基本情報
          </Text>
          <Divider color="gray-400" />
          <Box px={{ base: 16, sm: 24 }} py={{ base: 24, sm: 32 }}>
            <BasicInfomationForm />
          </Box>
        </Card>
        <Buttons
          isSubmitButtonLoading={isLoading}
          onCancelButtonClick={confirmLogout}
          onSubmitButtonClick={methods.handleSubmit(onSubmit)}
          submitButtonDisabled={!methods.formState.isValid}
        />
      </Box>
    </FormProvider>
  );
};

export default BasicInfomationStep;
