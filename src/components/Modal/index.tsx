import type {
  ButtonProps,
  ModalProps as DefaultModalProps,
} from '@mantine/core';
import { Box, Button, Flex, Modal as DefaultModal } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import React from 'react';

import useStyles from './styles';

interface ModalProps extends DefaultModalProps {
  children: React.ReactElement;
  labels?: {
    cancel?: string;
    submit?: string;
  };
  buttonProps?: {
    cancel?: ButtonProps & React.HTMLAttributes<HTMLButtonElement>;
    submit?: ButtonProps & React.HTMLAttributes<HTMLButtonElement>;
  };
}

const Modal = ({ children, labels, buttonProps, ...props }: ModalProps) => {
  const { classes } = useStyles();
  const isMobile = useMediaQuery('(max-width: 48em');

  return (
    <DefaultModal
      fullScreen={isMobile}
      {...props}
      className={classes.modal}
      size={784}
    >
      <Flex direction={'column'} h={'100%'}>
        <Box
          px={{ base: 16, sm: 24 }}
          py={{ base: 24, sm: 32 }}
          sx={(theme) => ({
            flex: 1,
            borderBottom: `1px solid ${theme.colors['gray-300']}`,
            [theme.fn.smallerThan('sm')]: {
              borderBottom: 0,
            },
          })}
        >
          {children}
        </Box>
        <Flex
          align={'center'}
          bg="white"
          bottom={0}
          gap={{ base: 8, sm: 16 }}
          justify={'flex-end'}
          pos="sticky"
          px={{ base: 16, sm: 24 }}
          py={{ base: 12, sm: 16 }}
          w="100%"
        >
          <Button
            miw={100}
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                flex: 1,
              },
            })}
            variant="outline"
            {...buttonProps?.cancel}
          >
            {labels?.cancel || 'キャンセル'}
          </Button>
          <Button
            miw={100}
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                flex: 1,
              },
            })}
            {...buttonProps?.submit}
          >
            {labels?.submit || '保存する'}
          </Button>
        </Flex>
      </Flex>
    </DefaultModal>
  );
};

export default Modal;
