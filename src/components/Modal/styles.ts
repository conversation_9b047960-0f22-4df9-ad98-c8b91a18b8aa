import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  modal: {
    '.mantine-Modal-content': {
      display: 'flex',
      flexDirection: 'column',
    },
    '.mantine-Modal-inner': {
      [theme.fn.largerThan('sm')]: {
        alignItems: 'center',
      },
    },
    '.mantine-Modal-title': {
      [theme.fn.smallerThan('sm')]: {
        textAlign: 'center',
        padding: '20px 32px 18px',
        lineHeight: '26px',
      },
    },
    '.mantine-Modal-close': {
      [theme.fn.smallerThan('sm')]: {
        top: 18,
        right: 14,
      },
    },
    '.mantine-Modal-body': {
      height: '100%',
    },
  },
}));

export default useStyles;
