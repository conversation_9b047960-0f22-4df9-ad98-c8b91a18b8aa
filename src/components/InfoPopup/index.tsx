import { Button, Flex, Text } from '@mantine/core';
import Link from 'next/link';
import React from 'react';

interface InfoPopupProps {
  icon: React.ReactNode;
  title: string;
  description: string | React.ReactNode;
  submitButtonLabel: string;
  isLink?: boolean;
  href?: string;
  submitButtonLoading?: boolean;
  onSubmit?: () => void;
}

const InfoPopup = ({
  icon,
  title,
  description,
  submitButtonLabel,
  isLink,
  href = '/',
  submitButtonLoading,
  onSubmit,
}: InfoPopupProps) => {
  const SubmitButton = () => {
    if (isLink) {
      return (
        <Link href={href} style={{ alignSelf: 'stretch' }}>
          <Button fullWidth loading={submitButtonLoading}>
            {submitButtonLabel}
          </Button>
        </Link>
      );
    }

    return (
      <Button fullWidth loading={submitButtonLoading} onClick={onSubmit}>
        {submitButtonLabel}
      </Button>
    );
  };

  return (
    <Flex align={'center'} direction={'column'} w="100%">
      {icon}
      <Text
        align="center"
        fw={700}
        fz={18}
        lh="26px"
        mb={{ base: 8, sm: 16 }}
        mt={4}
      >
        {title}
      </Text>
      {typeof description === 'string' ? (
        <Text
          align="center"
          fw={400}
          fz={{ base: 14, sm: 16 }}
          lh={{ base: '20px', sm: '24px' }}
          mb={32}
        >
          {description}
        </Text>
      ) : (
        description
      )}
      <SubmitButton />
    </Flex>
  );
};

export default InfoPopup;
