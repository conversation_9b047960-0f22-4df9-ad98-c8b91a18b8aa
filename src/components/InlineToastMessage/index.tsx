import type { FlexProps, TextProps } from '@mantine/core';
import { Flex, Text, ThemeIcon } from '@mantine/core';
import React from 'react';
import { toastOptions } from 'utils/helpers';

import useStyles from './styles';

interface IInlineToastMessageProps {
  visible?: boolean;
  message?: string | React.ReactNode;
  wrapperProps?: FlexProps;
  messageProps?: TextProps;
  type?: 'success' | 'error' | 'status' | 'warning';
}

const InlineToastMessage = ({
  visible,
  message,
  wrapperProps,
  messageProps,
  type = 'success',
}: IInlineToastMessageProps) => {
  const { classes } = useStyles();
  if (!visible) {
    return null;
  }

  return (
    <Flex
      align={'center'}
      bg={toastOptions[type].bg}
      className={classes.toastMessage}
      color={toastOptions[type].color}
      direction={'row'}
      gap={16}
      px={24}
      py={16}
      w="100%"
      {...wrapperProps}
    >
      <ThemeIcon size={24}>{toastOptions[type].icon}</ThemeIcon>
      <Text
        color={toastOptions[type].color}
        fw={400}
        fz={{ base: 'xs', sm: 'sm' }}
        lh={{ base: '16px', sm: '20px' }}
        sx={{ flex: 1 }}
        {...messageProps}
      >
        {message}
      </Text>
    </Flex>
  );
};

export default InlineToastMessage;
