import type { InferType } from 'yup';
import { array, object, string } from 'yup';

const schema = object({
  cvDocuments: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
      }),
    )
    .max(15)
    .required(),
});

export default schema;
export type MatchingUpdateFormValues = InferType<typeof schema>;
