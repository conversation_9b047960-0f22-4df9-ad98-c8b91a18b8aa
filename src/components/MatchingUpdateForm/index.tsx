import { Box } from '@mantine/core';
import { FileUpload } from 'components/Form';
import UploadFileList from 'components/Form/UploadFileList';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { FILE_UPLOAD_TYPES } from 'utils/constants';
import helpers from 'utils/helpers';

const MatchingUpdateForm = () => {
  const { control } = useFormContext();

  return (
    <Box component="form">
      <FileUpload
        accept={FILE_UPLOAD_TYPES}
        control={control}
        max={15}
        maxSize={10}
        multiple
        name="cvDocuments"
        onUploadSuccess={() => {
          helpers.logEventTracking('upload_resume', {
            reference_page: 'matching_detail',
          });
        }}
        render={(props) => (
          <UploadFileList
            acceptTypes={props.accept}
            data={props.values}
            error={props.error}
            label={'名簿ファイル'}
            loading={props.isUploading}
            max={15}
            onAdd={() => props.ref.current?.click()}
            onRemove={props.remove}
            required
          />
        )}
      />
    </Box>
  );
};

export default MatchingUpdateForm;
