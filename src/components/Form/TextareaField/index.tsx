import type { TextareaProps } from '@mantine/core';
import { Textarea } from '@mantine/core';
import React from 'react';
import {
  type Control,
  type FieldValues,
  type Path,
  useController,
} from 'react-hook-form';

import Description from '../Description';
import Label from '../Label';

interface TextareaFieldProps<TFormValues extends FieldValues>
  extends Omit<TextareaProps, 'value'> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  extra?: React.ReactNode;
  required?: boolean;
  transformValue?: (value: string) => string;
}

const TextareaField = <TFormValues extends FieldValues>({
  name,
  control,
  required,
  label,
  defaultValue,
  maxLength,
  ...props
}: TextareaFieldProps<TFormValues>) => {
  const {
    field: { ref, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <Textarea
      {...field}
      defaultValue={defaultValue}
      description={
        (error?.message || maxLength) && (
          <Description
            error={error?.message}
            maxLength={maxLength}
            valueLength={(field.value || '').length}
          />
        )
      }
      error={!!error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      label={label && <Label label={label} required={required} />}
      ref={ref}
      value={field.value || ''}
      {...props}
    />
  );
};

export default TextareaField;
