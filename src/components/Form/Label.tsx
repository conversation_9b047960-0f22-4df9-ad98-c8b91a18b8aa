import type { BoxProps } from '@mantine/core';
import { Box, Text } from '@mantine/core';
import type { ReactNode } from 'react';

const FormLabel = ({
  label,
  required,
  subTitle,
  ...rest
}: {
  label: ReactNode;
  required?: boolean;
  subTitle?: ReactNode;
} & BoxProps) => {
  return (
    <Box display="flex" mb={4} sx={{ flexDirection: 'column' }} {...rest}>
      <Box
        display="flex"
        mb={4}
        sx={{ justifyContent: 'space-between' }}
        {...rest}
      >
        <Text color="gray-600" lh="20px" size={14} weight={700}>
          {label}
          {required && (
            <Text color="error-300" component="span" fw={500} ml={2}>
              ※
            </Text>
          )}
        </Text>
      </Box>
      {subTitle && (
        <Text color="#9CA3AF" fw={400} fz={12} lh="16px">
          {subTitle}
        </Text>
      )}
    </Box>
  );
};

export default FormLabel;
