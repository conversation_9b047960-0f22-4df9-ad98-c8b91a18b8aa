import { CloseButton, Flex, Stack } from '@mantine/core';
import type { Omit } from 'lodash';
import type { IFile } from 'models/auth/type';
import Image from 'next/image';
import React from 'react';

interface ListFileItemProps extends Omit<IFile, '_id' | 'key'> {
  canRemoved?: boolean;
  onRemove?: () => void;
}

const ListFileItem = ({
  originUrl,
  originalName,
  onRemove,
  canRemoved = true,
}: ListFileItemProps) => {
  return (
    <Flex
      align={'center'}
      className="uploadMediaItem"
      direction={'row'}
      pos={'relative'}
      sx={{
        borderRadius: 'inherit',
        overflow: 'hidden',
        width: '100%',
        height: '100%',
        aspectRatio: '1',
      }}
    >
      <Stack
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
          aspectRatio: '1',
        }}
      >
        <Image
          alt={originalName}
          fill
          src={originUrl}
          style={{
            objectFit: 'contain',
            borderRadius: 'inherit',
          }}
        />
      </Stack>
      {canRemoved && (
        <CloseButton
          onClick={onRemove}
          sx={{
            position: 'absolute',
            color: 'white',
            background: '#1F2937',
            borderRadius: '50%',
            top: 8,
            right: 8,
            zIndex: 10,
          }}
          variant="transparent"
        />
      )}
    </Flex>
  );
};

export default ListFileItem;
