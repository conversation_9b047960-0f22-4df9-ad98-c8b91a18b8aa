import IconMediaUpload from '@icons/icon-media-upload.svg';
import type { BoxProps } from '@mantine/core';
import { Flex, Progress, Stack, Text } from '@mantine/core';
import isEmpty from 'lodash/isEmpty';
import type { IFile } from 'models/auth/type';
import React, { useEffect, useMemo, useState } from 'react';
import type { FieldError } from 'react-hook-form';

import FormDescription from '../Description';
import FormLabel from '../Label';
import ListFileItem from './ListFileItem';

interface UploadImageFileListProps {
  loading?: boolean;
  data: IFile[];
  onAdd?: () => void;
  onRemove?: (idx: number) => void;
  error?: FieldError;
  max?: number;
  label?: string;
  labelProps?: BoxProps;
  required?: boolean;
  inlineToastMessage?: boolean;
  acceptTypes?: string;
  progress?: number;
  hideUploadButton?: boolean;
}

const UploadImageFileList = ({
  data,
  loading,
  onRemove,
  onAdd,
  max = 10,
  label,
  labelProps,
  error,
  required = false,
  inlineToastMessage = false,
  acceptTypes = '.doc, .pdf, .png, .jpeg',
  progress = 25,
  hideUploadButton = false,
  ...props
}: UploadImageFileListProps) => {
  const [delayedLoading, setDelayedLoading] = useState(loading);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayedLoading(loading);
    }, 100);

    return () => clearTimeout(timer); // Cleanup on unmount or dependency change
  }, [loading]);

  const isLoading = useMemo(() => {
    return delayedLoading;
  }, [delayedLoading]);

  const handleRemove = (idx: number) => {
    if (onRemove) {
      onRemove(idx);
    }
  };
  const {
    customClass,
    multiple,
  }: Partial<{
    customClass: string;
    multiple: boolean;
  }> = props;

  return (
    <Flex direction="column" h="100%" w={'100%'}>
      <Flex className={customClass} h="100%" w={'100%'}>
        <FormLabel label={label} required={required} {...labelProps} />

        <>
          {multiple ? (
            <Flex direction="row" gap={8} pt={16} px={0} w="100%" wrap="wrap">
              {!hideUploadButton && (
                <Flex
                  h={164}
                  sx={{
                    aspectRatio: '1',
                    minHeight: 164,
                  }}
                  w={{
                    base: 'calc(50% - 4px)',
                    sm: 'calc(25% - 6px)',
                  }}
                >
                  <Flex
                    align="center"
                    direction="column"
                    h="100%"
                    justify="center"
                    style={{
                      backgroundImage: error?.message
                        ? `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='2' ry='2' stroke='%23B91C1CFF' stroke-width='2' stroke-dasharray='8' stroke-dashoffset='42' stroke-linecap='square'/%3e%3c/svg%3e")`
                        : `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='2' ry='2' stroke='%23B8BEC8FF' stroke-width='2' stroke-dasharray='8' stroke-dashoffset='42' stroke-linecap='square'/%3e%3c/svg%3e")`,
                    }}
                    w="100%"
                  >
                    <Stack align="center" w="100%">
                      <Flex
                        align="center"
                        bg="#F3F4F6"
                        h={56}
                        justify="center"
                        px={18}
                        py={16}
                        sx={{
                          borderRadius: '50%',
                        }}
                        w={56}
                      >
                        <IconMediaUpload />
                      </Flex>
                      {isLoading ? (
                        <Stack px={16} w="100%">
                          <Progress size="sm" value={progress as number} />
                          <Text
                            color="#2B3033"
                            fz="12px"
                            sx={{
                              textAlign: 'center',
                              transform: 'translateY(-8px)',
                            }}
                          >
                            {progress}%
                          </Text>
                        </Stack>
                      ) : (
                        <Text
                          c={
                            !!max && data.length >= max ? '#9ca3af' : '#0162D1'
                          }
                          fz="12px"
                          lh="16px"
                          onClick={onAdd}
                          style={{
                            fontWeight: 500,
                            cursor:
                              !!max && data.length >= max
                                ? 'not-allowed'
                                : 'pointer',
                            ...(!!max &&
                              data.length >= max && {
                                pointerEvents: 'none',
                              }),
                          }}
                          variant="outline"
                        >
                          画像アップロード
                        </Text>
                      )}
                    </Stack>
                  </Flex>
                </Flex>
              )}

              {data.map((value, idx) => (
                <Flex
                  h={{
                    base: 164,
                    sm: 164,
                  }}
                  key={`${value._id}-${idx}`}
                  sx={{
                    borderRadius: 2,
                    border: isEmpty(data)
                      ? 'none'
                      : `1px solid ${error?.message ? '#b91c1c' : '#ECECEC'}`,
                    aspectRatio: '1',
                    minHeight: 164,
                  }}
                  w={{
                    base: '100%',
                    sm: 'calc(25% - 6px)',
                  }}
                >
                  <ListFileItem
                    canRemoved={!value.readOnly}
                    onRemove={() => handleRemove(idx)}
                    originUrl={value.originUrl}
                    originalName={value.originalName}
                    url={value.url}
                  />
                </Flex>
              ))}
            </Flex>
          ) : (
            <Flex
              align="center"
              direction="column"
              gap={8}
              h={'100%'}
              justify="center"
              px={isEmpty(data) ? 24 : 0}
              py={isEmpty(data) ? 24 : 0}
              style={{
                ...(isEmpty(data) && {
                  backgroundImage: error?.message
                    ? `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='8' ry='8' stroke='%23B91C1CFF' stroke-width='3' stroke-dasharray='13%2c 9' stroke-dashoffset='90' stroke-linecap='square'/%3e%3c/svg%3e")`
                    : `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='8' ry='8' stroke='%23B8BEC8FF' stroke-width='3' stroke-dasharray='13%2c 9' stroke-dashoffset='90' stroke-linecap='square'/%3e%3c/svg%3e")`,
                }),
              }}
              sx={{
                border: isEmpty(data)
                  ? 'none'
                  : `1px solid ${error?.message ? '#b91c1c' : '#B8BEC8'}`,
              }}
              w="100%"
            >
              {data.map((value, idx) => (
                <ListFileItem
                  canRemoved={!value.readOnly}
                  key={`${value._id}-${idx}`}
                  onRemove={() => handleRemove(idx)}
                  originUrl={value.originUrl}
                  originalName={value.originalName}
                  url={value.url}
                />
              ))}
              {isEmpty(data) && !hideUploadButton ? (
                <>
                  <Flex
                    align="center"
                    bg="#F3F4F6"
                    justify="center"
                    px={18}
                    py={16}
                    sx={{
                      borderRadius: '50%',
                    }}
                  >
                    <IconMediaUpload />
                  </Flex>

                  {isLoading ? (
                    <Stack w="100%">
                      <Progress size="sm" value={progress as number} />
                      <Text
                        color="#2B3033"
                        fz="12px"
                        sx={{
                          textAlign: 'center',
                          transform: 'translateY(-8px)',
                        }}
                      >
                        {progress}%
                      </Text>
                    </Stack>
                  ) : (
                    <>
                      <Text
                        color="gray-500"
                        fw={400}
                        fz="12px"
                        lh="16px"
                        sx={{
                          textAlign: 'center',
                        }}
                      >
                        {`添付ファイルは${acceptTypes
                          .split(',')
                          .map((v) => v.trim())
                          .join('、')
                          .toUpperCase()
                          .replaceAll(
                            '.',
                            '',
                          )}形式でアップロードしてください。`}
                      </Text>
                      <Text
                        c={!!max && data.length >= max ? '#9ca3af' : '#0162D1'}
                        fz="12px"
                        lh="16px"
                        onClick={onAdd}
                        style={{
                          fontWeight: 500,
                          cursor:
                            !!max && data.length >= max
                              ? 'not-allowed'
                              : 'pointer',
                          ...(!!max &&
                            data.length >= max && {
                              pointerEvents: 'none',
                            }),
                        }}
                        variant="outline"
                      >
                        画像アップロード
                      </Text>
                    </>
                  )}
                </>
              ) : (
                <></>
              )}
            </Flex>
          )}
        </>
      </Flex>
      {!inlineToastMessage && (
        <Stack className="inlineError">
          <FormDescription error={error?.message} />
        </Stack>
      )}
    </Flex>
  );
};

export default UploadImageFileList;
