import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconClose from '@icons/icon-close.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import type { MultiSelectProps } from '@mantine/core';
import { ActionIcon, ThemeIcon } from '@mantine/core';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import type { MultiSelectCheckboxProps } from '../../MultiSelectCheckbox';
import MultiSelectCheckbox from '../../MultiSelectCheckbox';
import Description from '../Description';
import Label from '../Label';

interface MultiSelectCheckboxFieldProps<TFormValues extends FieldValues>
  extends MultiSelectProps,
    Omit<MultiSelectCheckboxProps, 'value' | 'setSelected'> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  required?: boolean;
}

const MultiSelectCheckboxField = <TFormValues extends FieldValues>({
  name,
  control,
  required,
  maxLength,
  label,
  clearable,
  icon,
  ...props
}: MultiSelectCheckboxFieldProps<TFormValues>) => {
  const {
    field: { onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const values = field.value || [];

  return (
    <MultiSelectCheckbox
      {...field}
      description={
        (error?.message || maxLength) && (
          <Description
            error={error?.message}
            maxLength={maxLength}
            valueLength={(values || '').length}
          />
        )
      }
      error={!!error?.message}
      icon={
        icon || (
          <ThemeIcon color="gray-700" size={24}>
            <IconLocation height={24} width={24} />
          </ThemeIcon>
        )
      }
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      label={label && <Label label={label} required={required} />}
      rightSection={
        clearable && values.length ? (
          <ActionIcon
            color="black"
            onClick={() => onChange([])}
            size="sm"
            sx={{ pointerEvents: 'all' }}
            variant="transparent"
          >
            <IconClose height={10} width={10} />
          </ActionIcon>
        ) : (
          <IconArrowDown color={'#9CA3AF'} height={16} width={16} />
        )
      }
      setSelected={onChange}
      size="lg"
      {...props}
    />
  );
};

export default MultiSelectCheckboxField;
