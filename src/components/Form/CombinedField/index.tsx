import type { SelectProps } from '@mantine/core';
import { Box, Flex } from '@mantine/core';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import Description from '../Description';
import Label from '../Label';
import SelectField from '../SelectField';
import TextField from '../TextField';

interface CombinedFieldProps<TFormValues extends FieldValues> {
  // First field (SelectField) props
  selectName: Path<TFormValues>;
  selectData: SelectProps['data'];
  selectPlaceholder?: string;

  // Second field (TextField) props
  textName: Path<TFormValues>;
  placeholder?: string;

  // Common props
  control: Control<TFormValues>;
  label?: React.ReactNode;
  required?: boolean;
  maxLength?: number;
}

const CombinedField = <TFormValues extends FieldValues>({
  selectName,
  selectData,
  selectPlaceholder,
  textName,
  placeholder,
  control,
  label,
  required,
  maxLength,
}: CombinedFieldProps<TFormValues>) => {
  // Controllers for both fields
  const {
    fieldState: { error: selectError },
  } = useController({
    name: selectName,
    control,
  });

  const {
    fieldState: { error: textError },
  } = useController({
    name: textName,
    control,
  });

  // Determine if there's an error in either field
  const hasError = !!selectError || !!textError;
  const errorMessage = selectError?.message || textError?.message;

  return (
    <Box>
      {label && <Label label={label} required={required} />}

      <Box
        sx={(theme) => ({
          border: hasError
            ? `1px solid ${theme.colors['error-500']}`
            : `1px solid ${theme.colors['gray-original']}`,
          borderRadius: theme.radius.md,
          overflow: 'visible',
        })}
      >
        <Flex>
          {/* SelectField on the left side */}
          <Box
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                width: 130,
              },
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                right: 0,
                width: 1,
                height: '100%',
                backgroundColor: theme.colors['gray-original'],
              },
            })}
            w={{ base: '190px' }}
          >
            <SelectField
              control={control}
              data={selectData}
              error={null}
              inputWrapperOrder={['input']}
              name={selectName}
              placeholder={selectPlaceholder}
              styles={(_theme) => ({
                input: {
                  border: 'none',
                  '&:focus': {
                    border: 'none',
                  },
                },
                root: {
                  '& .mantine-Input-wrapper': {
                    border: 'none',
                  },
                  '& .mantine-InputWrapper-error': {
                    display: 'none',
                  },
                  '& .mantine-InputWrapper-description': {
                    display: 'none',
                  },
                },
              })}
            />
          </Box>

          {/* TextField on the right side */}
          <Box sx={{ flex: 1 }}>
            <TextField
              control={control}
              error={null}
              inputWrapperOrder={['input']}
              name={textName}
              placeholder={placeholder}
              styles={(_theme) => ({
                input: {
                  border: 'none',
                  paddingLeft: 16,
                  '&:focus': {
                    border: 'none',
                  },
                },
                root: {
                  '& .mantine-Input-wrapper': {
                    border: 'none',
                  },
                  '& .mantine-InputWrapper-error': {
                    display: 'none',
                  },
                  '& .mantine-InputWrapper-description': {
                    display: 'none',
                  },
                },
              })}
            />
          </Box>
        </Flex>
      </Box>

      {/* Error message display */}
      {(errorMessage || maxLength) && <Description error={errorMessage} />}
    </Box>
  );
};

export default CombinedField;
