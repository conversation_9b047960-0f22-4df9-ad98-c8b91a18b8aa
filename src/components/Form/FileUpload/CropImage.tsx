import 'react-image-crop/dist/ReactCrop.css';

import { Flex } from '@mantine/core';
import React, { useState } from 'react';
import type { Crop, PixelCrop, ReactCropProps } from 'react-image-crop';
import ReactCrop, { centerCrop, makeAspectCrop } from 'react-image-crop';

import { canvasPreview } from './canvasPreview';
import { useDebounceEffect } from './useDebounceEffect';

// This is to demonstate how to make and center a % aspect crop
// which is a bit trickier so we use some helper functions.
function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

interface CropImageProps
  extends Omit<ReactCropProps, 'onChange' | 'onComplete'> {
  imageSrc: string;
  file: File;
  imgRef: React.RefObject<HTMLImageElement>;
  previewCanvasRef: React.RefObject<HTMLCanvasElement>;
}

const CropImage: React.FC<CropImageProps> = ({
  imageSrc,
  previewCanvasRef,
  imgRef,
  aspect = 1,
  ...props
}) => {
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    if (aspect) {
      const { width, height } = e.currentTarget;
      setCrop(centerAspectCrop(width, height, aspect));
    }
  };

  useDebounceEffect(
    async () => {
      if (completedCrop && imgRef.current && previewCanvasRef.current) {
        const fullImageCrop = {
          height: imgRef.current?.height,
          width: imgRef.current?.width,
          x: 0,
          y: 0,
          unit: completedCrop.unit,
        };
        canvasPreview(
          imgRef.current,
          previewCanvasRef.current,
          !completedCrop.width && !completedCrop.height
            ? fullImageCrop
            : completedCrop,
        );
      }
    },
    100,
    [completedCrop],
  );

  return (
    <Flex
      sx={{
        'img[alt="crop"]': {
          pointerEvents: 'none',
        },
        '& > *': {
          flex: 1,
        },
      }}
    >
      <ReactCrop
        aspect={aspect}
        circularCrop
        crop={crop}
        keepSelection
        onChange={(_, p) => setCrop(p)}
        onComplete={(c) => setCompletedCrop(c)}
        {...props}
      >
        <img
          alt="Crop me"
          draggable={false}
          onLoad={onImageLoad}
          ref={imgRef}
          src={imageSrc}
          style={{
            width: '100%',
            height: '100%',
            maxWidth: 960,
            maxHeight: 'min(calc(90vh - 64px - 80px), 680px)', // exclude the header and footer
            objectFit: 'contain',
          }}
        />
      </ReactCrop>
      <canvas ref={previewCanvasRef} style={{ display: 'none' }} />
    </Flex>
  );
};

export default CropImage;
