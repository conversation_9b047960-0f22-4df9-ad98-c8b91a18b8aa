import type { FileInputProps } from '@mantine/core';
import { FileInput } from '@mantine/core';
import useUpload from 'hooks/useUpload';
import { set } from 'lodash';
import type { IFile } from 'models/auth/type';
import React, { useRef, useState } from 'react';
import type { Control, FieldError, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import helpers from 'utils/helpers';

import CropImage from './CropImage';

const readFile = (file: File) =>
  new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      }
    });
    reader.readAsDataURL(file as unknown as Blob);
  });

interface FileUploadProps<
  TFormValues extends FieldValues,
  Multiple extends boolean,
> extends Omit<FileInputProps<Multiple>, 'value' | 'onChange'> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  multiple?: Multiple;
  withCrop?: boolean;
  maxSize?: number;
  max?: number;
  enableOCRValidation?: boolean; // Enable OCR-specific validation
  onUploadSuccess?: () => void;
  render: (props: {
    isUploading: boolean;
    values: IFile[];
    ref: React.RefObject<HTMLButtonElement>;
    remove: (index: number) => void;
    error?: FieldError;
    accept: string;
    progress?: number;
  }) => React.ReactNode;
}

const FileUpload = <
  TFormValues extends FieldValues,
  Multiple extends boolean = false,
>({
  name,
  control,
  multiple,
  max = 1,
  maxSize = 5,
  withCrop,
  accept = '.jpg,.jpeg,.png',
  enableOCRValidation = false,
  render,
  onUploadSuccess,
  ...props
}: FileUploadProps<TFormValues, Multiple>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const values = field.value || [];

  const inputRef = useRef<HTMLButtonElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const [progress, setProgress] = useState(20); // Track upload progress

  const { isUploading, uploadFile, validateFile } = useUpload({
    onUploadSuccess: (data) => {
      if (max !== 1 && multiple) {
        field.onChange([...values, ...data]);
      } else {
        field.onChange(data);
      }
      setProgress(20); // Reset progress after upload
      onUploadSuccess?.();
      field.onBlur();
    },
    onUploadProgress: (percent) => {
      setProgress(percent);
    },
    validTypes: accept?.split(','),
    maxSize,
    enableOCRValidation,
  });

  const handleOnChange = async (payload: File[] | File | null) => {
    if (!payload) {
      return;
    }

    if (max !== 1 && multiple && Array.isArray(payload)) {
      let isValid = true;
      payload.forEach((file) => {
        if (!validateFile(file)) {
          isValid = false;
        }
      });
      if (!isValid) return;

      if (max < values.length + payload.length) {
        helpers.toast({ message: 'Limit reached', type: 'error' });
        return;
      }

      uploadFile({ files: payload });
      return;
    }

    if (max === 1 && !multiple && !Array.isArray(payload)) {
      if (!validateFile(payload)) return;
      if (withCrop) {
        const imageDataUrl = await readFile(payload);
        helpers.confirm({
          title: 'Edit image',
          children: (
            <CropImage
              file={payload}
              imageSrc={imageDataUrl}
              imgRef={imgRef}
              previewCanvasRef={previewCanvasRef}
            />
          ),
          onConfirm: async () => {
            const { type, name: fileName } = payload;
            previewCanvasRef.current?.toBlob(
              async (blob) => {
                const newFile = new File([blob as BlobPart], fileName, {
                  type,
                });

                uploadFile({ files: [newFile] });
              },
              type,
              0.4,
            );
          },
          size: 'auto',
          lockScroll: true,
        });
      } else {
        uploadFile({ files: [payload] });
      }
    }
  };

  const handleRemoveFile = (index: number) => {
    const currentFile = field.value || [];
    currentFile.splice(index, 1);
    field.onChange(currentFile);
    field.onBlur();
  };

  return (
    <>
      <FileInput
        {...field}
        accept={accept}
        error={!!error?.message}
        fileInputProps={{
          onClick: (e) => {
            set(e, 'target.value', null);
          },
        }}
        multiple={multiple}
        onChange={handleOnChange}
        ref={inputRef}
        sx={{ display: 'none' }}
        {...props}
      />
      {render({
        isUploading,
        values,
        ref: inputRef,
        error,
        remove: handleRemoveFile,
        accept,
        progress,
      })}
    </>
  );
};

export default FileUpload;
