/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable prefer-spread */
import type { DependencyList } from 'react';
import { useEffect } from 'react';

export function useDebounceEffect(
  fn: () => void,
  waitTime: number,
  deps?: DependencyList,
) {
  useEffect(() => {
    const t = setTimeout(() => {
      // @ts-ignore
      fn.apply(undefined, deps);
    }, waitTime);

    return () => {
      clearTimeout(t);
    };
  }, deps);
}
