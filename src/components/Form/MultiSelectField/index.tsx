import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconClose from '@icons/icon-close.svg';
import type { MultiSelectProps } from '@mantine/core';
import { ActionIcon, MultiSelect } from '@mantine/core';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import Description from '../Description';
import Label from '../Label';

interface MultiSelectFieldProps<TFormValues extends FieldValues>
  extends MultiSelectProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  required?: boolean;
}

const MultiSelectField = <TFormValues extends FieldValues>({
  name,
  control,
  required,
  maxLength,
  label,
  clearable,
  ...props
}: MultiSelectFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const values = field.value || [];

  return (
    <MultiSelect
      {...field}
      description={
        (error?.message || maxLength) && (
          <Description
            error={error?.message}
            maxLength={maxLength}
            valueLength={(field.value || '').length}
          />
        )
      }
      error={!!error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      label={label && <Label label={label} required={required} />}
      maxDropdownHeight={256}
      rightSection={
        clearable && values.length ? (
          <ActionIcon
            color="black"
            onClick={() => field.onChange([])}
            size="sm"
            sx={{ pointerEvents: 'all' }}
            variant="subtle"
          >
            <IconClose height={10} width={10} />
          </ActionIcon>
        ) : (
          <IconArrowDown height={16} width={16} />
        )
      }
      value={values}
      {...props}
    />
  );
};

export default MultiSelectField;
