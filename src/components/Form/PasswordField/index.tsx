import VisibleIcon from '@icons/icon-visible.svg';
import VisibleOffIcon from '@icons/icon-visible-off.svg';
import type { PasswordInputProps } from '@mantine/core';
import { PasswordInput } from '@mantine/core';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import Description from '../Description';
import Label from '../Label';

interface PasswordFieldProps<TFormValues extends FieldValues>
  extends PasswordInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  required?: boolean;
}

const PasswordField = <TFormValues extends FieldValues>({
  name,
  control,
  required,
  label,
  ...props
}: PasswordFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  return (
    <PasswordInput
      {...field}
      description={error?.message && <Description error={error?.message} />}
      error={!!error?.message}
      inputWrapperOrder={['label', 'error', 'input', 'description']}
      label={label && <Label label={label} required={required} />}
      value={field.value || ''}
      visibilityToggleIcon={({ reveal }) =>
        reveal ? <VisibleOffIcon /> : <VisibleIcon />
      }
      {...props}
    />
  );
};

export default PasswordField;
