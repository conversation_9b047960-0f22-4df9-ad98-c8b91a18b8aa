import IconError from '@icons/icon-warning.svg';
import { Box, Text } from '@mantine/core';
import { motion } from 'framer-motion';
import React from 'react';

const FormDescription = ({
  error,
  valueLength,
  maxLength,
}: {
  error?: string;
  valueLength?: number;
  maxLength?: number;
}) => {
  return (
    <Box
      animate={{ opacity: 1, height: 'auto' }}
      component={motion.div}
      display="flex"
      initial={error ? { opacity: 0, height: 0 } : {}}
      mt={4}
      sx={{ justifyContent: 'space-between', gap: 10 }}
      transition={{ duration: 0.1 }}
    >
      <Box display="flex" sx={{ gap: 4, alignItems: 'flex-start' }}>
        {error && <IconError />}
        <Text
          color="error-500"
          fz={{ base: 'xs', sm: 'sm' }}
          lh="20px"
          sx={{ flex: 1 }}
        >
          {error}
        </Text>
      </Box>
      {maxLength && (
        <Text color="gray-500" fw={400} fz={{ base: 'xs', sm: 'sm' }} lh="20px">
          {valueLength || 0}/{maxLength}
        </Text>
      )}
    </Box>
  );
};

export default FormDescription;
