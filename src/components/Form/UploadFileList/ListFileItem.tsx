import FileIcon from '@icons/icon-file.svg';
import { CloseButton, Flex, Text } from '@mantine/core';
import type { Omit } from 'lodash';
import type { IFile } from 'models/auth/type';
import Link from 'next/link';
import React from 'react';

interface ListFileItemProps extends Omit<IFile, '_id' | 'key'> {
  canRemoved?: boolean;
  onRemove?: () => void;
}

const ListFileItem = ({
  originUrl,
  originalName,
  onRemove,
  canRemoved = true,
}: ListFileItemProps) => (
  <Flex
    align={'center'}
    bg="gray-200"
    direction={'row'}
    gap={8}
    mb={8}
    mih={56}
    pos={'relative'}
    px={16}
    py={12}
    sx={{ borderRadius: 10 }}
  >
    <FileIcon />
    <Link href={originUrl} style={{ flex: 1 }} target="_blank">
      <Text fz={14} lh={'20px'}>
        {originalName}
      </Text>
    </Link>
    {canRemoved && (
      <CloseButton color="gray-700" onClick={onRemove} variant="transparent" />
    )}
  </Flex>
);

export default ListFileItem;
