import AttachedIcon from '@icons/icon-attached.svg';
import type { BoxProps } from '@mantine/core';
import { Box, Button, Flex, Text } from '@mantine/core';
import InlineToastMessage from 'components/InlineToastMessage';
import type { IFile } from 'models/auth/type';
import React from 'react';
import type { FieldError } from 'react-hook-form';

import FormDescription from '../Description';
import FormLabel from '../Label';
import ListFileItem from './ListFileItem';

interface FileListProps {
  loading?: boolean;
  data: IFile[];
  onAdd?: () => void;
  onRemove?: (idx: number) => void;
  error?: FieldError;
  max?: number;
  label?: string;
  labelProps?: BoxProps;
  required?: boolean;
  inlineToastMessage?: boolean;
  acceptTypes?: string;
}

const FileList = ({
  data,
  loading,
  onRemove,
  onAdd,
  max = 10,
  label,
  labelProps,
  error,
  required = false,
  inlineToastMessage = false,
  acceptTypes = '.doc, .pdf, .png, .jpeg',
}: FileListProps) => {
  const handleRemove = (idx: number) => {
    if (onRemove) {
      onRemove(idx);
    }
  };

  return (
    <Box w={'100%'}>
      <FormLabel label={label} required={required} {...labelProps} />
      {inlineToastMessage && (
        <InlineToastMessage
          message={error?.message}
          messageProps={{
            fz: { base: 14, sm: 16 },
            lh: { base: '20px', sm: '24px' },
          }}
          type="warning"
          visible={!!error?.message}
          wrapperProps={{
            mb: 8,
          }}
        />
      )}
      {data.map((value, idx) => (
        <ListFileItem
          canRemoved={!value.readOnly}
          key={`${value._id}-${idx}`}
          onRemove={() => handleRemove(idx)}
          originUrl={value.originUrl}
          originalName={value.originalName}
          url={value.url}
        />
      ))}
      <Flex
        align={{ base: undefined, sm: 'center' }}
        direction={{ base: 'column', sm: 'row' }}
        gap={12}
      >
        <Button
          disabled={!!max && data.length >= max}
          leftIcon={<AttachedIcon />}
          loading={loading}
          onClick={onAdd}
          size="sm"
          variant="outline"
        >
          ファイル追加
        </Button>
        <Text
          color="gray-500"
          fw={400}
          fz={{ base: 12, sm: 14 }}
          lh="20px"
          sx={(theme) => ({
            textAlign: 'center',
            [theme.fn.largerThan('sm')]: {
              textAlign: 'left',
            },
          })}
        >
          {`${acceptTypes
            .split(',')
            .map((v) => v.trim())
            .join(', ')}の拡張子を持っているファイルをアップロードできます。 
          10MBまでのファイルをアップロードできます。`}
        </Text>
      </Flex>

      {!inlineToastMessage && <FormDescription error={error?.message} />}
    </Box>
  );
};

export default FileList;
