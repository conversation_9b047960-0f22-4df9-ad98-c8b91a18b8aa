import type { TextInputProps } from '@mantine/core';
import { TextInput } from '@mantine/core';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { PatternFormat } from 'react-number-format';

import Description from '../Description';
import Label from '../Label';

interface TextFieldProps<TFormValues extends FieldValues>
  extends Omit<TextInputProps, 'value'> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
  format?: string;
  transformValue?: (value: string) => string;
  keepFormatted?: boolean;
}

const TextField = <TFormValues extends FieldValues>({
  name,
  control,
  required,
  format,
  type,
  defaultValue,
  maxLength,
  label,
  keepFormatted,
  ...props
}: TextFieldProps<TFormValues>) => {
  const {
    field: { ref, onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  // Format props for mask input
  if (format) {
    return (
      <PatternFormat
        {...field}
        customInput={TextInput}
        description={error?.message && <Description error={error?.message} />}
        error={!!error?.message}
        format={format}
        getInputRef={ref}
        inputWrapperOrder={['label', 'input', 'error', 'description']}
        label={label && <Label label={label} required={required} />}
        onValueChange={(values) => {
          if (keepFormatted) {
            onChange(values.formattedValue);
          } else onChange(values.value);
        }}
        value={field.value || ''}
        {...props}
      />
    );
  }

  return (
    <TextInput
      {...field}
      defaultValue={defaultValue}
      description={
        (error?.message || maxLength) && (
          <Description
            error={error?.message}
            maxLength={maxLength}
            valueLength={(field.value || '').length}
          />
        )
      }
      error={!!error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      label={label && <Label label={label} required={required} />}
      onChange={onChange}
      ref={ref}
      type={type}
      value={field.value || ''}
      {...props}
    />
  );
};

export default TextField;
