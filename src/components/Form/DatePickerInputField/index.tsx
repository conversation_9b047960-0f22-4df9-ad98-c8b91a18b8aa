import CalendarIcon from '@icons/icon-calendar.svg';
import type { DatePickerInputProps } from '@mantine/dates';
import { DatePickerInput } from '@mantine/dates';
import React from 'react';
import {
  type Control,
  type FieldValues,
  type Path,
  useController,
} from 'react-hook-form';
import dayjs from 'utils/dayjs';

import Description from '../Description';
import Label from '../Label';

interface DatePickerInputFieldProps<TFormValues extends FieldValues>
  extends DatePickerInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
}

const DatePickerInputField = <TFormValues extends FieldValues>({
  control,
  name,
  label,
  required,
  valueFormat = 'YYYY-MM-DD',
  ...props
}: DatePickerInputFieldProps<TFormValues>) => {
  const {
    field: { onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleOnChange: DatePickerInputProps['onChange'] = (date) => {
    if (!date) {
      return;
    }
    onChange(dayjs(date));
  };

  return (
    <DatePickerInput
      {...field}
      description={
        !!error?.message && (
          <Description
            error={error?.message}
            valueLength={(field.value || '').length}
          />
        )
      }
      error={!!error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      label={label && <Label label={label} required={required} />}
      onChange={handleOnChange}
      rightSection={<CalendarIcon color="#9CA3AF" height={16} width={16} />}
      valueFormat={valueFormat}
      {...props}
    />
  );
};

export default DatePickerInputField;
