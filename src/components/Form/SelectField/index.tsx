import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconClose from '@icons/icon-close.svg';
import type { SelectProps } from '@mantine/core';
import { ActionIcon, Select } from '@mantine/core';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import Description from '../Description';
import Label from '../Label';

interface SelectFieldProps<TFormValues extends FieldValues>
  extends SelectProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  required?: boolean;
}

const SelectField = <TFormValues extends FieldValues>({
  name,
  control,
  required,
  maxLength,
  label,
  clearable,
  ...props
}: SelectFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const value = field.value || '';

  return (
    <Select
      {...field}
      description={
        (error?.message || maxLength) && (
          <Description
            error={error?.message}
            maxLength={maxLength}
            valueLength={(field.value || '').length}
          />
        )
      }
      error={!!error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      label={label && <Label label={label} required={required} />}
      maxDropdownHeight={256}
      rightSection={
        clearable && value ? (
          <ActionIcon
            color="black"
            onClick={() => field.onChange('')}
            size="sm"
            sx={{ pointerEvents: 'all' }}
            variant="subtle"
          >
            <IconClose height={10} width={10} />
          </ActionIcon>
        ) : (
          <IconArrowDown height={16} width={16} />
        )
      }
      value={field.value || ''}
      {...props}
    />
  );
};

export default SelectField;
