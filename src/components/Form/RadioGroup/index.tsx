import type { CSSObject } from '@mantine/core';
import { Box, Radio } from '@mantine/core';
import React from 'react';
import {
  type Control,
  type FieldValues,
  type Path,
  useController,
} from 'react-hook-form';

import Description from '../Description';
import FormLabel from '../Label';

type DataItem = {
  label: string;
  value: string;
  checked?: boolean;
};

interface RadioGroupProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
  data: ReadonlyArray<DataItem>;
  direction?: 'vertical' | 'horizontal';
  style?: CSSObject;
}

const RadioGroup = <TFormValues extends FieldValues>({
  data,
  name,
  control,
  label,
  required,
  direction = 'horizontal',
  style,
}: RadioGroupProps<TFormValues>) => {
  const {
    field: { ref, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const dynamicStyles: CSSObject = {
    flexDirection: direction === 'horizontal' ? 'row' : 'column',
    gap: direction === 'horizontal' ? 40 : 24,
  };

  return (
    <Box>
      {label && <FormLabel label={label} required={required} />}
      <Radio.Group
        {...field}
        description={error?.message && <Description error={error?.message} />}
        display={'flex'}
        error={!!error?.message}
        ref={ref}
        required={required}
        sx={{ ...dynamicStyles, ...style }}
      >
        {data.map((item) => (
          <Radio key={item.label} {...item} />
        ))}
      </Radio.Group>
    </Box>
  );
};

export default RadioGroup;
