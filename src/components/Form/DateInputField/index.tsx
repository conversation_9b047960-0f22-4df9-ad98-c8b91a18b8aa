import CalendarIcon from '@icons/icon-calendar.svg';
import type { DateInputProps } from '@mantine/dates';
import { DateInput } from '@mantine/dates';
import React from 'react';
import {
  type Control,
  type FieldValues,
  type Path,
  useController,
} from 'react-hook-form';
import dayjs from 'utils/dayjs';

import Description from '../Description';
import Label from '../Label';

interface DatePickerInputFieldProps<TFormValues extends FieldValues>
  extends Omit<DateInputProps, 'name' | 'value' | 'ref'> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  subTitle?: React.ReactNode;
}

const DatePickerInputField = <TFormValues extends FieldValues>({
  control,
  name,
  label,
  subTitle,
  required,
  valueFormat = 'YYYY-MM-DD',
  onBlur,
  ...props
}: DatePickerInputFieldProps<TFormValues>) => {
  const {
    field: { onChange, value, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleOnChange: DateInputProps['onChange'] = (date) => {
    if (!date) {
      onChange(null);
      return;
    }
    onChange(dayjs(date));
  };

  const handleOnBlur: DateInputProps['onBlur'] = (e) => {
    field.onBlur();
    onBlur?.(e);
  };

  return (
    <DateInput
      {...field}
      description={
        !!error?.message && (
          <Description
            error={error?.message}
            valueLength={(value || '').length}
          />
        )
      }
      error={!!error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      label={
        label && <Label label={label} required={required} subTitle={subTitle} />
      }
      onBlur={handleOnBlur}
      onChange={handleOnChange}
      rightSection={<CalendarIcon color="#9CA3AF" height={16} width={16} />}
      value={value ? dayjs(value).toDate() : null}
      valueFormat={valueFormat}
      {...props}
    />
  );
};

export default DatePickerInputField;
