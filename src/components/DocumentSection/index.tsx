import { Box, Flex, Text } from '@mantine/core';
import DocumentFile from 'components/Matching/DocumentFile';
import type { IFile } from 'models/auth/type';

type TDocumentSection = {
  title: string;
  files: IFile[];
  isWrapped?: boolean;
};

const DocumentSection = ({ title, files, isWrapped }: TDocumentSection) => {
  if (isWrapped) {
    return (
      <Box mb={32}>
        <Text color="gray-600" fw={700} fz="sm" mb={12}>
          {title}
        </Text>

        <Flex gap={16} wrap="wrap">
          {files.map((doc: IFile) => (
            <Box
              key={doc.key}
              sx={(theme) => ({
                width: 'calc(25% - 12px)',
                [theme.fn.smallerThan('md')]: {
                  width: 'calc(33.333% - 12px)',
                },
                [theme.fn.smallerThan('sm')]: {
                  width: 'calc(50% - 8px)',
                },
              })}
            >
              <DocumentFile
                originUrl={doc.originUrl}
                originalName={doc.originalName}
                url={doc.originUrl}
              />
            </Box>
          ))}
        </Flex>
      </Box>
    );
  }
  return (
    <Box mb={32}>
      <Text color="gray-600" fw={700} fz="sm" mb={12}>
        {title}
      </Text>

      {files.map((doc: IFile) => (
        <DocumentFile
          key={doc.key}
          originUrl={doc.originUrl}
          originalName={doc.originalName}
          url={doc.originUrl}
        />
      ))}
    </Box>
  );
};

export default DocumentSection;
