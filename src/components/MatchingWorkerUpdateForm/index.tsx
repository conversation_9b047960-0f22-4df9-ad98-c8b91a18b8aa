import { Box } from '@mantine/core';
import { FileUpload, TextField } from 'components/Form';
import UploadFileList from 'components/Form/UploadFileList';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { FILE_UPLOAD_TYPES } from 'utils/constants';
import helpers from 'utils/helpers';

import type { MatchingWorkerUpdateFormValues } from './schema';

const MatchingWorkerUpdateForm = () => {
  const { control } = useFormContext<MatchingWorkerUpdateFormValues>();

  return (
    <Box component="form">
      <TextField
        control={control}
        disabled
        hidden
        name="_id"
        readOnly
        required
        sx={{ display: 'none' }}
      />
      <TextField
        control={control}
        disabled
        label={'応募者の名前'}
        name="workerName"
        readOnly
        required
        sx={{ marginBottom: 24 }}
      />
      <FileUpload
        accept={FILE_UPLOAD_TYPES}
        control={control}
        max={15}
        maxSize={10}
        multiple
        name="applicationDocuments"
        onUploadSuccess={() => {
          helpers.logEventTracking('upload_certification', {
            reference_page: 'matching_detail',
          });
        }}
        render={(props) => (
          <UploadFileList
            acceptTypes={props.accept}
            data={props.values}
            error={props.error}
            label={'提出資料'}
            loading={props.isUploading}
            max={15}
            onAdd={() => props.ref.current?.click()}
            onRemove={props.remove}
            required
          />
        )}
      />
    </Box>
  );
};

export default MatchingWorkerUpdateForm;
