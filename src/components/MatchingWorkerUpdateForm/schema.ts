import type { InferType } from 'yup';
import { array, boolean, object, string } from 'yup';

const schema = object({
  _id: string().required(),
  workerName: string().required(),
  applicationDocuments: array()
    .of(
      object({
        originUrl: string().trim().required(),
        originalName: string().trim().required(),
        key: string().trim().required(),
        readOnly: boolean().default(false),
      }),
    )
    .max(15)
    .required(),
});

export default schema;
export type MatchingWorkerUpdateFormValues = InferType<typeof schema>;
