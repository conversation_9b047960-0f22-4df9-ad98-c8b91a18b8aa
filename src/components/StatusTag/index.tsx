import StatusIcon from '@icons/icon-status-ellipse.svg';
import { Flex, Text } from '@mantine/core';
import React from 'react';
import type { StatusBadgeColor } from 'utils/constants';

interface StatusTagProps {
  type?: keyof typeof StatusBadgeColor;
  showIcon?: boolean;
  label: string;
}

const StatusTag = ({
  type = 'INFO',
  showIcon = true,
  label,
}: StatusTagProps) => {
  const STATUS_STYLE: Record<
    typeof type,
    { background: string; color: string }
  > = {
    WARNING: {
      background: 'warning-50',
      color: 'warning-600',
    },
    INFO: {
      background: 'info-50',
      color: 'info-500',
    },
    SUCCESS: {
      background: 'success-50',
      color: 'success-500',
    },
    ERROR: {
      background: 'error-50',
      color: 'error-500',
    },
    DISABLED: {
      background: 'gray-200',
      color: 'gray-original',
    },
    COLLECTING: {
      background: 'aqua-squeeze',
      color: 'keppel',
    },
  };

  const status = STATUS_STYLE[type];

  return (
    <Flex
      align={'center'}
      direction={'row'}
      gap={4}
      justify={'flex-end'}
      px={10}
      py={4}
      sx={(theme) => ({
        borderRadius: 10,
        background: theme.colors[status.background],
        '& > svg > circle': {
          fill: theme.colors[status.color],
        },
      })}
    >
      {showIcon && <StatusIcon />}
      <Text color={status?.color} fw={500} fz={14} lh={'20px'}>
        {label}
      </Text>
    </Flex>
  );
};

export default StatusTag;
