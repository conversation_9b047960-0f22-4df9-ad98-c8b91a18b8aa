import IconArrowNext from '@icons/icon-arrow-next.svg';
import IconArrowPrevious from '@icons/icon-arrow-previous.svg';
import type { MantineTheme } from '@mantine/core';
import { Flex, Pagination, Text } from '@mantine/core';
import { useSearch } from 'hooks';
import { useMemo } from 'react';

interface IPaginationBar {
  pages: number;
  limit?: number;
  total: number;
}

const PaginationBar = ({ pages, total, limit = 10 }: IPaginationBar) => {
  const { query, setQuery } = useSearch();
  const activePage = useMemo(() => Number(query?.page) || 1, [query?.page]);
  const handleOnChange = (page: number) => {
    setQuery({ page: page.toString() });
  };

  const pageRecord = {
    min: (activePage - 1) * limit + 1,
    max: activePage * limit < total ? activePage * limit : total,
  };

  if (total <= limit) {
    return null;
  }
  return (
    <Flex
      align={'center'}
      direction={{ base: 'column', sm: 'row' }}
      gap={'loose'}
      justify={'end'}
    >
      <Text color={'gray-700'} fs={'sm'} sx={{ display: 'inline-block' }}>
        {`${total}件中 ${pageRecord.min}-${pageRecord.max}件表示`}
      </Text>

      <Pagination
        maw={'100%'}
        nextIcon={IconArrowNext}
        onChange={handleOnChange}
        previousIcon={IconArrowPrevious}
        radius={'sm'}
        spacing={4}
        sx={(theme: MantineTheme) => ({
          flexWrap: 'nowrap',
          'button.mantine-Pagination-control': {
            backgroundColor: 'white',
            borderColor: 'white',
            textAlign: 'center',
            color: theme.colors['gray-original'],
          },
          'button.mantine-Pagination-control:hover': {
            backgroundColor: 'white',
          },
          'button[data-active=true]': {
            borderColor: theme.colors.primary,
            color: theme.colors.primary,
            fontWeight: 700,
          },
          '.mantine-Pagination-control:not([disabled])': {
            '&:first-of-type, &:last-child': {
              color: theme.colors.primary,
            },
          },
        })}
        total={pages}
        value={activePage}
      />
    </Flex>
  );
};

export default PaginationBar;
