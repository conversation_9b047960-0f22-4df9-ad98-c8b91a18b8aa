import { yupResolver } from '@hookform/resolvers/yup';
import EmailExpiredIcon from '@icons/icon-link-expired.svg';
import PasswordIcon from '@icons/icon-password.svg';
import SuccessIcon from '@icons/icon-success.svg';
import { Button, Card, Flex, Title } from '@mantine/core';
import { PasswordField } from 'components/Form';
import InfoPopup from 'components/InfoPopup';
import { useFetch, useMutate } from 'hooks';
import authQuery from 'models/auth';
import type {
  IForgotPasswordTokenVerifyData,
  ISetPasswordRequest,
} from 'models/auth/type';
import { useRouter } from 'next/router';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';

import type { SetPasswordFormValues } from './schema';
import schema from './schema';

const ForgetPasswordForm = () => {
  const { query } = useRouter();
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<SetPasswordFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  const token = query?.token as string;

  const {
    mutateAsync: onSetPassword,
    isSuccess,
    isLoading,
    isError,
  } = useMutate<ISetPasswordRequest>(authQuery.setNewPassword);

  const {
    isError: verifyError,
    isLoading: isVerifying,
    data: verifyData,
  } = useFetch<IForgotPasswordTokenVerifyData>({
    ...authQuery.verifyTokenForgetPassword,
    customParams: { token },
    queryKey: ['verifyEmail', 'forgetPassword'],
    enabled: !!token,
    meta: {
      noToastError: true,
    },
  });

  const onSubmit: SubmitHandler<SetPasswordFormValues> = ({ password }) => {
    onSetPassword({
      password,
      token,
    });
  };

  const renderContent = () => {
    if (!token || !verifyData || verifyError || isError) {
      return (
        <InfoPopup
          description={'招待の期限が過ぎています。'}
          href="/"
          icon={<EmailExpiredIcon />}
          isLink
          submitButtonLabel="ホームページに戻る"
          title="認証エラー"
        />
      );
    }

    if (isSuccess) {
      return (
        <InfoPopup
          description={'再度ログインしてください'}
          href="/login"
          icon={<SuccessIcon />}
          isLink
          submitButtonLabel="ログイン"
          title={'パスワードを変更しました'}
        />
      );
    }

    return <></>;
  };

  if (isVerifying) {
    return null;
  }

  if (!token || !verifyData || verifyError || isError || isSuccess) {
    return (
      <Card
        maw={464}
        px={{ base: 15, sm: 31 }}
        py={{ base: 16, sm: 32 }}
        shadow="sm"
        w={'100%'}
      >
        {renderContent()}
      </Card>
    );
  }

  return (
    <Card
      maw={464}
      px={{ base: 15, sm: 31 }}
      py={{ base: 32, sm: 40 }}
      shadow="sm"
      w={'100%'}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Flex align={'center'} direction={'column'} gap={32} justify={'center'}>
          <Title color="black" fz={{ base: 24, sm: 32 }} order={3}>
            パスワードを設定する
          </Title>
          <Flex direction={'column'} gap={16} sx={{ alignSelf: 'stretch' }}>
            <PasswordField
              control={control}
              icon={<PasswordIcon />}
              label="パスワード"
              name="password"
              placeholder="パスワードを入力してください"
              sx={(theme) => ({
                [theme.fn.smallerThan('sm')]: {
                  'input::placeholder': {
                    fontSize: 14,
                  },
                },
              })}
            />
            <PasswordField
              control={control}
              icon={<PasswordIcon />}
              label="パスワード（確認）"
              name="confirmPassword"
              placeholder="もう一度パスワードを入力してください"
              sx={(theme) => ({
                [theme.fn.smallerThan('sm')]: {
                  'input::placeholder': {
                    fontSize: 14,
                    whiteSpace: 'normal',
                    transform: 'translateY(-12px)',
                  },
                },
              })}
            />
          </Flex>
          <Button
            disabled={!isValid}
            fullWidth
            loading={isLoading}
            type="submit"
          >
            設定する
          </Button>
        </Flex>
      </form>
    </Card>
  );
};

export default ForgetPasswordForm;
