import { REG_EXP } from 'utils/constants';
import type { InferType } from 'yup';
import { object, ref, string } from 'yup';

const schema = object({
  password: string()
    .required()
    .matches(REG_EXP.NON_WHITESPACE, '空白文字は使用できません。')
    .matches(
      REG_EXP.PASSWORD,
      '英文字と数字を含めて８文字以上のパスワードを設定してください。',
    ),
  confirmPassword: string()
    .required()
    .oneOf([ref('password')], 'パスワードが一致しません。')
    .matches(
      REG_EXP.PASSWORD,
      '英文字と数字を含めて８文字以上のパスワードを設定してください。',
    ),
});

export default schema;
export type SetPasswordFormValues = InferType<typeof schema>;
