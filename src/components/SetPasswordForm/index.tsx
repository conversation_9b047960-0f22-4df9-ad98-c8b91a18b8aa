import { yupResolver } from '@hookform/resolvers/yup';
import EmailExpiredIcon from '@icons/icon-link-expired.svg';
import MailIcon from '@icons/icon-mail.svg';
import PasswordIcon from '@icons/icon-password.svg';
import SuccessIcon from '@icons/icon-success.svg';
import { Button, Card, Flex, Text, Title } from '@mantine/core';
import { PasswordField } from 'components/Form';
import InfoPopup from 'components/InfoPopup';
import InlineToastMessage from 'components/InlineToastMessage';
import type { RegisterFormValues } from 'components/RegisterForm/schema';
import { useFetch, useMutate } from 'hooks';
import authQuery from 'models/auth';
import type { ISetPasswordRequest, IWorker } from 'models/auth/type';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import helpers from 'utils/helpers';

import type { SetPasswordFormValues } from './schema';
import schema from './schema';

const SetPasswordForm = () => {
  const { query, push } = useRouter();
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<SetPasswordFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  const token = query?.token as string;
  const email = (query?.email as string)?.replace(/\s/g, '+');

  const {
    mutateAsync: onSetPassword,
    isSuccess,
    isLoading,
    isError,
  } = useMutate<ISetPasswordRequest>({
    ...authQuery.setPassword,
    onSuccess: () => {
      helpers.logEventTracking('set_signup_password');
    },
  });

  const {
    mutateAsync: onWorkerRegister,
    isSuccess: isMessageSent,
    isLoading: isSending,
  } = useMutate<RegisterFormValues>(authQuery.register);

  const {
    data: verifyData,
    isError: verifyError,
    isLoading: isVerifying,
    isSuccess: verifySuccess,
  } = useFetch<IWorker>({
    ...authQuery.verifyEmailToken,
    customParams: { token },
    queryKey: ['verifyEmail'],
    enabled: !!token,
    meta: {
      noToastError: true,
    },
  });

  useEffect(() => {
    if (!isVerifying && verifySuccess) {
      helpers.setEventTrackingUserId(verifyData?._id);
      helpers.logEventTracking('verify_signup_email');
    }
  }, [isVerifying, verifyData?._id, verifySuccess]);

  const onResend = () => {
    onWorkerRegister({ email });
  };

  const onSubmit: SubmitHandler<SetPasswordFormValues> = ({ password }) => {
    onSetPassword(
      {
        password,
        token,
      },
      {
        onSuccess: () => {
          helpers.logEventTracking('sign_up');
        },
      },
    );
  };

  const goToLoginPage = () => {
    helpers.setEventTrackingUserId();
    push('/login');
  };

  const renderContent = () => {
    if (isMessageSent) {
      return (
        <InfoPopup
          description={
            <Text
              align="center"
              fw={400}
              fz={16}
              lh="24px"
              mb={{ base: 24, sm: 32 }}
            >
              アカウント認証するためのメールを以下のメールアドレスに送信いたしました。メールの内容に従って、パスワードを設定してください。
              {'\n'}
              <Text fw={700} inherit span>
                {email}
              </Text>
            </Text>
          }
          href="/"
          icon={<MailIcon />}
          isLink
          submitButtonLabel="ホームページに戻る"
          title="アカウント認証メール送信完了"
        />
      );
    }

    if (!token || !email || verifyError || isError) {
      return (
        <InfoPopup
          description={
            '認証URLの期限が過ぎています。\n再度アカウント登録してください'
          }
          icon={<EmailExpiredIcon />}
          onSubmit={onResend}
          submitButtonLabel="再度送信する"
          submitButtonLoading={isSending}
          title="認証エラー"
        />
      );
    }

    if (isSuccess) {
      return (
        <InfoPopup
          description={
            'アカウントの設定はまだ完了していません。\nアカウントにログインし、必要な項目を 入力してください'
          }
          icon={<SuccessIcon />}
          onSubmit={goToLoginPage}
          submitButtonLabel="ログイン"
          title={'パスワード設定完了'}
        />
      );
    }

    return <></>;
  };

  if (isVerifying) {
    return null;
  }

  if (
    isMessageSent ||
    !token ||
    !email ||
    verifyError ||
    isError ||
    isSuccess
  ) {
    return (
      <Card
        maw={424}
        px={{ base: 15, sm: 31 }}
        py={{ base: 16, sm: 32 }}
        shadow="sm"
        w={'100%'}
      >
        {renderContent()}
      </Card>
    );
  }

  return (
    <Card
      maw={424}
      px={{ base: 15, sm: 31 }}
      py={{ base: 32, sm: 40 }}
      shadow="sm"
      w={'100%'}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Flex align={'center'} direction={'column'} gap={32} justify={'center'}>
          <Title color="black" fz={{ base: 24, sm: 32 }} order={3}>
            パスワードを設定する
          </Title>
          <InlineToastMessage
            message={
              '英文字と数字を含めて８文字以上のパスワードを設定してください。'
            }
            type="status"
            visible
          />
          <Flex direction={'column'} gap={16} sx={{ alignSelf: 'stretch' }}>
            <PasswordField
              control={control}
              icon={<PasswordIcon />}
              label="パスワード"
              name="password"
              placeholder="パスワード"
            />
            <PasswordField
              control={control}
              icon={<PasswordIcon />}
              name="confirmPassword"
              placeholder="パスワード (確認用)"
            />
          </Flex>
          <Button
            disabled={!isValid}
            fullWidth
            loading={isLoading}
            type="submit"
          >
            設定する
          </Button>
        </Flex>
      </form>
    </Card>
  );
};

export default SetPasswordForm;
