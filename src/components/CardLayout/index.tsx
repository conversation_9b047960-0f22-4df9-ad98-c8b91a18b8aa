import type {
  BoxProps,
  ButtonProps,
  CardProps,
  FlexProps,
  TextProps,
} from '@mantine/core';
import { Box, Button, Card, Flex, LoadingOverlay, Text } from '@mantine/core';
import React from 'react';

interface FormLayoutProps {
  children: React.ReactElement;
  headerProps?: BoxProps;
  bodyProps?: BoxProps;
  footerProps?: FlexProps;
  containerProps?: Omit<CardProps, 'children'>;
  HeaderComponent?: React.ReactNode;
  FooterComponent?: React.ReactNode;
  title?: string;
  cancelButtonLabel?: string;
  submitButtonLabel?: string;
  titleProps?: TextProps;
  cancelButtonProps?: ButtonProps & React.HTMLAttributes<HTMLButtonElement>;
  submitButtonProps?: ButtonProps & React.HTMLAttributes<HTMLButtonElement>;
  isHeaderShown?: boolean;
  isFooterShown?: boolean;
  isLoading?: boolean;
}

const FormLayout = ({
  children,
  title,
  titleProps,
  headerProps,
  footerProps,
  bodyProps,
  containerProps,
  cancelButtonLabel,
  cancelButtonProps,
  submitButtonLabel,
  submitButtonProps,
  HeaderComponent,
  FooterComponent,
  isHeaderShown = true,
  isFooterShown = true,
  isLoading = false,
}: FormLayoutProps) => {
  const renderHeader = () => {
    if (!isHeaderShown) {
      return null;
    }

    if (HeaderComponent) {
      return HeaderComponent;
    }

    return (
      <Box
        pb={{ base: 20, sm: 24 }}
        pt={16}
        px={{ base: 16, sm: 24 }}
        sx={(theme) => ({
          borderBottom: `1px solid ${theme.colors['gray-300']}`,
        })}
        {...headerProps}
      >
        <Text fw={700} fz={18} lh="26px" {...titleProps}>
          {title}
        </Text>
      </Box>
    );
  };

  const renderFooter = () => {
    if (!isFooterShown) {
      return null;
    }

    if (FooterComponent) {
      return FooterComponent;
    }

    return (
      <Flex
        align={'center'}
        direction={'row'}
        gap={{ base: 8, sm: 16 }}
        justify={'flex-end'}
        px={{ base: 16, sm: 24 }}
        py={16}
        sx={(theme) => ({
          borderTop: `1px solid ${theme.colors['gray-300']}`,
          [theme.fn.smallerThan('sm')]: {
            '& > *': {
              flex: 1,
            },
          },
        })}
        {...footerProps}
      >
        <Button miw={100} variant="outline" {...cancelButtonProps}>
          {cancelButtonLabel || 'キャンセル'}
        </Button>
        <Button miw={100} {...submitButtonProps}>
          {submitButtonLabel || '保存する'}
        </Button>
      </Flex>
    );
  };

  return (
    <Card
      maw={784}
      mx="auto"
      sx={(theme) => ({
        [theme.fn.smallerThan('sm')]: {
          borderRadius: 0,
        },
      })}
      w="100%"
      {...containerProps}
    >
      {renderHeader()}
      <Box
        pos="relative"
        px={{ base: 16, sm: 24 }}
        py={{ base: 24, sm: 32 }}
        {...bodyProps}
      >
        {children}
        <LoadingOverlay loaderProps={{ size: 'lg' }} visible={isLoading} />
      </Box>
      {renderFooter()}
    </Card>
  );
};

export default FormLayout;
