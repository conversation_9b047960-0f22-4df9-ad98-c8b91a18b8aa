import { Flex, Footer, Text } from '@mantine/core';
import Link from 'next/link';
import React from 'react';
import { PUBLIC_NAVIGATION_MENU, SOCIAL_CONTACTS } from 'utils/constants';

import useStyles from './styles';

const LayoutFooter = () => {
  const { classes } = useStyles();

  return (
    <Footer
      className={classes.footerWrapper}
      fixed={false}
      height={161}
      withBorder={false}
    >
      <Flex
        align="center"
        className={classes.navigationLink}
        gap={40}
        justify="center"
        wrap="wrap"
      >
        {PUBLIC_NAVIGATION_MENU.map((nav) => (
          <Text
            className="hover-underline"
            color="white"
            component={Link}
            href={nav.href}
            key={nav.href}
            lh="24px"
            size={16}
            weight="bold"
          >
            {nav.label}
          </Text>
        ))}
      </Flex>
      <Flex className={classes.socialLink} gap={24}>
        {SOCIAL_CONTACTS.map((nav) => (
          <Link href={nav.href} key={nav.href} rel="noreferrer" target="_blank">
            {nav.icon}
          </Link>
        ))}
      </Flex>
      <Text color="white" lh="24px" size={16}>
        © <b>Teqnowa</b> all rights reserved.
      </Text>
    </Footer>
  );
};

export default LayoutFooter;
