import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  footerWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px 32px',
    backgroundColor: '#BD3518',
    marginTop: '-1px',
    [theme.fn.smallerThan('sm')]: {
      height: 'unset',
      maxHeight: 'unset',
      minHeight: '173px',
      px: 16,
      margin: 0,
    },
  },
  navigationLink: {
    marginBottom: 16,
    [theme.fn.smallerThan('sm')]: {
      gap: '8px 24px',
      maxWidth: 235,
      a: {
        fontSize: 14,
        lineHeight: '20px',
      },
    },
  },
  socialLink: {
    marginBottom: 16,
    'a, img': {
      width: 40,
      height: 40,
    },
    [theme.fn.smallerThan('sm')]: {
      marginBottom: 12,
    },
  },
}));

export default useStyles;
