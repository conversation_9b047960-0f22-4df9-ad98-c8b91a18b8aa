import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    padding: '72px 20px',
    [theme.fn.smallerThan('sm')]: {
      padding: '48px 20px',
    },
  },
  featureContentWrapper: {
    maxWidth: 1184,
    width: '100%',
    margin: '0 auto',
  },
  featureItem: {
    textAlign: 'center',
    padding: '24px 0',
    [theme.fn.smallerThan('sm')]: {
      padding: '16px 0',
    },
  },
  featureImage: {
    width: 200,
    height: 200,
    position: 'relative',
    [theme.fn.smallerThan('sm')]: {
      width: 160,
      height: 160,
    },
  },
}));

export default useStyles;
