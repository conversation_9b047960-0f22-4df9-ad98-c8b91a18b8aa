import { Box, Flex, SimpleGrid, Text, Title } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import useStyles from './styles';

const CONTENTS = [
  {
    image: '/recruiter/feature-1.png',
    title: '稼働日数 累計約1,500日',
    description: 'テクノワを通して、約1,500日分の\n施工実働が実現',
  },
  {
    image: '/recruiter/feature-2.png',
    title: '複数案件の応募獲得',
    description:
      'S社：8案件でテクノワを利用。\n全件で職人の応募満了\n(大阪府従業員数 約100名)',
  },
  {
    image: '/recruiter/feature-3.png',
    title: '掲載初日で職人調達を完了',
    description: '多くの案件が初日に調達を完了',
  },
  {
    image: '/recruiter/feature-4.png',
    title: '全国の職人ネットワーク',
    description: '東北、関東、関西、九州での調達実績',
  },
  {
    image: '/recruiter/feature-5.png',
    title: '長期案件にも対応',
    description: 'J社の案件\n23年8月〜24年1月\n333日の実働実績\n（職種：配管）',
  },
  {
    image: '/recruiter/feature-6.png',
    title: '様々な職種でマッチングを実現',
    description: '対応職種実績\n配管、溶接、鍛治、塗装、重量',
  },
];

const Feature = () => {
  const { classes } = useStyles();
  return (
    <Box
      className={classes.container}
      component="section"
      id="features"
      title="Features"
    >
      <Title
        color="black"
        fz={{ base: 24, sm: 36 }}
        lh={{ base: '34px', sm: '52px' }}
        mb={{ base: 8, sm: 16 }}
        order={3}
        ta="center"
      >
        実績
      </Title>
      <SimpleGrid
        breakpoints={[
          { maxWidth: 'md', cols: 2 },
          { maxWidth: 'sm', cols: 1 },
        ]}
        className={classes.featureContentWrapper}
        cols={3}
        spacing={8}
      >
        {CONTENTS.map((content) => {
          return (
            <Flex
              align="center"
              className={classes.featureItem}
              direction="column"
              key={content.title}
            >
              <Box className={classes.featureImage}>
                <Image alt="" fill src={content.image} />
              </Box>
              <Text
                color="primary"
                fw={700}
                fz={{ base: 18, sm: 24 }}
                lh={{ base: '26px', sm: '34px' }}
                mb={{ base: 8, sm: 16 }}
              >
                {content.title}
              </Text>
              <Text
                color="black"
                fw={400}
                fz={{ base: 14, sm: 16 }}
                lh={{ base: '20px', sm: '24px' }}
              >
                {content.description}
              </Text>
            </Flex>
          );
        })}
      </SimpleGrid>
    </Box>
  );
};

export default Feature;
