import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  drawerOverlay: {
    top: 64,
  },
  drawerInner: {
    top: 64,
  },
  drawerBody: {
    padding: '24px 16px',
  },
  navLinkRoot: {
    display: 'flex',
    alignItems: 'center',
    height: 52,
    textDecoration: 'none',
    padding: '0 16px',
    gap: 12,
    borderRadius: '10px',
    borderWidth: 0,
    backgroundColor: 'transparent',
    cursor: 'pointer',
    width: '100%',
    '&.logout-btn .mantine-NavLink-label': {
      color: theme.colors.primary,
    },
    '&[data-active=true]': {
      backgroundColor: theme.colors['neutral-50'],
      '.mantine-NavLink-label': {
        color: theme.colors.primary,
        fontWeight: 'bold',
      },
      '.mantine-NavLink-icon': {
        color: theme.colors.primary,
      },
    },
  },
  navLinkLabel: {
    color: theme.colors['gray-700'],
  },
}));

export default useStyles;
