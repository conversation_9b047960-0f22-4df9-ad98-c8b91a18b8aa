import { Drawer as MantineDrawer, NavLink } from '@mantine/core';
import { useGlobalState } from 'hooks';
import React from 'react';

import useStyles from './styles';

const Drawer = () => {
  const { classes } = useStyles();

  const { openedDrawer, setOpenDrawer } = useGlobalState();

  const navLinkClassNames = {
    root: classes.navLinkRoot,
    label: classes.navLinkLabel,
  };

  return (
    <MantineDrawer
      classNames={{
        overlay: classes.drawerOverlay,
        inner: classes.drawerInner,
        body: classes.drawerBody,
      }}
      onClose={() => setOpenDrawer(false)}
      opened={openedDrawer}
      position="right"
      size={375}
      withCloseButton={false}
      withinPortal={false}
    >
      <NavLink
        classNames={navLinkClassNames}
        component="a"
        href="#usage-flow"
        label="サービス利用の流れ"
        onClick={() => {
          setOpenDrawer(false);
        }}
        unstyled
      />
      <NavLink
        classNames={navLinkClassNames}
        component="a"
        href="#features"
        label="実績"
        onClick={() => {
          setOpenDrawer(false);
        }}
        unstyled
      />
      <NavLink
        classNames={navLinkClassNames}
        component="a"
        href="#faq"
        label="よくあるご質問"
        onClick={() => {
          setOpenDrawer(false);
        }}
        unstyled
      />
    </MantineDrawer>
  );
};

export default Drawer;
