import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    padding: '72px 20px 80px',
    [theme.fn.smallerThan('sm')]: {
      padding: '48px 20px',
    },
  },
  problemIllustration: {
    position: 'relative',
    aspectRatio: '1184/496',
    maxWidth: 1184,
    width: '100%',
    margin: '0 auto',
    '.arrow-down': {
      display: 'flex',
      position: 'absolute',
      left: '50%',
      bottom: -64,
      transform: 'translate(-50%, 0)',
      [theme.fn.smallerThan('sm')]: {
        bottom: -36,
        svg: {
          width: 48,
          height: 44,
        },
      },
    },
    [theme.fn.largerThan('sm')]: {
      '.illustration-sp': {
        display: 'none',
      },
    },
    [theme.fn.smallerThan('sm')]: {
      aspectRatio: '375/600',
      maxWidth: 375,
      '.illustration-pc': {
        display: 'none',
      },
    },
  },
  solutionContent: {
    padding: '64px 0',
    margin: '40px auto 0',
    background:
      'url(./recruiter/solution-bg.png) 0% 0% / 1184px 1268px repeat #F8FAFC',
    maxWidth: 1184,
    width: '100%',
    borderRadius: '20px',

    [theme.fn.smallerThan('sm')]: {
      margin: '24px auto 0',
      padding: '40px 0',
    },
  },
}));

export default useStyles;
