import { Box, Flex, Text, Title } from '@mantine/core';
import DoubleChervonDown from '@recruiter/double-chervon-down.svg';
import Image from 'next/image';
import React from 'react';

import useStyles from './styles';

const CONTENTS = [
  {
    image: '/recruiter/solution-1.png',
    title: '職人が見つかる',
    description: '大半の案件が応募満了',
    offset: '18%',
  },
  {
    image: '/recruiter/solution-2.png',
    title: 'すぐに集まる',
    description: '即日で応募満了のケースも',
    offset: '19%',
  },
  {
    image: '/recruiter/solution-3.png',
    title: '手間なく調達できる',
    description: '登録からマッチングまでを\n全面サポート',
    offset: '18%',
  },
  {
    image: '/recruiter/solution-4.png',
    title: '新しく職人を探せる',
    description: '全国の職人ネットワークに\nアクセス可能',
    offset: '16%',
  },
  {
    image: '/recruiter/solution-5.png',
    title: '明瞭で安価な料金体系',
    description: '人/日/単価が明瞭',
    offset: '18%',
  },
];

const ProblemSolution = () => {
  const { classes } = useStyles();
  return (
    <Box className={classes.container}>
      <Title
        color="black"
        fz={{ base: 24, sm: 36 }}
        lh={{ base: '34px', sm: '52px' }}
        mb={{ base: 24, sm: 40 }}
        order={3}
        ta="center"
      >
        こんな課題が
        <Box component="br" display={{ base: 'block', sm: 'none' }} />
        ありませんか？
      </Title>
      <Box className={classes.problemIllustration}>
        <Image
          alt=""
          className="illustration-pc"
          draggable={false}
          fill
          quality={100}
          sizes="(max-width: 48em) 1em, ((min-width: 48em) and (max-width: 75em)) 75em, 88em"
          src="/recruiter/delulu.png"
        />
        <Image
          alt=""
          className="illustration-sp"
          draggable={false}
          fill
          quality={100}
          sizes="(max-width: 48em) 48em, 1em"
          src="/recruiter/delulu-sp.png"
        />
        <Box className="arrow-down">
          <DoubleChervonDown />
        </Box>
      </Box>
      <Box className={classes.solutionContent}>
        <Title
          fz={{ base: 24, sm: 36 }}
          lh={{ base: '34px', sm: '52px' }}
          mb={{ base: 32, sm: 40 }}
          order={3}
          ta="center"
        >
          プラント案件の職人募集に
          <Box component="br" display={{ base: 'block', sm: 'none' }} />
          関わるお悩みを <br />
          テクノワが解決します!
        </Title>
        <Flex direction="column" gap={{ base: 32, sm: 24 }}>
          {CONTENTS.map((content, index) => {
            const offsetMargin = { base: 0, sm: content.offset };
            return (
              <Flex
                align="center"
                direction={{ base: 'column', sm: 'row' }}
                gap={{ base: 8, sm: 12 }}
                justify={index % 2 === 0 ? 'flex-start' : 'flex-end'}
                key={content.title}
                ml={index % 2 === 0 ? offsetMargin : 0}
                mr={index % 2 !== 0 ? offsetMargin : 0}
                ta={{ base: 'center', sm: 'left' }}
              >
                <Box
                  h={{ base: 144, sm: 180 }}
                  pos="relative"
                  w={{ base: 198, sm: 248 }}
                >
                  <Image
                    alt={content.title}
                    draggable={false}
                    fill
                    sizes="(max-width: 48em) 12.5em, 16em"
                    src={content.image}
                  />
                </Box>
                <Box>
                  <Text
                    color="primary"
                    fw={700}
                    fz={{ base: 18, sm: 24 }}
                    lh={{ base: '26px', sm: '34px' }}
                    mb={{ base: 8, sm: 12 }}
                  >
                    {content.title}
                  </Text>
                  <Text
                    color="black"
                    fw={400}
                    fz={{ base: 14, sm: 16 }}
                    lh={{ base: '20px', sm: '24px' }}
                  >
                    {content.description}
                  </Text>
                </Box>
              </Flex>
            );
          })}
        </Flex>
      </Box>
    </Box>
  );
};

export default ProblemSolution;
