import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    alignItems: 'stretch',
    backgroundColor: theme.colors['neutral-600'],
    [theme.fn.smallerThan('sm')]: {
      flexDirection: 'column',
    },
  },
  bannerLeftWrap: {
    flex: '0 0 49.4%',
    position: 'relative',
    padding: 32,
    [theme.fn.smallerThan('sm')]: {
      flex: '1 1 auto',
      padding: '64px 20px 76px',
    },
  },
  bannerImage: {
    flex: '1 1 auto',
    aspectRatio: '728/828',
    width: '100%',
    height: '100%',
    position: 'relative',
    [theme.fn.smallerThan('sm')]: {
      aspectRatio: '375/426',
    },
  },
  pattern: {
    position: 'absolute',
    '&[data-pattern="top-left"]': {
      left: 0,
      top: 0,
      width: 200,
      height: 196,
      [theme.fn.smallerThan('sm')]: {
        width: 100,
        height: 98,
      },
    },
    '&[data-pattern="bottom-right"]': {
      right: 0,
      bottom: 0,
      width: 208,
      height: 188,
      [theme.fn.smallerThan('sm')]: {
        width: 104,
        height: 94,
      },
    },
  },
}));

export default useStyles;
