import { Box, Button, Flex, Text, Title } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import useStyles from './styles';

const HeroBanner = () => {
  const { classes } = useStyles();
  return (
    <Flex className={classes.container}>
      <Flex
        align="center"
        className={classes.bannerLeftWrap}
        direction="column"
        justify="center"
      >
        <Box className={classes.pattern} data-pattern="top-left">
          <Image
            alt=""
            draggable={false}
            fill
            sizes="(max-width: 48em) 6.25em, 12.5em"
            src="/recruiter/banner-pattern-left.png"
          />
        </Box>
        <Box className={classes.pattern} data-pattern="bottom-right">
          <Image
            alt=""
            draggable={false}
            fill
            sizes="(max-width: 48em) 6.5em, 13em"
            src="/recruiter/banner-pattern-right.png"
          />
        </Box>
        <Box>
          <Title
            color="white"
            fz={{ base: 28, md: 38 }}
            lh={{ base: '42px', md: '53.2px' }}
            mb={{ base: 16, md: 28 }}
            style={{
              textShadow: '0px 4px 8px rgba(0, 0, 0, 0.20)',
            }}
            ta={{ base: 'center', sm: 'left' }}
          >
            プラント工事の
            <br />
            職人・一人親方・協力会社を
            <br />
            見つけられる
          </Title>
          <Text
            color="white"
            fz={{ base: 16, md: 20 }}
            lh={{ base: '24px', md: '30px' }}
            mb={{ base: 32, md: 56 }}
            style={{
              textShadow: '0px 4px 8px rgba(0, 0, 0, 0.20)',
            }}
            ta={{ base: 'center', sm: 'left' }}
          >
            プラント案件に特化にした受発注マッチングサービス
          </Text>
          <a href="#contact-form">
            <Button
              bg="white"
              className="hero"
              fullWidth
              fz={16}
              maw={384}
              size="xl"
              variant="subtle"
            >
              お問い合わせはこちら
            </Button>
          </a>
        </Box>
      </Flex>
      <Box className={classes.bannerImage}>
        <Image
          alt="Banner"
          fill
          priority
          quality={100}
          sizes="(max-width: 48em) 100vw, 50vw"
          src="/recruiter/banner.png"
        />
      </Box>
    </Flex>
  );
};

export default HeroBanner;
