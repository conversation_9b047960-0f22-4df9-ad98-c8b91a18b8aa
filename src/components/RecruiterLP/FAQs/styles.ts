import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: theme.colors['gray-200'],
    position: 'relative',
  },
  topSpace: {
    height: 48,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    backgroundColor: theme.white,
    [theme.fn.largerThan('sm')]: {
      height: 64,
    },
  },
  wrapper: {
    padding: '0 20px 40px',
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
    backgroundColor: theme.white,
    textAlign: 'center',
    [theme.fn.largerThan('sm')]: {
      padding: '0 128px 80px',
    },
    [theme.fn.largerThan('md')]: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      textAlign: 'left',
    },
  },
  title: {
    [theme.fn.largerThan('md')]: {
      flexBasis: 216,
      flexGrow: 1,
      flexShrink: 0,
    },
  },
  faqs: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
    [theme.fn.largerThan('md')]: {
      flexBasis: 784,
      flexGrow: 0,
      flexShrink: 1,
    },
  },
  faqItem: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: '10px',
    display: 'block',
    border: `1px solid ${theme.colors['gray-300']}`,
    textAlign: 'start',
    padding: '20px 16px',
    cursor: 'pointer',
    position: 'relative',
    zIndex: 1,
    boxShadow: theme.shadows.md,
    overflow: 'hidden',
    [theme.fn.largerThan('sm')]: {
      padding: '28px 24px',
    },
    '.label': {
      display: 'flex',
      alignItems: 'flex-start',
      gap: 4,
      fontSize: 16,
      lineHeight: 1.5,
      fontWeight: 500,
      color: theme.colors.black,
      paddingRight: 28,
      [theme.fn.largerThan('sm')]: {
        gap: 8,
        fontSize: 18,
        lineHeight: '26px',
        alignItems: 'center',
        paddingRight: 40,
      },
      svg: {
        flexShrink: 0,
        flexGrow: 0,
        width: 24,
        height: 24,
        marginBlock: 3,
        [theme.fn.largerThan('sm')]: {
          width: 32,
          height: 32,
        },
      },
    },
    '.content': {
      color: theme.colors['gray-700'],
      fontSize: 14,
      fontWeight: 400,
      lineHeight: '20px',
      marginTop: 8,
      paddingInline: 28,
      [theme.fn.largerThan('sm')]: {
        paddingInline: 40,
        fontSize: 16,
        lineHeight: 1.5,
      },
    },
    '.indicator-arrow': {
      position: 'absolute',
      top: 20,
      right: 16,
      transition: 'all 0.2s ease-in',
      transformOrigin: 'center',
      width: 24,
      height: 24,
      color: theme.colors['gray-600'],
      svg: {
        width: 24,
        height: 24,
      },
      [theme.fn.largerThan('sm')]: {
        top: 32,
        right: 32,
      },
    },
    '&::after': {
      borderRadius: '10px',
      zIndex: -1,
    },
  },
  maskedText: {
    position: 'absolute',
    left: 0,
    bottom: -24,
    width: 239,
    height: 102,
    objectFit: 'contain',
    [theme.fn.largerThan('sm')]: {
      bottom: -58,
      height: 340,
      width: 796,
    },
  },
}));

export default useStyles;
