import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconQuestion from '@icons/icon-question.svg';
import type { BoxProps } from '@mantine/core';
import { Box, Collapse, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import React from 'react';

const CollapseItem = ({
  label,
  content,
  ...props
}: { label: string; content: string | React.ReactElement } & BoxProps) => {
  const [opened, { toggle }] = useDisclosure(false);

  const Content = () => {
    if (typeof content === 'string') {
      return <Text className="content">{content}</Text>;
    }

    return React.cloneElement(content, { className: 'content' });
  };

  return (
    <Box {...props} onClick={toggle} role="toggle">
      <Text className="label">
        <IconQuestion height={24} width={24} />
        {label}
      </Text>
      <Box
        className="indicator-arrow"
        sx={opened ? { transform: 'rotate(-180deg)' } : {}}
      >
        <IconArrowDown />
      </Box>
      <Collapse in={opened}>
        <Content />
      </Collapse>
    </Box>
  );
};

export default CollapseItem;
