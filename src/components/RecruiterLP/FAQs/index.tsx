import { Box, Title } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import CollapseItem from './CollapseItem';
import useStyles from './styles';

const faqData: { question: string; answer: string | React.ReactElement }[] = [
  {
    question: 'テクノワにどうやって登録しますか？',
    answer: (
      <Box>
        <a href="#contact-form">お問い合わせ</a>
        にてご連絡ください。
        <br />
        テクノワ事務局から登録のご案内をいたします。
      </Box>
    ),
  },
  {
    question: 'テクノワでどうやって案件を掲載しますか？',
    answer: 'アカウント登録後、案件掲載を行えます。',
  },
  {
    question: '料金体系について',
    answer: `月次請求です。料金体系は以下の通りです。
            ・テクノワ経由で配属した職人数 × 実働日数 × テクノワへの支払い単価
            ※テクノワへの支払い単価はアカウント登録の際にご案内いたします
            ※実働で請求が発生する成果報酬形式です。募集は無料ですのでぜひお試しください`,
  },
  {
    question: '職人・協力会社との契約について',
    answer: '契約は発注業者と協力会社間で直接結んでいただきます。',
  },
  {
    question: '代行サービスは無料ですか？',
    answer: '無料です。',
  },
];

const FAQs = () => {
  const { classes } = useStyles();
  return (
    <Box
      className={classes.container}
      component="section"
      id="faq"
      title="FAQs"
    >
      <Box className={classes.topSpace} />
      <Box className={classes.wrapper}>
        <Title
          className={classes.title}
          color="black"
          fz={{ base: 24, sm: 36 }}
          lh={{ base: '34px', sm: '52px' }}
          mb={{ base: 24, sm: 40 }}
          order={3}
        >
          よくある質問
        </Title>
        <Box className={classes.faqs}>
          {faqData.map((qa, idx) => (
            <CollapseItem
              className={classes.faqItem}
              content={qa.answer}
              key={`faq-${idx}`}
              label={qa.question}
            />
          ))}
        </Box>
      </Box>
      <Box className={classes.maskedText}>
        <Image
          alt=""
          fill
          sizes="(max-width: 48em) 75vw, 50vw"
          src={'/icons/faq-masked.svg'}
        />
      </Box>
    </Box>
  );
};

export default FAQs;
