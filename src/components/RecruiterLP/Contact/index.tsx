import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Title } from '@mantine/core';
import { useMutate } from 'hooks';
import resourceQuery from 'models/resource';
import type { ISendEmail } from 'models/resource/type';
import type { SubmitHandler } from 'react-hook-form';
import { FormProvider, useForm } from 'react-hook-form';
import { CONTACT_OPTIONS } from 'utils/constants';
import helpers from 'utils/helpers';

import ContactForm from './ContactForm';
import type { FormValues } from './ContactForm/schema';
import schema from './ContactForm/schema';
import useStyles from './styles';

const ContactBlock = () => {
  const { classes } = useStyles();
  const methods = useForm<FormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
    defaultValues: {
      selectOption: CONTACT_OPTIONS[0]?.value,
    },
  });
  const { mutateAsync: handleSendEmail } = useMutate<any>({
    ...resourceQuery.sendEmail,
    onSuccess: () => {
      helpers.toast({
        type: 'success',
        message: '提出完了しました。',
        id: 'contact-form-success',
      });
      methods.reset();
    },
  });

  const onSubmit: SubmitHandler<FormValues> = async (values: ISendEmail) => {
    handleSendEmail(values);
  };

  return (
    <Box
      className={classes.container}
      component="section"
      id="contact-form"
      title="Contact form"
    >
      <Box className={classes.contentWrapper}>
        <Title
          color="#111827"
          fw={700}
          fz={{ base: 24, sm: 36 }}
          lh={{ base: '34px', sm: '52px' }}
          mb={24}
          order={3}
          ta="center"
        >
          \ 問い合わせ /
        </Title>
        <Box className={classes.contactForm}>
          <FormProvider {...methods}>
            <ContactForm onSubmitButtonClick={methods.handleSubmit(onSubmit)} />
          </FormProvider>
        </Box>
      </Box>
    </Box>
  );
};

export default ContactBlock;
