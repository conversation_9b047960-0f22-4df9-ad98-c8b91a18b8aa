import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    background: `url(./recruiter/bg-contact.png) center bottom, ${theme.colors.primary}`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: '100% auto',
    borderTopLeftRadius: '40px',
    borderTopRightRadius: '40px',
    padding: '120px 20px 80px 20px',
    [theme.fn.smallerThan('sm')]: {
      paddingTop: 72,
      paddingBottom: 32,
      background: theme.colors.primary,
    },
  },
  contentWrapper: {
    boxShadow: '2px 8px 14px 0px rgba(117, 138, 147, 0.14)',
    background: 'white',
    borderRadius: '10px',
    padding: '64px 80px',
    maxWidth: 784,
    width: '100%',
    margin: '0 auto',
    [theme.fn.smallerThan('sm')]: {
      padding: '40px 16px',
    },
  },
  contactForm: {},
}));

export default useStyles;
