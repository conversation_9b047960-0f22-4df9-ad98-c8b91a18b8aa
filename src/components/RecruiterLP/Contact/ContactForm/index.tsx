import { <PERSON>, Button, Flex } from '@mantine/core';
import { TextareaField, TextField } from 'components/Form';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { CONTACT_OPTIONS } from 'utils/constants';

import CustomRadioGroup from './CustomRadioGroup';
import type { FormValues } from './schema';

interface ContactFormProps {
  onSubmitButtonClick: () => void;
  submitButtonDisabled?: boolean;
}

const ContactForm = ({
  onSubmitButtonClick,
  submitButtonDisabled,
}: ContactFormProps) => {
  const { control } = useFormContext<FormValues>();

  return (
    <Box component="form" w={'100%'}>
      <Flex align={'center'} direction={'column'} gap={16} w="100%">
        <Box w="100%">
          <CustomRadioGroup
            control={control}
            data={CONTACT_OPTIONS}
            label={'お問い合わせについて'}
            name="selectOption"
            required
          />
        </Box>
        <Box w="100%">
          <TextField
            control={control}
            label="名前"
            maxLength={50}
            name="name"
            placeholder="名前を入力してください"
            required
          />
        </Box>
        <Box w="100%">
          <TextField
            control={control}
            label="会社名・事業者名"
            maxLength={50}
            name="companyName"
            placeholder="会社名・事業者名を入力してください"
            required
          />
        </Box>
        <Box w="100%">
          <TextField
            control={control}
            label="メールアドレス"
            name="email"
            placeholder="メールアドレスを入力してください"
            required
          />
        </Box>
        <Box w="100%">
          <TextField
            control={control}
            label="電話番号"
            name="phoneNumber"
            placeholder="電話番号を入力してください "
            required
          />
        </Box>
        <Box w="100%">
          <TextareaField
            control={control}
            label="お問い合わせ内容"
            maxLength={1000}
            minRows={4}
            name="content"
            placeholder="お問い合わせ内容を入力してください "
            required
          />
        </Box>
        <Button
          disabled={submitButtonDisabled}
          fullWidth
          maw={384}
          onClick={onSubmitButtonClick}
        >
          提出する
        </Button>
      </Flex>
    </Box>
  );
};

export default ContactForm;
