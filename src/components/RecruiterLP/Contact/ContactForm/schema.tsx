import { REG_EXP } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  selectOption: string().trim().required(),
  name: string().required().trim(),
  companyName: string().required().trim(),
  email: string()
    .required()
    .trim()
    .lowercase()
    .matches(REG_EXP.EMAIL, '無効な形式です。'),
  phoneNumber: string()
    .trim()
    .matches(REG_EXP.PHONE, {
      message: '正しい電話番号を入力してください。',
      excludeEmptyString: true,
    })
    .required(),
  content: string().required().trim().max(1000),
});

export default schema;
export type FormValues = InferType<typeof schema>;
