import { Box, Radio, useMantineTheme } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { FormLabel } from 'components/Form';
import Description from 'components/Form/Description';
import React, { useId } from 'react';
import {
  type Control,
  type FieldValues,
  type Path,
  useController,
} from 'react-hook-form';

type DataItem = {
  label: string;
  value: string;
  checked?: boolean;
};

interface RadioGroupProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
  data: ReadonlyArray<DataItem>;
}

const RadioGroup = <TFormValues extends FieldValues>({
  data,
  name,
  control,
  label,
  required,
}: RadioGroupProps<TFormValues>) => {
  const id = useId();
  const {
    field: { ref, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const isMobile = useMediaQuery('(max-width: 768px)');
  const theme = useMantineTheme();

  return (
    <Box>
      {label && <FormLabel label={label} required={required} />}
      <Radio.Group
        {...field}
        description={error?.message && <Description error={error?.message} />}
        display={'flex'}
        error={!!error?.message}
        inputWrapperOrder={['label', 'input', 'error', 'description']}
        ref={ref}
        required={required}
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          flexDirection: isMobile ? 'column' : 'row',
          gap: isMobile ? 8 : 0,
          columnGap: isMobile ? 0 : 16,
        }}
      >
        {data.map((item, idx) => (
          <Box
            component="label"
            htmlFor={`${id}-${idx}`}
            key={item.value}
            sx={{
              width: isMobile ? '100%' : 197,
              border: `1px solid ${theme.colors['gray-400']}`,
              borderRadius: 8,
              display: 'flex',
              alignItems: 'center',
              gap: 8,
              cursor: 'pointer',
              transition: 'border-color 0.2s, color 0.2s',
              padding: isMobile ? '19px 16px' : '10px 16px',
              '&:hover': {
                borderColor: theme.colors.primary,
              },
              '&:has([data-checked])': {
                borderColor: '#D23A19',
                color: '#D23A19',
                input: {
                  borderColor: '#D23A19',
                  '&:checked + .___ref-icon': {
                    color: '#D23A19',
                  },
                },
                span: {
                  color: '#D23A19',
                },
              },
            }}
          >
            <Radio id={`${id}-${idx}`} value={item.value} />
            <div
              style={{
                textAlign: 'left',
                flex: 1,
                color: '#111827',
                fontSize: 14,
              }}
            >
              <span>{item.label}</span>
            </div>
          </Box>
        ))}
      </Radio.Group>
    </Box>
  );
};

export default RadioGroup;
