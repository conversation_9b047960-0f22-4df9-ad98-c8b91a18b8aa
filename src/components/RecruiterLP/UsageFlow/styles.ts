import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.black,
    padding: '72px 30px',
    [theme.fn.smallerThan('sm')]: {
      padding: '40px 30px',
    },
  },
  flowContent: {
    position: 'relative',
    aspectRatio: '1184/412',
    maxWidth: 1184,
    width: '100%',
    margin: '0 auto',
    [theme.fn.largerThan('sm')]: {
      '.flow-sp': {
        display: 'none',
      },
    },
    [theme.fn.smallerThan('sm')]: {
      aspectRatio: '315/553',
      maxWidth: 315,
      '.flow-pc': {
        display: 'none',
      },
    },
  },
}));

export default useStyles;
