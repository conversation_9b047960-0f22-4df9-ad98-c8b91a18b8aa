import { Box, Title } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import useStyles from './styles';

const UsageFlow = () => {
  const { classes } = useStyles();
  return (
    <Box
      className={classes.container}
      component="section"
      id="usage-flow"
      title="Usage Flow"
    >
      <Title
        color="white"
        fz={{ base: 24, sm: 36 }}
        lh={{ base: '34px', sm: '52px' }}
        mb={{ base: 32, sm: 40 }}
        order={3}
        ta="center"
      >
        サービス利用の流れ
      </Title>
      <Box className={classes.flowContent}>
        <Image
          alt=""
          className="flow-pc"
          draggable={false}
          fill
          quality={100}
          src="/recruiter/flow.png"
        />
        <Image
          alt=""
          className="flow-sp"
          draggable={false}
          fill
          quality={100}
          src="/recruiter/flow-sp.png"
        />
      </Box>
    </Box>
  );
};

export default UsageFlow;
