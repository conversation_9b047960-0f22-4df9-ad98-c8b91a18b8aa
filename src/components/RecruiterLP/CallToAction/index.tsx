import { Box, Button, Title } from '@mantine/core';
import Image from 'next/image';
import React from 'react';

import useStyles from './styles';

const CallToAction = ({ buttonClassname }: { buttonClassname?: string }) => {
  const { classes } = useStyles();
  return (
    <Box className={classes.container}>
      <Title
        color="white"
        fw={700}
        fz={{ base: 24, sm: 36 }}
        lh={{ base: '34px', sm: '52px' }}
        mb={{ base: 24, sm: 32 }}
        order={3}
        ta="center"
      >
        \まずは無料相談から/
      </Title>
      <a href="#contact-form">
        <Button
          bg="white"
          className={buttonClassname}
          fullWidth
          fz={16}
          maw={384}
          mx="auto"
          size="xl"
          variant="subtle"
        >
          お問い合わせはこちら
        </Button>
      </a>
      <Box className={classes.pattern} data-pattern="bottom-left">
        <Image
          alt=""
          draggable={false}
          fill
          sizes="(max-width: 48em) 5.7em, 11.25em"
          src="/recruiter/cta-pattern-left.png"
        />
      </Box>
      <Box className={classes.pattern} data-pattern="top-right">
        <Image
          alt=""
          draggable={false}
          fill
          sizes="(max-width: 48em) 4.7em, 9.5em"
          src="/recruiter/cta-pattern-right.png"
        />
      </Box>
    </Box>
  );
};

export default CallToAction;
