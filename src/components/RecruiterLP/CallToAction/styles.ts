import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.colors.primary,
    padding: '64px 20px',
    position: 'relative',
    [theme.fn.smallerThan('sm')]: {
      padding: '40px 20px',
    },
  },
  pattern: {
    position: 'absolute',
    '&[data-pattern="bottom-left"]': {
      left: 0,
      bottom: 0,
      width: 179,
      height: 162,
      [theme.fn.smallerThan('sm')]: {
        width: 89.5,
        height: 81,
      },
    },
    '&[data-pattern="top-right"]': {
      right: 0,
      top: 0,
      width: 150,
      height: 149,
      [theme.fn.smallerThan('sm')]: {
        width: 75,
        height: 74.5,
      },
    },
  },
}));

export default useStyles;
