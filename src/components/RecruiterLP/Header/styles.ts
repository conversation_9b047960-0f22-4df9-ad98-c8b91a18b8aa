import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  headerWrapper: {
    top: 0,
    position: 'sticky',
    padding: '0 128px',
    width: '100%',
    height: 72,
    zIndex: 100,
    background: 'white',
    borderBottom: `1px solid ${theme.colors['gray-300']}`,
    [theme.fn.smallerThan('md')]: {
      padding: '0 16px',
    },
    [theme.fn.smallerThan('sm')]: {
      height: 64,
    },
  },
  logo: {
    svg: {
      width: 148,
      height: 40,
    },
    [theme.fn.smallerThan('sm')]: {
      svg: {
        width: 120,
        height: 32,
      },
    },
  },
  navLink: {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    position: 'relative',
    fontSize: 16,
    cursor: 'pointer',
    '&:after': {
      content: '""',
      position: 'absolute',
      width: '100%',
      height: 2,
      bottom: -1,
      left: 0,
      backgroundColor: 'currentColor',
      visibility: 'hidden',
      transform: 'scaleX(0)',
      transition: 'all 0.2s ease-in-out 0s',
      borderRadius: '2px',
    },
    '&[data-active=true]': {
      fontWeight: 'bold',
      '&:after': {
        visibility: 'visible',
        transform: 'scaleX(1)',
      },
    },
    '@media (hover: hover)': {
      '&:hover': {
        fontWeight: 'bold',
        '&:after': {
          visibility: 'visible',
          transform: 'scaleX(1)',
        },
      },
    },
  },
}));

export default useStyles;
