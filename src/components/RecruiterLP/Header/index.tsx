import LogoHorizontal from '@icons/logo-horizontal.svg';
import { Box, Flex, Text } from '@mantine/core';
import HamburgerIcon from 'components/Layout/Header/Hamburger';
import Link from 'next/link';
import React from 'react';

import useStyles from './styles';

const Header = () => {
  const { classes } = useStyles();
  return (
    <Flex
      align="center"
      className={classes.headerWrapper}
      justify="space-between"
    >
      <Link className={classes.logo} href="/">
        <LogoHorizontal />
      </Link>
      <Box display={{ base: 'none', sm: 'flex' }} h="100%" sx={{ gap: 40 }}>
        <Text
          className={classes.navLink}
          color="primary"
          component="a"
          href="#usage-flow"
        >
          サービス利用の流れ
        </Text>
        <Text
          className={classes.navLink}
          color="primary"
          component="a"
          href="#features"
        >
          実績
        </Text>
        <Text
          className={classes.navLink}
          color="primary"
          component="a"
          href="#faq"
        >
          よくあるご質問
        </Text>
      </Box>
      <Box display={{ base: 'inline', sm: 'none' }}>
        <HamburgerIcon />
      </Box>
    </Flex>
  );
};

export default Header;
