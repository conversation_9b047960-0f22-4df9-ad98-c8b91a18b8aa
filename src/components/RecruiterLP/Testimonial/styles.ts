import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.colors['gray-200'],
    padding: '12px 20px 40px',
    display: 'flex',
    flexDirection: 'column',
    [theme.fn.largerThan('sm')]: {
      padding: '20px 20px 72px',
    },
  },
  cards: {
    display: 'grid',
    gridTemplateColumns: 'minmax(0, 384px)',
    gridTemplateRows: 'repeat(3, 1fr)',
    rowGap: 16,
    marginInline: 'auto',
    [theme.fn.largerThan('sm')]: {
      gridTemplateRows: '1fr',
      gridTemplateColumns: 'repeat(3, minmax(0, 384px))',
      rowGap: 0,
      columnGap: 16,
    },
  },
  card: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
    paddingBlock: 24,
    borderRadius: 20,
    backgroundColor: theme.white,
    boxShadow: theme.shadows.md,
    alignItems: 'flex-start',
    [theme.fn.largerThan('sm')]: {
      paddingBlock: 32,
    },
    '.card-header': {
      paddingInline: 16,
      [theme.fn.largerThan('sm')]: {
        paddingInline: 24,
      },
    },
    '& > .icon > svg': {
      width: 32,
      height: 32,
    },
    '.label': {
      marginTop: 10,
      fontSize: 18,
      lineHeight: '26px',
      fontWeight: 700,
      height: 52,
      overflow: 'hidden',
      color: theme.colors.black,
      whiteSpace: 'pre-line',
      [theme.fn.largerThan('sm')]: {
        fontSize: 24,
        lineHeight: '34px',
        height: 68,
      },
    },
    '.content-wrapper': {
      position: 'relative',
      fontSize: 14,
      lineHeight: '20px',
      padding: '10px 16px 0',
      [theme.fn.largerThan('sm')]: {
        padding: '10px 24px 0',
        fontSize: 16,
        lineHeight: 1.5,
      },
      '.content': {
        position: 'relative',
        fontSize: 14,
        lineHeight: '20px',
        zIndex: 1,
        color: theme.colors['gray-700'],
        [theme.fn.largerThan('sm')]: {
          fontSize: 16,
          lineHeight: 1.5,
        },
      },
      '& > svg': {
        position: 'absolute',
        top: 0,
        left: 8,
        width: 16,
        height: 16,
        [theme.fn.largerThan('sm')]: {
          width: 24,
          height: 24,
        },
      },
    },
  },
}));

export default useStyles;
