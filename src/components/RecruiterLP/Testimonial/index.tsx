import IconChat from '@icons/icon-chat-quote.svg';
import IconQuote from '@icons/icon-double-quote.svg';
import IconGlobe from '@icons/icon-globe.svg';
import IconShakingHands from '@icons/icon-shaking-hands.svg';
import type { BoxProps } from '@mantine/core';
import { Box, Button, Text, Title } from '@mantine/core';
import React from 'react';

import useStyles from './styles';

type IQuote = {
  icon: React.ReactElement;
  title: string;
  content: string;
};

const Card = ({
  icon,
  title,
  content,
  ...props
}: Omit<BoxProps, 'children'> & IQuote) => {
  const Icon = () => icon;
  return (
    <Box {...props}>
      <Box className="card-header">
        <Box className="icon">
          <Icon />
        </Box>
        <Box className="label">{title}</Box>
      </Box>
      <Box className="content-wrapper">
        <Text className="content">{content}</Text>
        <IconQuote />
      </Box>
    </Box>
  );
};

const CONTENT_DATA: IQuote[] = [
  {
    icon: <IconGlobe />,
    title: 'テクノワの便利なところは？',
    content:
      '職人がすぐに集まり、案件を断ることが減りました。また、テクノワは全国に職人ネットワークがあるので、出張ではなく現地調達も何度かできています。長期・スポット調達を都合に合わせて活用しています。',
  },
  {
    icon: <IconChat />,
    title: 'サービスの魅力は？',
    content:
      'プラント業界出身の運営陣が、現場のニーズや商習慣を汲み取ったサポートを行ってくれます。代行サービスでは面倒な書類回収まで行ってくれ、大変助かってます。',
  },
  {
    icon: <IconShakingHands />,
    title: '新規発注先の開拓に\n役立ちましたか？',
    content:
      'はい、テクノワでツテ以外の発注先を見つけて取引拡大に繋がりました。腕の良い職人には継続的に発注しています。',
  },
];

const Testimonial = () => {
  const { classes } = useStyles();
  return (
    <Box className={classes.container} component="section" title="Testimonial">
      <Title
        color="black"
        fz={{ base: 24, sm: 36 }}
        lh={{ base: '34px', sm: '52px' }}
        mb={{ base: 32, sm: 48 }}
        order={3}
        ta="center"
      >
        発注企業のコメント
      </Title>
      <Box className={classes.cards}>
        {CONTENT_DATA.map((card, idx) => (
          <Card className={classes.card} key={`quote-${idx}`} {...card} />
        ))}
      </Box>
      <a href="#contact-form">
        <Button
          className="testimonial"
          fullWidth
          fw={500}
          fz={16}
          lh="24px"
          maw={384}
          mt={{ base: 32, sm: 56 }}
          mx={'auto'}
          size="xl"
        >
          お問い合わせはこちら
        </Button>
      </a>
    </Box>
  );
};

export default Testimonial;
