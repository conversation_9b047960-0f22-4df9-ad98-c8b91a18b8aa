import { Box, Flex, Grid, Text, Title } from '@mantine/core';
import Circle from '@recruiter/circle.svg';
import Triangle from '@recruiter/triangle.svg';
import X from '@recruiter/x.svg';
import Image from 'next/image';
import React, { Fragment } from 'react';

import useStyles from './styles';

const CONTENTS = [
  {
    category: '発注範囲の広がり',
    provide: 'テクノワの全国ネットワークから\n新規発注先の開拓が可能',
    difficult: '付き合いのある発注先に限られる',
  },
  {
    category: '直接発注による\n利益獲得',
    provide: '職人との直接マッチングにより\n発注側と受注者の双方に利益が分配',
    difficult: '多重受け構造により\n中間マージンが発生し利益を圧迫',
  },
  {
    category: '発注 プロセスの\n簡便さ',
    provide:
      'プラント業界出身のテクノワ運営陣\nによる代行サポートで、書類回収や応募対\n応の作業負担を軽減',
    difficult:
      '書類回収、協力会社との調整など\n全て自社で対応するので作業負担が大きい',
  },
  {
    category: '職人の質',
    provide:
      'プラント案件に特化しているため、\nプラントの経験豊富な職人を調達可能',
    difficult: '協力会社により異なる',
  },
];

const Comparison = () => {
  const { classes } = useStyles();
  return (
    <Box className={classes.container}>
      <Title
        color="black"
        fz={{ base: 24, sm: 36 }}
        lh={{ base: '34px', sm: '52px' }}
        mb={{ base: 32, sm: 48 }}
        order={3}
        ta="center"
      >
        比較表
      </Title>
      <Grid className={classes.compareTable} grow maw={1184} mx="auto">
        <Grid.Col bg="black" maw="216px !important" span={4}></Grid.Col>
        <Grid.Col bg="primary" p={24} span={4}>
          <Image
            alt="テクノワ"
            height={40}
            quality={100}
            src="/recruiter/logo.png"
            width={150}
          />
        </Grid.Col>
        <Grid.Col
          bg="white"
          p={30}
          span={4}
          sx={{ borderTopRightRadius: '8px' }}
        >
          <Text color="black" fw={700} fz={18} lh="26px" ta="center">
            従来の案件発注プロセスとの比較
          </Text>
        </Grid.Col>
        {CONTENTS.map((content, index) => {
          return (
            <Fragment key={`compare-pc-${index}`}>
              <Grid.Col
                bg="black"
                maw="216px !important"
                span={4}
                sx={{ justifyContent: 'center' }}
              >
                <Text color="white" fw={700} fz={18} lh="26px">
                  {content.category}
                </Text>
              </Grid.Col>
              <Grid.Col bg="#FBEBE8" p="24px 40px" span={4}>
                <Circle />
                <Text color="black" fw={400} fz={16} lh="24px" mt={10}>
                  {content.provide}
                </Text>
              </Grid.Col>
              <Grid.Col bg="white" p="24px 40px" span={4}>
                {index % 2 === 0 ? <X /> : <Triangle />}
                <Text color="black" fw={400} fz={16} lh="24px" mt={10}>
                  {content.difficult}
                </Text>
              </Grid.Col>
            </Fragment>
          );
        })}
      </Grid>
      <Flex className={classes.compareTableMobile}>
        {CONTENTS.map((content, index) => {
          return (
            <Box className={classes.compareItem} key={`compare-sp-${index}`}>
              <Box bg="black" p={16}>
                <Text
                  color="white"
                  fw={700}
                  fz={14}
                  lh="20px"
                  sx={{ whiteSpace: 'inherit' }}
                  ta="center"
                >
                  {content.category}
                </Text>
              </Box>
              <Box bg="#FBEBE8" p="16px 20px">
                <Flex align="center" justify="space-between">
                  <Image
                    alt="テクノワ"
                    height={24}
                    quality={100}
                    src="/icons/logo-horizontal.svg"
                    width={90}
                  />
                  <Circle height={24} width={24} />
                </Flex>
                <Text color="black" fw={400} fz={14} lh="20px" mt={8}>
                  {content.provide}
                </Text>
              </Box>
              <Box bg="white" p="16px 20px">
                <Flex align="center" justify="space-between">
                  <Text color="black" fw={700} fz={14} lh="20px">
                    従来の案件発注プロセスとの比較
                  </Text>
                  {index % 2 === 0 ? (
                    <X height={24} width={24} />
                  ) : (
                    <Triangle height={24} width={24} />
                  )}
                </Flex>
                <Text color="black" fw={400} fz={14} lh="20px" mt={8}>
                  {content.difficult}
                </Text>
              </Box>
            </Box>
          );
        })}
      </Flex>
    </Box>
  );
};

export default Comparison;
