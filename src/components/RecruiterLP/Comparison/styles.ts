import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.colors['gray-200'],
    padding: '72px 20px',
    [theme.fn.smallerThan('sm')]: {
      padding: '48px 20px',
    },
  },
  compareTable: {
    borderRadius: '20px',
    border: `1px solid ${theme.colors.black}`,
    [theme.fn.smallerThan('sm')]: {
      display: 'none',
    },
    '& > *': {
      textAlign: 'center',
      display: 'flex',
      alignItems: 'center',
      flexDirection: 'column',
    },
    '& > :not(:nth-last-of-type(-n+3))': {
      borderBottom: `0.5px solid ${theme.colors['gray-500']}`,
    },
    '& > :nth-of-type(3n+2)': {
      borderRight: `1px solid ${theme.colors.black}`,
    },
    '& > :nth-of-type(3)': {
      borderTopRightRadius: '20px',
    },
    '& > :first-of-type': {
      borderTopLeftRadius: '20px',
    },
    '& > :nth-last-of-type(3)': {
      borderBottomLeftRadius: '20px',
    },
    '& > :last-of-type': {
      borderBottomRightRadius: '20px',
    },
  },
  compareTableMobile: {
    flexDirection: 'column',
    alignItems: 'center',
    gap: 12,
    [theme.fn.largerThan('sm')]: {
      display: 'none',
    },
  },
  compareItem: {
    maxWidth: 335,
    width: '100%',
    borderRadius: '20px',
    border: `1px solid ${theme.colors.black}`,
    '& > :first-of-type': {
      borderTopRightRadius: '20px',
      borderTopLeftRadius: '20px',
    },
    '& > :last-of-type': {
      borderBottomRightRadius: '20px',
      borderBottomLeftRadius: '20px',
    },
  },
}));

export default useStyles;
