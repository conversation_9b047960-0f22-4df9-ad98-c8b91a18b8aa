import { <PERSON>, Button, Flex } from '@mantine/core';
import { AnimatePresence, motion } from 'framer-motion';
import { useGlobalState, useMutate } from 'hooks';
import { chatService } from 'models/chat/chatService';
import type { IMessage, MetaAction } from 'models/chat/type';
import { ActionHandlerNames } from 'models/chat/type';
import jobQuery from 'models/job';
import type { IConfirmCollectMethodDTO } from 'models/job/type';
import { CollectMethod } from 'models/job/type';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { sleep } from 'utils/helpers';

interface MetaActionExtensionForMessageBubbleProps {
  metaActions: MetaAction[];
  onActionClick?: (action: MetaAction, idx: number) => void;
  message: IMessage;
  roomId: string;
}

const MetaActionExtensionForMessageBubble: React.FC<
  MetaActionExtensionForMessageBubbleProps
> = ({ metaActions, onActionClick, message, roomId }) => {
  const { query } = useRouter();
  const matchingId = typeof query.id === 'string' ? query.id : '';
  const [isHandlingAction, setIsHandlingAction] = useState(false);

  const { chatUploadRef } = useGlobalState();

  const { mutateAsync: confirmMatchingCollectingMethod } =
    useMutate<IConfirmCollectMethodDTO>(
      jobQuery.confirmMatchingCollectingMethod(matchingId),
    );
  const { mutateAsync: completeUploadAndStartOCR } = useMutate(
    jobQuery.completeUploadAndStartOCR(matchingId),
  );

  const handleActionCases = async (action: MetaAction) => {
    setIsHandlingAction(true);
    try {
      switch (action.action) {
        case ActionHandlerNames.CONFIRM_COLLECT_METHOD_MANUAL:
          await confirmMatchingCollectingMethod({
            collectMethod: CollectMethod.MANUAL,
          });

          await chatService.clearMessageMetaAction({
            messageId: message.id,
            roomId,
          });
          return;
        case ActionHandlerNames.CONFIRM_COLLECT_METHOD_UPLOAD:
          await confirmMatchingCollectingMethod({
            collectMethod: CollectMethod.UPLOAD,
          });

          await chatService.clearMessageMetaAction({
            messageId: message.id,
            roomId,
          });
          break;

        case ActionHandlerNames.COMPLETE_UPLOAD:
          await completeUploadAndStartOCR({});

          await chatService.clearAllMetaActionsFromCache(roomId);
          break;
        case ActionHandlerNames.TRIGGER_UPLOAD_FILE:
          if (chatUploadRef?.current) {
            chatUploadRef.current.click();
          }
          break;

        default:
          // No return needed for async function
          break;
      }
    } catch (error) {
      console.error('Error handling action:', error);
    } finally {
      await sleep(1000);
      setIsHandlingAction(false);
    }
  };

  const hasActions = Array.isArray(metaActions) && metaActions.length > 0;

  return (
    <AnimatePresence>
      {hasActions && (
        <motion.div
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          initial={{ height: 0, opacity: 0 }}
          style={{ overflow: 'hidden' }}
          transition={{
            duration: 0.3,
            ease: 'easeInOut',
          }}
        >
          <Flex gap={8} mt={8}>
            {metaActions.map((action, idx) => (
              <Box key={idx}>
                <Button
                  disabled={isHandlingAction}
                  loading={isHandlingAction}
                  onClick={() => {
                    onActionClick?.(action, idx);
                    handleActionCases(action);
                  }}
                  {...(action.props || {})}
                  sx={{
                    minWidth: 148,
                  }}
                >
                  {action.content}
                </Button>
              </Box>
            ))}
          </Flex>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MetaActionExtensionForMessageBubble;
