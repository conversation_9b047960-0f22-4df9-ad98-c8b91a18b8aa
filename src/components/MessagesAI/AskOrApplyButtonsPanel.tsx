import { Box, Button, Flex, Text } from '@mantine/core';
import { useMutate } from 'hooks';
import { chatService } from 'models/chat/chatService';
import type { IRoom } from 'models/chat/type';
import jobQuery from 'models/job';
import type { IJobMatchingItem } from 'models/job/type';
import React, { useEffect, useState } from 'react';

interface AskOrApplyButtonsPanelProps {
  onAskClick?: () => void;
  onApplyClick?: () => void;
}

const useAskOrApplyButtonsPanel = ({
  roomData,
  matchingData,
  firebaseUserId,
  refetchMatchingData,
  chatTemplateCtl: [showChatTemplate, setShowChatTemplate],
}: {
  roomData?: IRoom;
  matchingData?: IJobMatchingItem;
  firebaseUserId?: string;
  refetchMatchingData?: () => any;
  chatTemplateCtl: [boolean, (newState: boolean) => void];
}) => {
  const [leftBtnShow, setLeftBtnShow] = useState<boolean>(showChatTemplate);
  const [isUIReady, setUIReady] = useState<boolean>(false);

  const isReady = !!roomData && !!matchingData;
  const isAIAssisting = roomData?.isAIAssisting;
  const askOrApplyBtnPanelVisible =
    isReady &&
    isUIReady &&
    isAIAssisting &&
    firebaseUserId &&
    matchingData?.status === 'CONTACT';

  useEffect(() => {
    async function senderHasMessageBefore() {
      const hasMessageBefore =
        roomData?.id && firebaseUserId
          ? await chatService.hasMessageBefore(roomData.id, firebaseUserId)
          : false;

      // Only show the template if we're showing the message input and user hasn't sent messages before
      setLeftBtnShow(!hasMessageBefore);
      setShowChatTemplate(false);

      if (!isUIReady) {
        setUIReady(true);
      }
    }

    if (isReady) {
      senderHasMessageBefore();
    }
  }, [firebaseUserId, setLeftBtnShow, isReady]);

  const { mutateAsync: applyWithAIAssistant } = useMutate<
    {
      jobPostId: string;
    },
    IJobMatchingItem
  >(jobQuery.applyWithAIAssistant);

  const AskOrApplyButtonsPanel: React.FC<AskOrApplyButtonsPanelProps> = ({
    onAskClick,
    onApplyClick,
  }) => {
    if (!askOrApplyBtnPanelVisible) {
      return null;
    }

    return (
      <Box px={{ base: 32, sm: 24 }} py={24}>
        <Text
          align="center"
          color="gray-500"
          fw={400}
          fz={12}
          lh="16px"
          mb={16}
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              textAlign: 'left',
            },
          })}
        >
          案件に関するお問合せは、チャットボットがご案内いたします。 <br />
          ［お問合せ］ボタンからお気軽にご質問ください。
          <br />
          ご応募をご希望の方は［応募する］ボタンから、作業員情報の入力または、名簿のアップロードへお進みください。
        </Text>
        <Flex
          gap={16}
          justify="center"
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              flexDirection: 'column',
            },
          })}
        >
          {leftBtnShow && (
            <Button
              color="neutral-500"
              onClick={() => {
                setLeftBtnShow(false);
                setShowChatTemplate(true);
                return onAskClick?.();
              }}
              size="md"
              sx={(theme) => ({
                flex: 1,
                maxWidth: 148,
                height: 48,
                [theme.fn.smallerThan('sm')]: {
                  maxWidth: 'none',
                  width: '100%',
                },
              })}
              variant="outline"
            >
              お問合せ
            </Button>
          )}
          <Button
            color="primary"
            onClick={async () => {
              if (matchingData?.jobPostId) {
                await applyWithAIAssistant({
                  jobPostId: matchingData?.jobPostId || '',
                });
                await refetchMatchingData?.();
              }

              return onApplyClick?.();
            }}
            size="md"
            sx={(theme) => ({
              flex: 1,
              maxWidth: 148,
              height: 48,
              [theme.fn.smallerThan('sm')]: {
                maxWidth: 'none',
                flex: 'unset',
                width: '100%',
              },
            })}
          >
            応募する
          </Button>
        </Flex>
      </Box>
    );
  };

  let chatActionSectionState: 'disabled' | 'hidden' | 'active' = 'active';
  if (isReady && isAIAssisting) {
    chatActionSectionState = 'active';
  }
  if (isReady && isAIAssisting && askOrApplyBtnPanelVisible && leftBtnShow) {
    chatActionSectionState = 'disabled';
  }

  return {
    AskOrApplyButtonsPanel,
    isReady,
    isAIAssisting,
    chatActionSectionState,
  };
};

export default useAskOrApplyButtonsPanel;
