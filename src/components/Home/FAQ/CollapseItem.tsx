import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconQuestion from '@icons/icon-question.svg';
import type { BoxProps } from '@mantine/core';
import { Box, Button, Collapse, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import React from 'react';

const CollapseItem = ({
  label,
  content,
  ...props
}: { label: string; content: string | React.ReactElement } & BoxProps) => {
  const [opened, { toggle }] = useDisclosure(false);

  const Content = () => {
    if (typeof content === 'string') {
      return <Text className="content">{content}</Text>;
    }

    return React.cloneElement(content, { className: 'content' });
  };

  return (
    <Button
      {...props}
      onClick={toggle}
      sx={{
        '&::after': {
          zIndex: -1,
        },
      }}
      unstyled
    >
      <Text className="label">
        <IconQuestion />
        {label}
      </Text>
      <Box
        className="indicator-arrow"
        sx={opened ? { transform: 'rotate(-180deg)' } : {}}
      >
        <IconArrowDown />
      </Box>
      <Collapse in={opened}>
        <Content />
      </Collapse>
    </Button>
  );
};

export default CollapseItem;
