import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  faqContainer: {
    maxWidth: 1220,
    margin: '0 auto',
    padding: '40px 16px 72px',
    [theme.fn.smallerThan('sm')]: {
      padding: '25px 16px 40px',
    },
  },
  faqWrapper: {
    borderRadius: '45px',
    backgroundColor: '#FBEBE8',
    padding: '56px 16px 72px',
    position: 'relative',
    overflow: 'hidden',
    zIndex: 0,
    [theme.fn.smallerThan('sm')]: {
      padding: '48px 16px 40px',
    },
  },
  faqItem: {
    maxWidth: 784,
    width: '100%',
    backgroundColor: 'white',
    margin: '0 auto',
    borderRadius: '10px',
    display: 'block',
    border: 0,
    textAlign: 'start',
    padding: '36px 24px',
    cursor: 'pointer',
    position: 'relative',
    zIndex: 1,
    [theme.fn.smallerThan('sm')]: {
      padding: '12px 20px',
    },
    '.label': {
      display: 'flex',
      alignItems: 'center',
      gap: 11,
      fontSize: 18,
      fontWeight: 700,
      lineHeight: '26px',
      color: theme.colors.black,
      paddingRight: 24,
      [theme.fn.smallerThan('sm')]: {
        fontSize: 16,
        gap: 8,
        lineHeight: '24px',
      },
      svg: {
        flexShrink: 0,
        width: 32,
        height: 32,
        [theme.fn.smallerThan('sm')]: {
          width: 24,
          height: 24,
        },
      },
    },
    '.content': {
      color: theme.colors['gray-700'],
      fontSize: 16,
      fontWeight: 400,
      lineHeight: '24px',
      marginTop: 12,
      paddingLeft: 36,
    },
    '.indicator-arrow': {
      position: 'absolute',
      top: 37,
      right: 32,
      transition: 'all 0.2s ease-in',
      transformOrigin: 'center',
      width: 24,
      height: 24,
      svg: {
        width: 24,
        height: 24,
      },
      [theme.fn.smallerThan('sm')]: {
        top: 12,
        right: 12,
      },
    },
    '&:hover': {
      backgroundColor: 'white',
    },
    '&:not(:last-of-type)': {
      marginBottom: 16,
    },
  },
}));

export default useStyles;
