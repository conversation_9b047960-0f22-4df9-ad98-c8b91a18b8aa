import { Box, Text } from '@mantine/core';
import { motion } from 'framer-motion';
import Link from 'next/link';
import React from 'react';

import CollapseItem from './CollapseItem';
import useStyles from './styles';

const FAQs: { question: string; answer: string | React.ReactElement }[] = [
  {
    question: 'テクノワでどうやって案件を探しますか？',
    answer:
      'ホームページのオススメ案件または検索機能から案件を見つかることができます。そこから発注企業との連絡や案件に応募することができます。',
  },
  {
    question: 'テクノワでどうやって応募しますか？',
    answer:
      '案件一覧か案件詳細から案件に応募することができます。 応募する際、履歴書や必要な書類をアップロードし応募できます。 応募した案件は案件管理から確認することができます。',
  },
  {
    question: 'テクノワは無料ですか？',
    answer: 'テクノワは完全無料です。',
  },
  {
    question: 'テクノワを利用するとどのくらいの期間で案件が決まりますか？',
    answer:
      '現在募集中の案件であれば書類提出がスムーズであれば約1週間～２週間程度で決まる予定です。',
  },
  {
    question: '案件を発注したいのですがどうすればいいですか？',
    answer: (
      <Box>
        <Text color="inherit" inherit>
          発注業者様としても登録されたい方はSNSからお気軽にお問い合わせください。
        </Text>
        <Text color="inherit" inherit>
          Instagram：
          <Link
            href="https://www.instagram.com/teqnowa/"
            onClick={(e) => {
              e.stopPropagation();
            }}
            target="_blank"
          >
            https://www.instagram.com/teqnowa/
          </Link>
        </Text>
        <Text color="inherit" inherit>
          X：
          <Link
            href={'https://twitter.com/teqnowa'}
            onClick={(e) => {
              e.stopPropagation();
            }}
            target="_blank"
          >
            https://twitter.com/teqnowa
          </Link>
        </Text>
        <Text color="inherit" inherit>
          Line：
          <Link
            href={
              'https://liff.line.me/**********-kWRPP32q/?accountId=803ucajy'
            }
            onClick={(e) => {
              e.stopPropagation();
            }}
            target="_blank"
          >
            liff.line.me/**********-kWRPP32q/?accountId=803ucajy
          </Link>
        </Text>
      </Box>
    ),
  },
];

const FAQ = () => {
  const { classes } = useStyles();

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      duration: 1,
      transition: {
        delayChildren: 0.5,
        staggerChildren: 0.25,
      },
    },
  };

  const item = {
    hidden: { scale: 0, x: '-50%', y: '-50%' },
    show: {
      scale: 1,
      transition: {
        bounce: 0.8,
        duration: 0.5,
      },
    },
  };
  const item1 = {
    hidden: { scale: 0, x: '50%', y: '50%' },
    show: {
      scale: 1,
      transition: {
        bounce: 0.8,
        duration: 0.5,
      },
    },
  };

  return (
    <Box
      className={classes.faqContainer}
      component={motion.div}
      initial="hidden"
      variants={container}
      viewport={{ once: true, amount: 0.2 }}
      whileInView="show"
    >
      <Box className={classes.faqWrapper}>
        <Text
          color="primary"
          fw={500}
          fz={{ base: '12px', sm: '14px' }}
          lh={{ base: '16px', sm: '20px' }}
          mb={8}
          pos="relative"
          ta="center"
        >
          FAQ
        </Text>
        <Text
          color="black"
          fw={700}
          fz={{ base: '32px', sm: '36px' }}
          lh={{ base: '46px', sm: '52px' }}
          mb={{ base: 24, sm: 40 }}
          pos="relative"
          sx={{ zIndex: 1 }}
          ta="center"
        >
          よくある質問
        </Text>
        {FAQs.map((qa, i) => (
          <CollapseItem
            className={classes.faqItem}
            content={qa.answer}
            key={i}
            label={qa.question}
          />
        ))}
        <Box
          bg="primary"
          component={motion.div}
          h={{ base: 140, sm: 281 }}
          left={{ base: 12, sm: 0 }}
          pos="absolute"
          sx={{
            zIndex: 0,
            borderRadius: '50%',
          }}
          top={{ base: 12, sm: 0 }}
          variants={item}
          w={{ base: 140, sm: 281 }}
        />
        <Box
          bg="primary"
          component={motion.div}
          h={{ base: 80, sm: 173 }}
          left={0}
          opacity={0.2}
          pos="absolute"
          sx={{
            zIndex: 0,
            borderRadius: '50%',
          }}
          top={{ base: 80, sm: 140 }}
          variants={item}
          w={{ base: 80, sm: 173 }}
        />
        <Box
          bg="primary"
          bottom={22}
          component={motion.div}
          h={{ base: 170, sm: 341 }}
          opacity={0.2}
          pos="absolute"
          right={22}
          sx={{
            zIndex: 0,
            borderRadius: '50%',
          }}
          variants={item1}
          w={{ base: 170, sm: 341 }}
        />
      </Box>
    </Box>
  );
};

export default FAQ;
