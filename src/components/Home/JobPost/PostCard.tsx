import IconChat from '@icons/icon-chat.svg';
import IconChatPrompt from '@icons/icon-chat-prompt.svg';
import IconClock from '@icons/icon-clock.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import { But<PERSON>, Card, Divider, Flex, Text, ThemeIcon } from '@mantine/core';
import { useMutate, useUser } from 'hooks';
import jobQuery from 'models/job';
import type { IJobMatchingItem, IJobPostItem } from 'models/job/type';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

import useStyles from './styles';

const PostCard = ({ data }: { data: IJobPostItem }) => {
  const { push } = useRouter();
  const { data: currentUser } = useUser();
  const { classes } = useStyles();

  const { mutateAsync: openContactAIAssistant, isLoading: isOpenContact } =
    useMutate<
      {
        jobPostId: string;
      },
      IJobMatchingItem
    >(jobQuery.openContactAIAssistant);

  const isOpenChatWithRecruiter = ['FINISHED', 'APPLYING'].includes(
    data?.appliedMatching?.status ?? '',
  );

  const handleOpenContact: React.MouseEventHandler<HTMLButtonElement> = async (
    e,
  ) => {
    e.preventDefault();
    if (currentUser) {
      const matchingDetail = await openContactAIAssistant(
        {
          jobPostId: data._id,
        },
        {
          onSuccess: (res) => {
            if (res?.newGroupChat) {
              helpers.logEventTracking('start_group_chat', {
                recruiter_id: res?.jobPostInfo?.createdBy,
                job_id: res?.jobPostId,
                status: 'contact',
              });
            } else {
              helpers.logEventTracking('view_group_chat', {
                chat_id: res?._id,
              });
            }
          },
        },
      );
      push(`/my-page/jobs/${matchingDetail.chatInfo.roomFirebaseId}/messages`);
    } else {
      push('/login');
    }
  };

  return (
    <Card
      className={classes.itemCard}
      component={Link}
      href={`/jobs/${data._id}?f=recommended`}
      shadow="sm"
    >
      <Text color="gray-600" lh="20px" lineClamp={1} mb={4}>
        {data.companyId.name}
      </Text>
      <Text
        color="black"
        lh="26px"
        lineClamp={3}
        mb={{ base: 12, sm: 8 }}
        size={18}
        sx={{ flex: 1 }}
        weight={700}
      >
        {data.title}
      </Text>
      <Text className={classes.infoRow} mb={{ base: 8, sm: 6 }}>
        <ThemeIcon color="primary" size={16}>
          <IconLocation height={16} width={16} />
        </ThemeIcon>
        <span>{data.workArea}</span>
      </Text>
      <Text className={classes.infoRow}>
        <IconClock height={16} width={16} />
        <span>
          {dayjs(data.startDate).format('LL')}{' '}
          {data.endDate ? `- ${dayjs(data.endDate).format('LL')}` : ''}
        </span>
      </Text>
      <Divider my={{ base: 12, sm: 16 }} />
      <Flex
        align="center"
        className={classes.cardFooter}
        gap={10}
        justify="space-between"
      >
        <Text
          color="primary"
          size={16}
          sx={{ wordBreak: 'break-word' }}
          weight={700}
        >
          単価(税抜):{' '}
          {data.isPrivateSalary
            ? 'メッセージにて見積もり依頼'
            : helpers.renderSalary(data.minSalary || 0, data.maxSalary)}
        </Text>
        <Flex gap={8} w={{ base: '100%', sm: 'auto' }}>
          {isOpenChatWithRecruiter ? (
            <Button
              fw={500}
              fz={14}
              leftIcon={<IconChat />}
              loading={isOpenContact}
              onClick={handleOpenContact}
              size="sm"
              variant="outline"
            >
              問合せする
            </Button>
          ) : (
            <Button
              fw={500}
              fz={14}
              leftIcon={<IconChatPrompt />}
              loading={isOpenContact}
              onClick={handleOpenContact}
              size="sm"
            >
              ご応募・お問合せ
            </Button>
          )}
        </Flex>
      </Flex>
    </Card>
  );
};

export default PostCard;
