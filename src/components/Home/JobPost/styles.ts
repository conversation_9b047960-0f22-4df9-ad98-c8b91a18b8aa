import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  jobPostContainer: {
    padding: '64px 0 40px',
    [theme.fn.smallerThan('sm')]: {
      padding: '32px 0 24px',
    },
  },
  carouselViewport: {
    maxWidth: 1184,
    margin: '0 auto',
    paddingBottom: 34,
    alignItems: 'stretch',
    [theme.fn.smallerThan('sm')]: {
      paddingBottom: 24,
    },
  },
  carouselControls: {
    top: 'calc(50% - 82px / 2)',
  },
  carouselControl: {
    backgroundColor: theme.colors.primary,
    color: 'white',
    border: 0,
    opacity: 1,
    transition: 'all 0.2s ease-in',
    '&:hover': {
      backgroundColor: theme.colors['neutral-600'],
    },
    '&[data-inactive]': {
      backgroundColor: theme.colors['neutral-100'],
      color: theme.colors['neutral-400'],
      cursor: 'default',
    },
  },
  carouselIndicators: {
    bottom: 0,
  },
  carouselIndicator: {
    backgroundColor: theme.colors['gray-300'],
    width: 10,
    height: 10,
    opacity: 1,
    '&[data-active]': {
      backgroundColor: theme.colors.primary,
    },
  },
  itemCard: {
    display: 'flex',
    flexDirection: 'column',
    padding: 16,
    height: '100%',
    width: '100%',
  },
  infoRow: {
    color: theme.colors['gray-700'],
    lineHeight: '20px',
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  },
  cardFooter: {
    [theme.fn.smallerThan('sm')]: {
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
  },
  applyBtn: {
    width: '100%',
  },
}));

export default useStyles;
