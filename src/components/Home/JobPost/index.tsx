import IconSearch from '@icons/icon-search.svg';
import { Carousel } from '@mantine/carousel';
import { Box, Button, Flex, Loader, Text } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { motion } from 'framer-motion';
import { useFetch } from 'hooks';
import jobQuery from 'models/job';
import type { IJobPostItem } from 'models/job/type';
import Link from 'next/link';
import React from 'react';
import type { IListResult } from 'utils/type';

import PostCard from './PostCard';
import useStyles from './styles';

const JobCarousel = () => {
  const { classes } = useStyles();
  const screenMatches = useMediaQuery('(min-width: 62em)');

  const { data, isLoading } = useFetch<IListResult<IJobPostItem>>(
    jobQuery.recommendJobPosts,
  );

  const renderList = () => {
    if ((data?.docs?.length || 0) > 1) {
      return (
        <Carousel
          align="start"
          breakpoints={[
            {
              maxWidth: 'md',
              slideSize: '80%',
            },
            {
              maxWidth: 'sm',
              slideSize: '90%',
              slideGap: 'sm',
            },
          ]}
          classNames={{
            controls: classes.carouselControls,
            control: classes.carouselControl,
            viewport: classes.carouselViewport,
            indicators: classes.carouselIndicators,
            indicator: classes.carouselIndicator,
          }}
          controlSize={48}
          controlsOffset={0}
          maw={1252}
          mx="auto"
          pl={{ base: 12, md: 72 }}
          pr={{ base: 0, md: 72 }}
          slideGap="md"
          slideSize="33.333333%"
          slidesToScroll={screenMatches ? 3 : 1}
          withControls={screenMatches}
          withIndicators
        >
          {data?.docs?.map((item) => (
            <Carousel.Slide key={item._id}>
              <PostCard data={item} />
            </Carousel.Slide>
          ))}
        </Carousel>
      );
    }
    return (
      <Flex gap={16} justify="center" px={16}>
        {data?.docs.map((item) => (
          <Box key={item._id} maw={384} w="100%">
            <PostCard data={item} />
          </Box>
        ))}
        {isLoading && <Loader h={200} size={36} />}
      </Flex>
    );
  };

  if (!isLoading && data?.docs.length === 0) {
    return <></>;
  }

  return (
    <Box
      className={classes.jobPostContainer}
      component={motion.div}
      initial={{ opacity: 0 }}
      transition={{ duration: 1, delay: 0.5 }}
      viewport={{ once: true, amount: 0.2 }}
      whileInView={{ opacity: 1 }}
    >
      <Text color="primary" lh="20px" mb={8} size={14} ta="center" weight={500}>
        RECOMMENDED JOB POST
      </Text>
      <Text color="black" lh="52px" mb={32} size={36} ta="center" weight={700}>
        おすすめ案件
      </Text>
      {renderList()}
      <Button
        component={Link}
        display="block"
        href={'/jobs'}
        leftIcon={<IconSearch color="white" />}
        mt={32}
        mx="auto"
        w={'fit-content'}
      >
        もっと案件を見る
      </Button>
    </Box>
  );
};

export default JobCarousel;
