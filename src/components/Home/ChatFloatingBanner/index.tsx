import { Button, Flex, Text, useMantineTheme } from '@mantine/core';
import { useMediaQuery, useWindowScroll } from '@mantine/hooks';
import { useRouter } from 'next/router';
import React from 'react';

import useStyles from './styles';

const ChatFloatingBanner = () => {
  const { classes } = useStyles();
  const { push } = useRouter();
  const { colors } = useMantineTheme();

  const [{ y }] = useWindowScroll();
  const isMobile = useMediaQuery('(max-width: 48em)');

  const isOverlay = y > 144 && isMobile;

  return (
    <Flex
      align={'center'}
      className={classes.bannerWrapper}
      direction={'row'}
      justify={'center'}
      style={{
        position: isOverlay ? 'fixed' : 'inherit',
      }}
      sx={{
        left: isOverlay ? 16 : 'unset',
        right: isOverlay ? 16 : 'unset',
        bottom: isOverlay ? 16 : 'unset',
        borderRadius: isOverlay ? 10 : 0,
      }}
    >
      <Text
        align="center"
        color="white"
        fw={500}
        fz={{ xs: 14, sm: 18 }}
        lh={{ xs: '20px', sm: '26px' }}
      >
        案件紹介から書類応募までテクノワがサポートします
      </Text>
      <Button
        bg={'white'}
        onClick={() => push('/contact-admin')}
        sx={{
          border: '1px solid',
          borderColor: colors.primary,
        }}
        variant="subtle"
      >
        テクノワに相談
      </Button>
    </Flex>
  );
};

export default ChatFloatingBanner;
