import { yupResolver } from '@hookform/resolvers/yup';
import IconJobType from '@icons/icon-job-type.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import IconSearch from '@icons/icon-search.svg';
import type { MantineTheme } from '@mantine/core';
import {
  Box,
  Button,
  Center,
  Flex,
  MediaQuery,
  Stack,
  Text,
  ThemeIcon,
} from '@mantine/core';
import MultiSelectCheckboxField from 'components/Form/MultiSelectCheckboxField';
import { useFetch } from 'hooks';
import jobQuery from 'models/job';
import type { IJobType } from 'models/job/type';
import resourceQuery from 'models/resource';
import type { IPrefecture, ISelectableOption } from 'models/resource/type';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';
import type { InferType } from 'yup';
import { array, object, string } from 'yup';

import useStyles from './styles';

const schema = object({
  workAreas: array().of(string().trim()).nullable(),
  jobTypeIds: array().of(string().trim()).nullable(),
});

export type SearchFormValues = InferType<typeof schema>;

const SearchForm = () => {
  const { classes } = useStyles();
  const router = useRouter();
  const { control, handleSubmit, watch } = useForm({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  const { data: prefectureOptions = [] } = useFetch<
    IPrefecture[],
    ISelectableOption[]
  >({
    ...resourceQuery.prefectures,
    select: (data) =>
      data.map((item) => ({
        label: item.prefecture_kanji,
        value: item.prefecture_kanji,
      })),
  });
  const { data: jobTypes = [] } = useFetch<
    { jobTypes: IJobType[] },
    ISelectableOption[]
  >({
    ...jobQuery.getJobTypes,
    customParams: {
      page: 1,
      limit: 9999,
    },
    select: (data) =>
      data.jobTypes.map((item) => ({ label: item.name, value: item._id })),
  });

  function pushToJobPostListPage(data: SearchFormValues): void {
    const params = new URLSearchParams();
    Object.entries(data).forEach(([key, value]) => {
      const filteredValues = (value || [])?.filter((v) => v !== 'all');
      if (filteredValues?.length) {
        params.set(key, filteredValues.join(','));
      }
    });
    router.push(`/jobs?${params.toString()}`);
  }

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(pushToJobPostListPage)}
      sx={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Flex
        className={classes.searchFormWrapper}
        direction={{ base: 'column', sm: 'row' }}
        gap={16}
        wrap={'wrap'}
      >
        <Stack mb={'8px'}>
          <MultiSelectCheckboxField
            clearable
            control={control}
            data={prefectureOptions}
            icon={
              <ThemeIcon color="primary">
                <IconLocation color={'#D23A19'} height={24} width={24} />
              </ThemeIcon>
            }
            maxDropdownHeight={200}
            name="workAreas"
            placeholder="稼働エリア"
            renderSelectedValueComponent={({ selectedCount }) => (
              <Flex gap={'loose'} w={'100%'}>
                <Text color="gray-600" fw={400} fz={'lg'} lh={'26px'}>
                  稼働エリア
                </Text>
                <Center
                  h={'24px'}
                  p={'2px'}
                  sx={(theme) => ({
                    backgroundColor: theme.colors.primary,
                    borderRadius: '100%',
                  })}
                  w={'24px'}
                >
                  <Text color="white" fw={700} fz={'12px'} lh={'16px'}>
                    {selectedCount}
                  </Text>
                </Center>
              </Flex>
            )}
            sx={{
              '&.mantine-MultiSelect-root': {
                padding: 0,
              },
              '.mantine-MultiSelect-dropdown .mantine-MultiSelect-itemsWrapper':
                {
                  padding: '8px',
                },
            }}
          />
        </Stack>
        <Stack mb={'8px'}>
          <MultiSelectCheckboxField
            clearable
            control={control}
            data={jobTypes}
            icon={
              <ThemeIcon color="primary" size={24}>
                <IconJobType color={'#D23A19'} height={24} width={24} />
              </ThemeIcon>
            }
            name="jobTypeIds"
            placeholder="職種"
            renderSelectedValueComponent={({ selectedCount }) => (
              <Flex gap={'loose'} w={'100%'}>
                <Text color="gray-600" fw={400} fz={'lg'} lh={'26px'}>
                  職種
                </Text>
                <Center
                  h={'24px'}
                  p={'2px'}
                  sx={(theme) => ({
                    backgroundColor: theme.colors.primary,
                    borderRadius: '100%',
                  })}
                  w={'24px'}
                >
                  <Text color="white" fw={700} fz={'12px'} lh={'16px'}>
                    {selectedCount}
                  </Text>
                </Center>
              </Flex>
            )}
            sx={{
              '&.mantine-MultiSelect-root': {
                padding: 0,
              },
              '.mantine-MultiSelect-dropdown .mantine-MultiSelect-itemsWrapper':
                {
                  padding: '8px',
                },
            }}
          />
        </Stack>
        <MediaQuery largerThan="sm" styles={{ display: 'none' }}>
          <Box
            sx={{
              textAlign: 'left',
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
            }}
          >
            {watch('workAreas')?.length ? (
              <Text
                fz={{ base: 14, sm: 16 }}
                sx={(theme: MantineTheme) => ({
                  color: theme.colors['gray-600'],
                  fontWeight: 400,
                  lineHeight: '24px',
                })}
              >
                <Text
                  color="gray-600"
                  display="inline-flex"
                  fw={700}
                  fz={'inherit'}
                  mr={'2px'}
                >
                  稼働エリア:
                </Text>
                {watch('workAreas')
                  .filter((item: string) => item !== 'all')
                  .join('・')}
              </Text>
            ) : null}

            {watch('jobTypeIds')?.length && jobTypes?.length ? (
              <Text
                fz={{ base: 14, sm: 16 }}
                sx={(theme: MantineTheme) => ({
                  color: theme.colors['gray-600'],
                  fontWeight: 400,
                  lineHeight: '24px',
                })}
              >
                <Text
                  color="gray-600"
                  display="inline-flex"
                  fw={700}
                  fz={'inherit'}
                  mr={'2px'}
                >
                  職種:
                </Text>
                {watch('jobTypeIds')
                  .filter((jtId: string) => jtId !== 'all')
                  .map(
                    (jobTypeId: string) =>
                      jobTypes.find(({ value }) => value === jobTypeId)
                        ?.label ?? '',
                  )
                  .join('・')}
              </Text>
            ) : null}
          </Box>
        </MediaQuery>
        <Button
          leftIcon={<IconSearch color="white" />}
          mah={'56px'}
          size="lg"
          sx={(theme: MantineTheme) => ({
            [theme.fn.smallerThan('sm')]: {
              '.mantine-Button-inner': {
                height: '30px',
              },
            },
          })}
          type="submit"
          w={{ base: undefined, sm: 'fit-content' }}
        >
          検索
        </Button>
      </Flex>

      <MediaQuery smallerThan="sm" styles={{ display: 'none' }}>
        <Box
          sx={{
            textAlign: 'left',
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
          }}
        >
          {watch('workAreas')?.length ? (
            <Text
              sx={(theme: MantineTheme) => ({
                color: theme.colors['gray-600'],
                fontWeight: 400,
                lineHeight: '24px',
                fontSize: '16px',
              })}
            >
              <Text
                color="gray-600"
                display="inline-flex"
                fw={700}
                fz={'16px'}
                mr={'2px'}
              >
                稼働エリア:
              </Text>
              {watch('workAreas')
                .filter((item: string) => item !== 'all')
                .join('・')}
            </Text>
          ) : null}

          {watch('jobTypeIds')?.length && jobTypes?.length ? (
            <Text
              sx={(theme: MantineTheme) => ({
                color: theme.colors['gray-600'],
                fontWeight: 400,
                lineHeight: '24px',
                fontSize: '16px',
              })}
            >
              <Text
                color="gray-600"
                display="inline-flex"
                fw={700}
                fz={'16px'}
                mr={'2px'}
              >
                職種:
              </Text>
              {watch('jobTypeIds')
                .filter((jtId: string) => jtId !== 'all')
                .map(
                  (jobTypeId: string) =>
                    jobTypes.find(({ value }) => value === jobTypeId)?.label ??
                    '',
                )
                .join('・')}
            </Text>
          ) : null}
        </Box>
      </MediaQuery>
    </Box>
  );
};

export default SearchForm;
