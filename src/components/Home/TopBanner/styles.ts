import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  topBannerContainer: {
    height: 828,
    position: 'relative',
    display: 'flex',
    alignItems: 'flex-end',
    marginBottom: 100,
    backgroundColor: '#191C21',
    img: {
      objectFit: 'cover',
      objectPosition: 'top',
    },
    [theme.fn.smallerThan('sm')]: {
      minHeight: 608,
      height: 'unset',
      marginBottom: 103,
      img: {
        height: 415,
      },
    },
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    [theme.fn.smallerThan('sm')]: {
      height: 336,
    },
  },
  topBannerContent: {
    zIndex: 1,
    width: '100%',
    position: 'relative',
    textAlign: 'center',
    padding: '0 16px',
    maxWidth: 958,
    margin: '0 auto -84px',
    [theme.fn.smallerThan('sm')]: {
      marginTop: 176,
    },
  },
  jobSearchCard: {
    overflow: 'unset',
    margin: 'auto',
    maxWidth: '844px',
    padding: '32px 32px 24px',
    [theme.fn.smallerThan('sm')]: {
      padding: '24px 16px',
    },
  },
  chatButton: {
    marginTop: 12,
    width: 244,
    backgroundColor: theme.colors.primary,
    [theme.fn.smallerThan('sm')]: {
      marginBottom: '8px',
    },
  },
  socialText: {
    position: 'relative',
    margin: '16px',
    span: {
      position: 'relative',
      zIndex: 1,
      backgroundColor: 'white',
      padding: '0 8px',
    },
    '&:after': {
      zIndex: 0,
      content: '""',
      display: 'block',
      position: 'absolute',
      width: '100%',
      height: 1,
      top: '50%',
      backgroundColor: theme.colors['gray-400'],
    },
    [theme.fn.smallerThan('sm')]: {
      '&:after': {
        top: -6,
        zIndex: 1,
      },
      margin: '16px auto',
      span: {
        padding: 0,
      },
    },
  },
  socialLink: {
    'a, img': {
      width: 32,
      height: 32,
    },
  },
  searchFormWrapper: {
    '.mantine-Button-root': {
      flex: '0 0 148px',
    },
    '.mantine-Stack-root': {
      flex: '1 0 180px',
    },
    '.mantine-Select-root': {
      flex: '0 0 182px',
    },
    '.mantine-TextInput-root': {
      flex: '1 1 auto',
    },
    [theme.fn.smallerThan('sm')]: {
      flexDirection: 'column',
      gap: 8,
      '.mantine-Button-root, .mantine-Select-root, .mantine-TextInput-root, .mantine-Stack-root':
        {
          flex: '1 1 auto',
        },
    },
  },
}));

export default useStyles;
