import IconChat from '@icons/icon-chat-prompt.svg';
import IconNew from '@icons/icon-new.svg';
import {
  Box,
  Button,
  Card,
  Flex,
  Overlay,
  Text,
  ThemeIcon,
  Title,
} from '@mantine/core';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { SOCIAL_CONTACTS } from 'utils/constants';

import SearchForm from './SearchForm';
import useStyles from './styles';

const TopBanner = () => {
  const { classes } = useStyles();
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        delayChildren: 0.25,
        staggerChildren: 0.35,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 50 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        bounce: 0.5,
        duration: 0.5,
      },
    },
  };

  const handleSearchJobsViaChat = () => {
    setIsRedirecting(true);
    router.push('/my-page/search-job-chat');
  };

  return (
    <Box
      className={classes.topBannerContainer}
      component={motion.div}
      initial="hidden"
      variants={container}
      viewport={{ once: true, amount: 0.2 }}
      whileInView="show"
    >
      <Box className={classes.bannerImage}>
        <Overlay
          gradient="linear-gradient(180deg, rgba(255, 255, 255, 0.00) 29.85%, rgba(17, 24, 39, 0.58) 54.67%, rgba(17, 24, 39, 0.90) 76.68%)"
          sx={{ mixBlendMode: 'multiply' }}
          zIndex={1}
        />
        <Image
          alt="Home banner"
          fill
          priority
          quality={100}
          src="/images/home-banner.png"
        />
      </Box>
      <Box className={classes.topBannerContent}>
        <motion.div variants={item}>
          <Title
            color="white"
            fz={{ base: 32, sm: 56 }}
            lh={{ base: '46px', sm: '56px' }}
            mb={{ base: 16, sm: 24 }}
            px={{ base: 16, sm: 0 }}
            weight={900}
          >
            全国のプラント案件が探せる
          </Title>
        </motion.div>
        <motion.div variants={item}>
          <Text
            color="white"
            fz={{ base: 14, sm: 22 }}
            lh={{ base: '20px', sm: '30px' }}
            mb={{ base: 32, sm: 40 }}
            weight={700}
          >
            プラントに特化しているからこそ見つかる。
            <br />
            全国の工事案件・職人を見つけて繋がれるサービス。
          </Text>
        </motion.div>
        <Card
          className={classes.jobSearchCard}
          component={motion.div}
          shadow="md"
          variants={item}
        >
          <SearchForm />
          {/* 
          Chat Assistant Guide 
          (Ask for AI section) 
          */}
          {/* V03301-206. 1 Add new Icon */}
          <Flex
            align="center"
            gap={3}
            justify="center"
            mt={24}
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                borderTop: '1px solid #D2D5DA',
                paddingTop: 4,
              },
            })}
          >
            <ThemeIcon size={24}>
              <IconNew />
            </ThemeIcon>
            <Text color="primary" lh="16px" size={12} weight={500}>
              あなたにピッタリの案件が見つかります
            </Text>
          </Flex>
          <Text
            color="black"
            lh="20px"
            size={14}
            sx={(_) => ({
              whiteSpace: 'pre-line',
              marginTop: 8,
            })}
            weight={400}
          >
            ご希望のエリアや職種を、チャットでお話しいただくだけで、
            最適な案件をご案内します。
            <br />
            <br />
            お気軽にお試しください。
          </Text>
          <Button
            className={classes.chatButton}
            disabled={isRedirecting}
            leftIcon={<IconChat color="white" />}
            loading={isRedirecting}
            mah={'32px'}
            onClick={handleSearchJobsViaChat}
            size="sm"
          >
            チャットで案件を探す
          </Button>
          {/*  */}
          <Text
            className={classes.socialText}
            color="gray-500"
            lh="24px"
            size={16}
            weight={500}
          >
            <span>SNSアカウントでも情報発信しています。</span>
          </Text>
          <Flex
            align="center"
            className={classes.socialLink}
            gap={24}
            justify="center"
          >
            {SOCIAL_CONTACTS.map((nav) => (
              <Link
                href={nav.href}
                key={nav.href}
                rel="noreferrer"
                target="_blank"
              >
                {nav.icon}
              </Link>
            ))}
          </Flex>
        </Card>
      </Box>
    </Box>
  );
};

export default TopBanner;
