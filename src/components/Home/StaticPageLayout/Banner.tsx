import type { TextProps } from '@mantine/core';
import { Box, Overlay, Text } from '@mantine/core';
import Breadcrumb from 'components/Breadcrumb';
import React from 'react';

import useStyles from './styles';

interface BannerProps {
  title: string;
  subtitle: string;
  titleProps?: TextProps;
}

const Banner = ({ title, subtitle, titleProps }: BannerProps) => {
  const { classes } = useStyles();
  return (
    <Box className={classes.topBannerContainer}>
      <Box className={classes.backgroundCircle1} />
      <Box className={classes.backgroundCircle2} />
      <Box className={classes.backgroundCircle3} />
      <Overlay
        gradient="linear-gradient(180deg, rgba(255, 255, 255, 0.0) 88%, rgba(17, 24, 39, 0.25) 99%)"
        sx={{ mixBlendMode: 'multiply' }}
        zIndex={1}
      />
      <Breadcrumb />
      <Box className={classes.title}>
        <Text
          align="center"
          color="#fff"
          fw={500}
          fz={{ base: 12, sm: 14 }}
          lh={{ base: '16px', sm: '20px' }}
        >
          {subtitle.toUpperCase()}
        </Text>
        <Text
          align="center"
          color="#fff"
          fw={700}
          fz={{ base: 28, sm: 36 }}
          lh={{ base: '40px', sm: '52px' }}
          {...titleProps}
        >
          {title}
        </Text>
      </Box>
    </Box>
  );
};

export default Banner;
