import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  topBannerContainer: {
    position: 'relative',
    display: 'flex',
    alignItems: 'flex-start',
    overflow: 'hidden',
    flexDirection: 'column',
    backgroundColor: theme.colors.primary,
    '.mantine-Breadcrumbs-root': {
      zIndex: 2,
      '> *': {
        color: theme.white,
      },
      g: {
        stroke: theme.white,
      },
    },
  },
  backgroundCircle1: {
    width: 240,
    height: 240,
    borderRadius: '50%',
    position: 'absolute',
    top: -125,
    left: -86,
    backgroundColor: '#DD6B53',
    zIndex: 1,
    [theme.fn.smallerThan('sm')]: {
      width: 192,
      height: 192,
      top: -107,
    },
  },
  backgroundCircle2: {
    width: 148,
    height: 148,
    borderRadius: '50%',
    position: 'absolute',
    top: 64,
    left: -81,
    backgroundColor: '#DC583C',
    [theme.fn.smallerThan('sm')]: {
      width: 118,
      height: 118,
      top: 36,
      left: -82,
    },
  },
  backgroundCircle3: {
    width: 280,
    height: 280,
    borderRadius: '50%',
    position: 'absolute',
    bottom: -130,
    right: -140,
    backgroundColor: '#DB6147',
    [theme.fn.smallerThan('sm')]: {
      width: 224,
      height: 224,
      right: -132,
      bottom: -94,
    },
  },
  title: {
    alignSelf: 'center',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    maxWidth: 620,
    paddingBottom: 96,
    gap: 8,
    zIndex: 10,
    textShadow: '0px 4px 8px rgba(0, 0, 0, 0.20)',
    [theme.fn.smallerThan('sm')]: {
      maxWidth: 'unset',
      paddingLeft: 16,
      marginTop: 8,
      paddingRight: 16,
      paddingBottom: 56,
      gap: 4,
    },
  },
  content: {
    maxWidth: 984,
    zIndex: 2,
    margin: '-56px auto 0',
    backgroundColor: theme.white,
    position: 'relative',
    padding: '56px 100px',
    borderRadius: '32px 32px 0px 0px',
    [theme.fn.smallerThan('sm')]: {
      padding: '32px 16px 44px',
      borderRadius: '16px 16px 0px 0px',
      margin: '-32px 16px 0',
    },
  },
}));

export default useStyles;
