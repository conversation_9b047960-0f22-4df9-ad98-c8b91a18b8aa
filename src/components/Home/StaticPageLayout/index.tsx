import type { TextProps } from '@mantine/core';
import { Box } from '@mantine/core';
import React from 'react';

import Banner from './Banner';
import useStyles from './styles';

interface StaticPageLayoutProps {
  children: React.ReactElement;
  title: string;
  subtitle: string;
  titleProps?: TextProps;
}

const StaticPageLayout = ({
  children,
  title,
  subtitle,
  titleProps,
}: StaticPageLayoutProps) => {
  const { classes } = useStyles();
  return (
    <Box bg={'#fff'} h={'100%'}>
      <Banner subtitle={subtitle} title={title} titleProps={titleProps} />
      <Box className={classes.content}>{children}</Box>
    </Box>
  );
};

export default StaticPageLayout;
