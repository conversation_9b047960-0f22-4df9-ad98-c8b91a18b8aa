import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  introductionContainer: {
    maxWidth: 1184,
    margin: '0 auto',
    padding: '56px 16px 102px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 58,
    [theme.fn.smallerThan('md')]: {
      flexDirection: 'column',
    },
    [theme.fn.smallerThan('sm')]: {
      padding: '40px 16px 32px',
    },
  },
  introductionBgWrapper: {
    position: 'relative',
    maxWidth: 584,
    width: '100%',
    flexShrink: 0,
    padding: '0 28px 0 8px',
  },
  bgItem: {
    color: theme.colors.black,
    gap: 16,
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'white',
    position: 'absolute',
    height: 80,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: '24px',
    padding: '0 20px',
    boxShadow: theme.shadows.lg,
    borderRadius: '10px',
    [theme.fn.smallerThan('sm')]: {
      height: 42,
      fontSize: 9,
      padding: '0 11px',
      gap: 8,
      svg: {
        width: 24,
        height: 24,
      },
    },
  },
  introductionContentWrapper: {
    maxWidth: 484,
  },
}));

export default useStyles;
