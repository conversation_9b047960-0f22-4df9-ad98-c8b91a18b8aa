import IconHelmet from '@icons/icon-helmet.svg';
import IconStar from '@icons/icon-star.svg';
import { AspectRatio, Box, Flex, Text, Title } from '@mantine/core';
import { motion } from 'framer-motion';
import Image from 'next/image';
import React from 'react';

import useStyles from './styles';

const Introduction = () => {
  const { classes } = useStyles();

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.25,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, scale: 0.5, y: 0 },
    show: {
      opacity: 1,
      scale: 1,
      y: '50%',
      transition: {
        bounce: 0.5,
        duration: 0.5,
      },
    },
  };

  const text = {
    hidden: { opacity: 0, y: 50 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        bounce: 0.5,
        duration: 0.3,
      },
    },
  };

  return (
    <Box
      className={classes.introductionContainer}
      component={motion.div}
      initial="hidden"
      variants={container}
      viewport={{ once: true, amount: 0.2 }}
      whileInView="show"
    >
      <Box className={classes.introductionBgWrapper}>
        <AspectRatio
          ratio={584 / 600}
          sx={{ borderRadius: 24, overflow: 'hidden' }}
          w="100%"
        >
          <Image
            alt="Introduction background"
            fill
            quality={100}
            sizes="(max-width: 768px) 1200px, 75vw"
            src="/images/introduction-bg.jpeg"
            style={{
              objectFit: 'cover',
              objectPosition: '90% 50%',
              transform: 'scaleX(-1)',
            }}
          />
        </AspectRatio>
        <Box
          bottom={{ base: 59, sm: 112 }}
          className={classes.bgItem}
          component={motion.div}
          right={{ base: 4, sm: -44 }}
          variants={item}
        >
          <IconHelmet />
          企業と直接繋がります
        </Box>
        <Box
          bottom={0}
          className={classes.bgItem}
          component={motion.div}
          right={{ base: 48, sm: 39 }}
          variants={item}
        >
          <IconStar />
          自分のスキルに合う案件が見つかります
        </Box>
      </Box>
      <Flex
        align={{ base: 'center', md: 'flex-start' }}
        className={classes.introductionContentWrapper}
        direction="column"
      >
        <motion.div variants={text}>
          <Text
            color="primary"
            fw={500}
            fz={{ base: '12px', sm: '14px' }}
            lh={{ base: '16px', sm: '20px' }}
            mb={{ base: 4, sm: 8 }}
          >
            WHAT IS TEQNOWA
          </Text>
        </motion.div>
        <motion.div variants={text}>
          <Title
            color="black"
            fw={700}
            fz={{ base: '32px', sm: '36px' }}
            lh={{ base: '46px', sm: '52px' }}
            mb={{ base: 24, sm: 32 }}
          >
            テクノワとは
          </Title>
        </motion.div>
        <motion.div variants={text}>
          <Text
            color="black"
            fw={400}
            fz={{ base: '14px', sm: '16px' }}
            lh={{ base: '20px', sm: '24px' }}
          >
            {`建設職人マッチングサービスのテクノワの特徴は、
          職人と職人、企業と職人をマッチさせることで人材不足を解消すること。
          人材のマッチングだけでなく、現在の建設業の構造や仕組みそのもを変えていけるサービスを目指しています。`}
          </Text>
        </motion.div>
      </Flex>
    </Box>
  );
};

export default Introduction;
