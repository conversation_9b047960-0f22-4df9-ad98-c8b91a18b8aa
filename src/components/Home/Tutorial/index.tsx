import { Box, Flex, Text } from '@mantine/core';
import { motion } from 'framer-motion';
import Image from 'next/image';
import React from 'react';

import useStyles from './styles';

const contents = [
  {
    step: 'Step 1',
    img: '/images/tutorial-1.png',
    title: '自分に合った仕事を見つけよう',
    description: '案件一覧から条件を絞って検索することができます',
  },
  {
    step: 'Step 2',
    img: '/images/tutorial-2.png',
    title: '案件についてチャットで相談する',
    description: '案件一覧か案件詳細からチャット開始することができます',
  },
  {
    step: 'Step 3',
    img: '/images/tutorial-3.png',
    title: '案件に応募して契約する',
    description: '案件一覧か案件詳細から案件に応募することができます',
  },
];

const Tutorial = () => {
  const { classes } = useStyles();

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      duration: 1,
      transition: {
        staggerChildren: 0.25,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 100 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        bounce: 0.5,
        duration: 0.8,
      },
    },
  };

  const text = {
    hidden: { opacity: 0, y: 50 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        bounce: 0.5,
        duration: 0.3,
      },
    },
  };

  return (
    <Box
      component={motion.div}
      initial="hidden"
      p={{ base: '40px 0 48px', sm: '48px 0 56px' }}
      variants={container}
      viewport={{ once: true, amount: 0.2 }}
      whileInView="show"
    >
      <motion.div variants={text}>
        <Text color="primary" fw={500} fz="14px" lh="20px" mb={8} ta="center">
          HOW TO USE
        </Text>
      </motion.div>
      <motion.div variants={text}>
        <Text
          color="black"
          fw={700}
          fz={{ base: '32px', sm: '36px' }}
          lh={{ base: '46px', sm: '52px' }}
          mb={{ base: 56, sm: 72 }}
          ta="center"
        >
          テクノワの使い方
        </Text>
      </motion.div>
      <Box
        className={classes.tutorialStep}
        component={motion.div}
        variants={item}
      >
        <Box className={classes.indicatorWrapper}>
          <Box className={classes.indicator}>
            <span />
            <span />
            <span />
          </Box>
        </Box>
        {contents.map((content) => {
          return (
            <Flex
              align="center"
              className={classes.itemWrapper}
              direction="column"
              key={content.step}
            >
              <Text
                color="black"
                fw={700}
                fz="16px"
                lh="24px"
                mb={{ base: 16, sm: 24 }}
              >
                {content.step}
              </Text>
              <Image
                alt={content.title}
                height={148}
                src={content.img}
                width={148}
              />
              <Text
                color="primary"
                fw={700}
                fz={{ base: '16px', sm: '18px' }}
                mb={{ base: 8, sm: 16 }}
                mt={{ base: 16, sm: 24 }}
              >
                {content.title}
              </Text>
              <Text
                color="black"
                fw={400}
                fz={{ base: '14px', sm: '16px' }}
                lh={{ base: '20px', sm: '24px' }}
                maw={294}
                ta="center"
              >
                {content.description}
              </Text>
            </Flex>
          );
        })}
      </Box>
    </Box>
  );
};

export default Tutorial;
