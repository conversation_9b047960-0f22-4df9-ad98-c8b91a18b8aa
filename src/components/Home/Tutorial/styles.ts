import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  indicatorWrapper: {
    position: 'absolute',
    width: '100%',
    top: -34,
    padding: '0 15%',
    [theme.fn.smallerThan('sm')]: {
      display: 'none',
    },
  },
  indicator: {
    display: 'flex',
    maxWidth: '831px',
    margin: '0 auto',
    justifyContent: 'space-between',
    position: 'relative',
    '&:after': {
      content: '""',
      position: 'absolute',
      top: '48%',
      display: 'block',
      width: 'calc(100% - 16px)',
      left: 10,
      borderBottom: `2px dashed ${theme.colors['neutral-100']}`,
    },
    [theme.fn.smallerThan('md')]: {
      maxWidth: '100%',
    },
    span: {
      width: 20,
      height: 20,
      display: 'block',
      zIndex: 1,
      borderRadius: '20px',
      background:
        'radial-gradient(circle, rgba(210,58,25,1) 30%, rgba(248,230,210,1)  30%, rgba(248,230,210,1) 55%, rgba(248,230,210,0.5) 55%)',
    },
  },
  tutorialStep: {
    position: 'relative',
    gap: 16,
    display: 'flex',
    justifyContent: 'center',
    [theme.fn.smallerThan('sm')]: {
      flexDirection: 'column',
      alignItems: 'center',
      gap: 64,
    },
  },
  itemWrapper: {
    position: 'relative',
    width: '100%',
    flexShrink: 0,
    maxWidth: 389,
    flex: '1 1 148px',
  },
}));

export default useStyles;
