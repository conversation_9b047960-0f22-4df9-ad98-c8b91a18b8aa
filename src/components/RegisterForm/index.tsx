import { yupResolver } from '@hookform/resolvers/yup';
import EmailIcon from '@icons/icon-email.svg';
import MailIcon from '@icons/icon-mail.svg';
import { Box, Button, Card, Checkbox, Flex, Text, Title } from '@mantine/core';
import { TextField } from 'components/Form';
import InfoPopup from 'components/InfoPopup';
import { useMutate } from 'hooks';
import authQuery from 'models/auth';
import Link from 'next/link';
import React, { useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';

import type { RegisterFormValues } from './schema';
import schema from './schema';

const RegisterForm = () => {
  const {
    control,
    handleSubmit,
    getValues,
    formState: { isValid },
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });
  const [isConditionAgreed, setConditionAgreement] = useState(false);

  const {
    mutateAsync: onWorkerRegister,
    isSuccess,
    isLoading,
  } = useMutate<RegisterFormValues>(authQuery.register);

  const onSubmit: SubmitHandler<RegisterFormValues> = (values) => {
    onWorkerRegister(values);
  };

  if (isSuccess) {
    return (
      <Card
        maw={424}
        px={{ base: 15, sm: 31 }}
        py={{ base: 16, sm: 32 }}
        shadow="sm"
        w={'100%'}
      >
        <InfoPopup
          description={
            <Text
              align="center"
              fw={400}
              fz={16}
              lh="24px"
              mb={{ base: 24, sm: 32 }}
            >
              アカウント認証するためのメールを以下のメールアドレスに送信いたしました。メールの内容に従って、パスワードを設定してください。
              {'\n'}
              <Text fw={700} inherit span>
                {getValues().email}
              </Text>
            </Text>
          }
          href="/"
          icon={<MailIcon />}
          isLink
          submitButtonLabel="ホームページに戻る"
          title="アカウント認証メール送信完了"
        />
      </Card>
    );
  }

  return (
    <Card
      maw={424}
      px={{ base: 15, sm: 31 }}
      py={{ base: 32, sm: 40 }}
      shadow="sm"
      w={'100%'}
    >
      <Box component="form" onSubmit={handleSubmit(onSubmit)} w="100%">
        <Flex
          align={'center'}
          direction={'column'}
          gap={{ base: 24, sm: 32 }}
          w="100%"
        >
          <Title
            color="black"
            fw={700}
            fz={{ base: 24, sm: 32 }}
            lh={{ base: '34px', sm: '46px' }}
            order={3}
          >
            新規登録
          </Title>
          <Box w="100%">
            <TextField
              control={control}
              icon={<EmailIcon color="#9CA3AF" />}
              label="メールアドレス"
              name="email"
              placeholder="メールアドレスを入力してください"
              sx={(theme) => ({
                [theme.fn.smallerThan('sm')]: {
                  'input::placeholder': {
                    fontSize: 14,
                  },
                },
              })}
            />
            <Checkbox
              checked={isConditionAgreed}
              label={
                <Text color="black" lh={{ base: '20px', sm: '24px' }}>
                  <Text
                    color="info-500"
                    component={Link}
                    href="/terms"
                    inherit
                    target="_blank"
                  >
                    利用規約
                  </Text>
                  と
                  <Text
                    color="info-500"
                    component={Link}
                    href="/policy"
                    inherit
                    target="_blank"
                  >
                    プライバシーポリシー
                  </Text>
                  に同意する
                </Text>
              }
              mt={24}
              name="isConditionAgreed"
              onChange={() => setConditionAgreement((prev) => !prev)}
            />
          </Box>
          <Button
            disabled={!isConditionAgreed || !isValid}
            fullWidth
            loading={isLoading}
            type="submit"
          >
            登録する
          </Button>
          <Text lh={{ base: '20px', sm: '24px' }}>
            既に会員登録をされた方は、{'\n'}
            <Text color="info-500" component={Link} href="/login" inherit>
              こちら
            </Text>
            よりログインください
          </Text>
        </Flex>
      </Box>
    </Card>
  );
};

export default RegisterForm;
