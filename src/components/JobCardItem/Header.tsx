import type { BoxProps, TextProps } from '@mantine/core';
import { Box, Text } from '@mantine/core';
import type { IJobPostItem } from 'models/job/type';
import React from 'react';

const Header = ({
  textProps,
  wrapperProps,
  extra,
  ...item
}: {
  textProps?: TextProps[];
  wrapperProps?: BoxProps;
  extra?: React.ReactNode;
} & Partial<Pick<IJobPostItem, 'companyId' | 'title'>>) => {
  return (
    <Box {...wrapperProps}>
      <Text
        color="gray-700"
        fw={400}
        fz={{ base: 12, sm: 14 }}
        lh={{ base: '16px', sm: '20px' }}
        lineClamp={1}
        maw={'92%'}
        mb={4}
        truncate
        {...textProps?.[0]}
      >
        {item.companyId?.name}
      </Text>
      <Text
        color="black"
        fw={700}
        fz={{ base: 14, sm: 18 }}
        lh={{ base: '20px', sm: '26px' }}
        lineClamp={2}
        maw={'100%'}
        truncate
        {...textProps?.[1]}
      >
        {item.title}
      </Text>
      {!!extra && React.isValidElement(extra) && extra}
    </Box>
  );
};

export default Header;
