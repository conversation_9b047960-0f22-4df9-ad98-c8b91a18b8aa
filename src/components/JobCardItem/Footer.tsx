import IconDeadline from '@icons/icon-deadline.svg';
import type { ButtonProps, FlexProps, MantineTheme } from '@mantine/core';
import { Box, Button, Divider, Flex, Text } from '@mantine/core';
import type { IJobPostItem } from 'models/job/type';
import React from 'react';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

export interface FooterProps
  extends Pick<
    IJobPostItem,
    | 'isPrivateSalary'
    | 'minSalary'
    | 'maxSalary'
    | 'publicationEndDate'
    | 'status'
  > {
  buttons?: (ButtonProps & React.HTMLAttributes<HTMLButtonElement>)[];
  containerStyle?: FlexProps;
}

const Footer = ({ buttons, containerStyle, ...item }: FooterProps) => {
  return (
    <>
      <Divider color="gray-300" m={0} />
      <Flex
        align="flex-start"
        gap={12}
        justify={'space-between'}
        sx={(theme: MantineTheme) => ({
          [theme.fn.smallerThan('sm')]: {
            flexDirection: 'column',
            alignItems: 'stretch',
          },
        })}
        wrap="wrap"
        {...containerStyle}
      >
        <Box sx={{ flexShrink: 0 }}>
          <Text color="primary" fw={700} fz={'md'}>
            単価(税抜):{' '}
            {item.isPrivateSalary
              ? 'メッセージにて見積もり依頼'
              : helpers.renderSalary(item.minSalary || 0, item.maxSalary)}
          </Text>
          {item.publicationEndDate && (
            <Flex align={'center'}>
              <IconDeadline
                color="#7B8492"
                height={'16px'}
                mr={'4px'}
                width={'16px'}
              />
              <Text
                color="gray-600"
                display={'inline-block'}
                fw={400}
                fz={'sm'}
              >
                {item.status === 'FINISHED'
                  ? '募集終了しました'
                  : `${dayjs(item.publicationEndDate).format(
                      'YYYY年MM月DD日',
                    )}`}
              </Text>
            </Flex>
          )}
        </Box>
        {buttons?.length && (
          <Flex
            gap={8}
            sx={(theme: MantineTheme) => ({
              [theme.fn.smallerThan('sm')]: {
                '> *': {
                  flex: 1,
                },
              },
            })}
          >
            {buttons.map((btn, index) => (
              <Button
                fw={500}
                fz={'sm'}
                key={index}
                onContextMenu={(e) => {
                  e.preventDefault();
                }}
                {...btn}
              />
            ))}
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default Footer;
