import type { StackProps, TextProps, ThemeIconProps } from '@mantine/core';
import { Flex, Stack, Text, ThemeIcon } from '@mantine/core';
import React from 'react';

const Content = ({
  rows,
  stackProps,
}: {
  stackProps?: StackProps;
  rows?: {
    children?: React.ReactElement;
    icon?: React.ReactElement;
    iconProps?: ThemeIconProps;
    text?: string;
    textProps?: TextProps;
  }[];
}) => {
  return (
    <Stack spacing={'loose'} {...stackProps}>
      {rows?.length &&
        rows.map((row, index) =>
          React.isValidElement(row.children) ? (
            React.cloneElement(row.children, { key: index })
          ) : (
            <Flex align={'center'} key={index}>
              <ThemeIcon color="primary" mr={4} size={16} {...row.iconProps}>
                {row.icon}
              </ThemeIcon>
              <Text
                color="gray-700"
                fw={400}
                fz={{ base: 12, sm: 14 }}
                lh={{ base: '16px', sm: '20px' }}
                {...row.textProps}
              >
                {row.text}
              </Text>
            </Flex>
          ),
        )}
    </Stack>
  );
};

export default Content;
