import { AppShell } from '@mantine/core';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import React, { useEffect, useRef } from 'react';
import ReactGA from 'react-ga4';

import Footer from './Footer';
import Header from './Header';
import useStyles from './styles';

const Drawer = dynamic(() => import('./Drawer'), { ssr: false });

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const layoutRef = useRef(null);
  const { pathname, events } = useRouter();
  const { classes, cx } = useStyles();

  useEffect(() => {
    const handleRouteChange = (url: unknown) => {
      // REACTGA
      // Send pageview with a custom path
      ReactGA.send({ hitType: 'pageview', page: url });
    };

    events.on('routeChangeComplete', handleRouteChange);
    return () => {
      events.off('routeChangeComplete', handleRouteChange);
    };
  }, [events]);

  const whiteBg = ['/'].includes(pathname);
  const noFooterPage = [
    '/my-page/jobs/[id]/messages',
    '/contact-admin',
    '/my-page/search-job-chat',
    '/my-page/search-job-chat/[id]',
  ].includes(pathname);

  return (
    <AppShell
      classNames={{
        root: cx(classes.layoutRoot, {
          [classes.bgWhite]: whiteBg,
          [classes.noFooter]: noFooterPage,
        }),
        body: classes.layoutBody,
        main: classes.layoutMain,
      }}
      fixed={false}
      footer={noFooterPage ? undefined : <Footer />}
      header={<Header />}
      padding={0}
      ref={layoutRef}
    >
      {children}
      <Drawer />
    </AppShell>
  );
};

export default Layout;
