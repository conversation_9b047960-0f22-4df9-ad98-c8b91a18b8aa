import IconJob from '@icons/icon-job.svg';
import IconLogout from '@icons/icon-logout.svg';
import IconUser from '@icons/icon-user.svg';
import {
  Box,
  Button,
  Divider,
  Drawer,
  Flex,
  NavLink,
  Text,
} from '@mantine/core';
import { useGlobalState, useLogout, useUser } from 'hooks';
import useFirebaseUser from 'hooks/useFirebaseUser';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';

import useStyles from './styles';

const LayoutDrawer = () => {
  const { pathname } = useRouter();
  const { data: userData } = useUser();
  const { confirmLogout } = useLogout();
  const { classes } = useStyles();

  const { openedDrawer, setOpenDrawer } = useGlobalState();

  const { data: firebaseUser } = useFirebaseUser();

  const isUnread = (() => {
    if (!userData || !userData.chatInfo?.roomId || !firebaseUser) {
      return false;
    }

    const unreadMapping = firebaseUser.numberOfUnreadMessages || {};
    return (unreadMapping[userData.chatInfo.roomId] || 0) > 0;
  })();

  const navLinkClassNames = {
    root: classes.navLinkRoot,
    label: classes.navLinkLabel,
    icon: classes.navLinkIcon,
  };

  const renderContent = () => {
    if (userData?.isCompletedProfile === false) {
      return (
        <NavLink
          className="logout-btn"
          classNames={navLinkClassNames}
          icon={<IconLogout />}
          label="ログアウト"
          onClick={() => {
            setOpenDrawer(false);
            confirmLogout();
          }}
          unstyled
        />
      );
    }
    return (
      <>
        {userData && (
          <>
            <Box px={16} py={12}>
              <Text color="gray-700" fw={400} lh="32px" lineClamp={1}>
                <Image
                  alt="user-avatar"
                  height={32}
                  src={
                    userData.avatar?.originUrl || '/images/default-avatar.png'
                  }
                  style={{ marginRight: 8 }}
                  width={32}
                />
                {userData.fullName}
              </Text>
            </Box>
            <Divider color="gray-300" my={16} sx={{ borderRadius: '1px' }} />
          </>
        )}
        <NavLink
          classNames={navLinkClassNames}
          component={Link}
          href="/recruiter"
          label="発注企業様向け"
          onClick={() => setOpenDrawer(false)}
          unstyled
        />
        <NavLink
          active={pathname === '/contact-admin'}
          classNames={navLinkClassNames}
          component={Link}
          href="/contact-admin"
          label="テクノワに相談"
          onClick={() => setOpenDrawer(false)}
          rightSection={
            isUnread ? (
              <Text
                span
                sx={(theme) => ({
                  position: 'absolute',
                  right: 0,
                  top: '50%',
                  zIndex: 10,
                  transform: 'translateY(-50%)',
                  backgroundColor: theme.colors.primary,
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                })}
              />
            ) : null
          }
          style={{
            position: 'relative',
          }}
          unstyled
        />
        <NavLink
          active={pathname === '/jobs'}
          classNames={navLinkClassNames}
          component={Link}
          href="/jobs"
          label="案件を探す"
          onClick={() => setOpenDrawer(false)}
          unstyled
        />
        <Divider color="gray-300" my={16} sx={{ borderRadius: '1px' }} />
        {!userData ? (
          <Flex direction="column" gap={16}>
            <Link href="/login" onClick={() => setOpenDrawer(false)}>
              <Button fullWidth variant="outline">
                ログイン
              </Button>
            </Link>
            <Link href="/register" onClick={() => setOpenDrawer(false)}>
              <Button fullWidth>アカウント登録</Button>
            </Link>
          </Flex>
        ) : (
          <>
            <NavLink
              active={pathname === '/my-page/profile'}
              classNames={navLinkClassNames}
              component={Link}
              href="/my-page/profile"
              icon={<IconUser />}
              label="プロフィール"
              onClick={() => setOpenDrawer(false)}
              unstyled
            />
            <NavLink
              active={pathname === '/my-page/jobs'}
              classNames={navLinkClassNames}
              component={Link}
              href="/my-page/jobs"
              icon={<IconJob />}
              label="案件一覧"
              onClick={() => setOpenDrawer(false)}
              unstyled
            />
            <NavLink
              className="logout-btn"
              classNames={navLinkClassNames}
              icon={<IconLogout />}
              label="ログアウト"
              onClick={() => {
                setOpenDrawer(false);
                confirmLogout();
              }}
              unstyled
            />
          </>
        )}
      </>
    );
  };

  return (
    <Drawer
      classNames={{
        overlay: classes.drawerOverlay,
        inner: classes.drawerInner,
        body: classes.drawerBody,
      }}
      onClose={() => setOpenDrawer(false)}
      opened={openedDrawer}
      position="right"
      size={375}
      withCloseButton={false}
      withinPortal={false}
    >
      {renderContent()}
    </Drawer>
  );
};

export default LayoutDrawer;
