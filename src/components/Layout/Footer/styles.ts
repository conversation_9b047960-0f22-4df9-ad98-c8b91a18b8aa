import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  footerWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '16px 32px',
    backgroundColor: 'var(--layout-bg)',
    borderTop: `1px solid ${theme.colors['gray-300']}`,
    [theme.fn.smallerThan('sm')]: {
      height: 'unset',
      maxHeight: 'unset',
      minHeight: 'var(--mantine-footer-height)',
    },
  },
  navigationLink: {
    marginBottom: 16,
    [theme.fn.smallerThan('sm')]: {
      gap: '8px 24px',
      maxWidth: 235,
      a: {
        fontSize: 14,
        lineHeight: '20px',
      },
    },
  },
  socialLink: {
    marginBottom: 16,
    'a, img': {
      width: 40,
      height: 40,
    },
    [theme.fn.smallerThan('sm')]: {
      marginBottom: 12,
    },
  },
}));

export default useStyles;
