import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  layoutRoot: {
    '--layout-bg': theme.colors['gray-200'],
    [theme.fn.smallerThan('sm')]: {
      '--mantine-header-height': '64px',
      '--mantine-footer-height': '173px',
    },
  },
  layoutBody: {
    paddingTop: 'var(--mantine-header-height)',
    minHeight: 'calc(var(--vh, 1vh) * 100 - var(--mantine-footer-height))',
    backgroundColor: 'var(--layout-bg)',
  },
  layoutMain: {
    width: '100%',
  },
  bgWhite: {
    '--layout-bg': theme.white,
  },
  noFooter: {
    '--mantine-footer-height': '0px',
    [theme.fn.smallerThan('sm')]: {
      '--mantine-footer-height': '0px',
    },
  },
}));

export default useStyles;
