import { ActionIcon } from '@mantine/core';
import { motion } from 'framer-motion';
import { useGlobalState } from 'hooks';
import React from 'react';

import useStyles from './styles';

const Path = (props: any) => (
  <motion.path
    fill="transparent"
    stroke="#4B5563"
    strokeLinecap="round"
    strokeWidth="2"
    {...props}
  />
);

const HamburgerIcon = () => {
  const { openedDrawer, setOpenDrawer } = useGlobalState();
  const { classes } = useStyles();

  return (
    <ActionIcon
      className={classes.hamburgerIcon}
      onClick={() => setOpenDrawer(!openedDrawer)}
      size="sm"
      variant="subtle"
    >
      <motion.svg
        animate={openedDrawer ? 'open' : 'closed'}
        height="14px"
        initial={false}
        viewBox="0 0 14 14"
        width="14px"
      >
        <Path
          variants={{
            closed: { d: 'M 4 1 L 13 1' },
            open: { d: 'M 1 13 L 13 1' },
          }}
        />
        <Path
          d="M 1 7 L 13 7"
          transition={{ duration: 0.1 }}
          variants={{
            closed: { opacity: 1 },
            open: { opacity: 0 },
          }}
        />
        <Path
          variants={{
            closed: { d: 'M 4 13 L 13 13' },
            open: { d: 'M 1 1 L 13 13' },
          }}
        />
      </motion.svg>
    </ActionIcon>
  );
};

export default HamburgerIcon;
