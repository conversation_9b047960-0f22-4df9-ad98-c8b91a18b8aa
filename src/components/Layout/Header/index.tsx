import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconArrowLeft from '@icons/icon-arrow-previous.svg';
import IconJob from '@icons/icon-job.svg';
import IconLogout from '@icons/icon-logout.svg';
import IconUser from '@icons/icon-user.svg';
import LogoHorizontal from '@icons/logo-horizontal.svg';
import { Box, Button, Divider, Flex, Header, Menu, Text } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { AnimatePresence, motion } from 'framer-motion';
import { useLogout, useUser } from 'hooks';
import useFirebaseUser from 'hooks/useFirebaseUser';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import HamburgerIcon from './Hamburger';
import useStyles from './styles';

const variants = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: {
    opacity: 0,
  },
};

// Mobile custom headers
const customHeaders: Record<string, string> = {
  '/contact-admin': 'テクノワに相談',
};

const LayoutHeader = () => {
  const { data: userData, fetchStatus, isRefetching } = useUser();
  const { confirmLogout } = useLogout();
  const { pathname, back } = useRouter();
  const { classes } = useStyles();
  const [trigger, setTrigger] = useState(true);
  const isMobile = useMediaQuery('(max-width: 48em');
  const { data: firebaseUser } = useFirebaseUser();

  const isUnread = (() => {
    if (!userData || !userData.chatInfo?.roomId || !firebaseUser) {
      return false;
    }

    const unreadMapping = firebaseUser.numberOfUnreadMessages || {};
    return (unreadMapping[userData.chatInfo.roomId] || 0) > 0;
  })();

  useEffect(() => {
    if (fetchStatus === 'idle') {
      setTrigger(false);
    }
    if (fetchStatus === 'fetching' && !isRefetching) {
      setTrigger(true);
    }
  }, [fetchStatus, isRefetching]);

  const simpleHeader = [
    '/register',
    '/login',
    '/set-password',
    '/forget-password',
  ].includes(pathname);

  const renderHeaderMenus = () => {
    if (simpleHeader || trigger) return <></>;
    if (userData?.isCompletedProfile === false) {
      return (
        <motion.div
          animate="animate"
          exit="exit"
          initial="initial"
          transition={{ duration: 0.5 }}
          variants={variants}
        >
          <Flex align="center" className={classes.navigationPC}>
            <Button onClick={confirmLogout} variant="outline">
              ログアウト
            </Button>
          </Flex>
          <Flex align="center" className={classes.navigationSP}>
            <HamburgerIcon />
          </Flex>
        </motion.div>
      );
    }
    return (
      <motion.div
        animate="animate"
        exit="exit"
        initial="initial"
        transition={{ duration: 0.5 }}
        variants={variants}
      >
        <Box className={classes.navigationPC}>
          <Text
            className={classes.navLink}
            color="primary"
            component={Link}
            href="/recruiter"
            size={16}
          >
            発注企業様向け
          </Text>
          <Text
            className={classes.navLink}
            color="primary"
            component={Link}
            data-active={pathname === '/contact-admin'}
            href="/contact-admin"
            size={16}
          >
            テクノワに相談
            {isUnread && (
              <Text
                span
                sx={(theme) => ({
                  position: 'absolute',
                  right: 0,
                  top: '50%',
                  zIndex: 10,
                  transform: 'translateX(100%) translateY(-100%)',
                  backgroundColor: theme.colors.primary,
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                })}
              />
            )}
          </Text>
          <Text
            className={classes.navLink}
            color="primary"
            component={Link}
            data-active={pathname === '/jobs'}
            href="/jobs"
            size={16}
          >
            案件を探す
          </Text>
          <Divider
            color="gray-400"
            h={24}
            orientation="vertical"
            size={1}
            sx={{ alignSelf: 'center' }}
          />
          {!userData ? (
            <Flex gap={16}>
              <Link href="/login">
                <Button variant="outline">ログイン</Button>
              </Link>
              <Link href="/register">
                <Button>アカウント登録</Button>
              </Link>
            </Flex>
          ) : (
            <Menu offset={8} shadow="md" width={180}>
              <Menu.Target>
                <Button className={classes.dropdownBtn} variant="subtle">
                  <Image
                    alt="user-avatar"
                    height={32}
                    src={
                      userData.avatar?.originUrl || '/images/default-avatar.png'
                    }
                    width={32}
                  />
                  {userData.fullName}
                  <IconArrowDown className="icon-arrow" />
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  component={Link}
                  href="/my-page/profile"
                  icon={<IconUser />}
                >
                  プロフィール
                </Menu.Item>
                <Menu.Item
                  component={Link}
                  href="/my-page/jobs"
                  icon={<IconJob />}
                >
                  案件一覧
                </Menu.Item>
                <Menu.Item
                  icon={<IconLogout />}
                  onClick={confirmLogout}
                  sx={(theme) => ({
                    color: theme.colors.primary,
                  })}
                >
                  ログアウト
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          )}
        </Box>
        <Flex align="center" className={classes.navigationSP} gap={14}>
          <HamburgerIcon />
        </Flex>
      </motion.div>
    );
  };

  const HeaderLogo = () => {
    if (userData?.isCompletedProfile === false) {
      return (
        <Box className={classes.logo}>
          <LogoHorizontal />
        </Box>
      );
    }

    return (
      <Link className={classes.logo} href="/">
        <LogoHorizontal />
      </Link>
    );
  };

  if (Object.keys(customHeaders).includes(pathname) && isMobile) {
    return (
      <Header
        className={classes.headerWrapper}
        fixed
        height={72}
        withBorder={false}
      >
        <Flex
          align={'center'}
          direction={'row'}
          justify={'center'}
          pos={'relative'}
          sx={{ flex: 1 }}
        >
          <Button
            color="gray-500"
            onClick={back}
            style={{ position: 'absolute', left: 0 }}
            variant="subtle"
          >
            <IconArrowLeft />
          </Button>
          <Text fw={700} fz={16} lh={1.5}>
            {customHeaders[pathname]}
          </Text>
        </Flex>
      </Header>
    );
  }

  return (
    <Header
      className={classes.headerWrapper}
      fixed
      height={72}
      withBorder={false}
    >
      <HeaderLogo />
      <AnimatePresence mode="wait">{renderHeaderMenus()}</AnimatePresence>
    </Header>
  );
};

export default LayoutHeader;
