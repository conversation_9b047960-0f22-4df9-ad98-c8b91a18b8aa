import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  headerWrapper: {
    display: 'flex',
    padding: '0 128px',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100vw',
    borderBottom: `1px solid ${theme.colors['gray-300']}`,
    [theme.fn.smallerThan('md')]: {
      padding: '0 16px',
    },
    [theme.fn.smallerThan('sm')]: {
      height: 'var(--mantine-header-height)',
    },
  },
  logo: {
    display: 'flex',
    [theme.fn.smallerThan('sm')]: {
      svg: {
        width: 120,
        height: 32,
      },
    },
  },
  hamburgerIcon: {
    width: 24,
    height: 24,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  },
  navLink: {
    height: '100%',
    lineHeight: '71px',
    position: 'relative',
    '&:after': {
      content: '""',
      position: 'absolute',
      width: '100%',
      height: 2,
      bottom: -0.5,
      left: 0,
      backgroundColor: 'currentColor',
      visibility: 'hidden',
      transform: 'scaleX(0)',
      transition: 'all 0.2s ease-in-out 0s',
      borderRadius: '2px',
    },
    '&[data-active=true]': {
      fontWeight: 'bold',
      '&:after': {
        visibility: 'visible',
        transform: 'scaleX(1)',
      },
    },
    '&:hover': {
      fontWeight: 'bold',
    },
  },
  navigationPC: {
    display: 'none',
    alignItems: 'center',
    gap: 8,
    height: '100%',
    [theme.fn.largerThan('sm')]: {
      display: 'flex',
      gap: 'clamp(8px, calc(7vw - 45.8px) , 24px)',
    },
    [theme.fn.largerThan('md')]: {
      gap: 'clamp(8px, calc(7.5vw - 66.5px) ,24px)',
    },
    [theme.fn.smallerThan('sm')]: {
      display: 'none',
    },
  },
  navigationSP: {
    [theme.fn.largerThan('sm')]: {
      display: 'none',
    },
  },
  dropdownBtn: {
    backgroundColor: theme.colors['gray-200'],
    color: theme.colors['gray-700'],
    padding: '6px 12px 6px 16px',
    img: {
      borderRadius: '50%',
    },
    '.mantine-Button-label': {
      gap: 8,
    },
    '&[data-expanded=true] .icon-arrow': {
      transform: 'rotate(180deg)',
    },
  },
}));

export default useStyles;
