import { REG_EXP, SPECIAL_EXAM_REQUIRED_JOB_TYPES } from 'utils/constants';
import dayjs from 'utils/dayjs';
import * as yup from 'yup';

export interface ContractedWorkerFormValues {
  // Section 1: Profile
  firstName: string;
  lastName: string;
  firstNameKata: string;
  lastNameKata: string;
  birthDate: Date;
  age: number;
  gender: string;
  address: string;
  phoneNumber: string;
  bloodType: string;
  employmentDate: Date;
  yearsOfExperience: number;
  jobTitle: string;

  // Section 2: Other Information
  emergencyContact: string;
  relationship: string;
  emergencyAddress: string;
  emergencyPhoneNumber: string;
  specialMedicalExamDate: Date | null;
  insuranceExamDate: Date;
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  insuranceType: string; // New field: dropdown for insurance type
  insuranceNumber: string; // New field: text field for insurance number
  otherQualifications: string;
}

const NAME_MAX_LENGTH = 10;
const GENERAL_MAX_LENGTH = 200;

const schema = yup.object().shape({
  // Section 1: Profile
  firstName: yup
    .string()
    .trim()
    .required('名が必要です')
    .max(NAME_MAX_LENGTH, '名は10文字以下でなければなりません')
    .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

  lastName: yup
    .string()
    .trim()
    .required('姓が必要です')
    .max(NAME_MAX_LENGTH, '姓は10文字以下でなければなりません')
    .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

  firstNameKata: yup
    .string()
    .trim()
    .required('フリガナ(名)が必要です')
    .max(NAME_MAX_LENGTH, 'フリガナ(名)は10文字以下でなければなりません')
    .matches(REG_EXP.KATAKANA, '無効な形式です。'),

  lastNameKata: yup
    .string()
    .trim()
    .required('フリガナ(姓)が必要です')
    .max(NAME_MAX_LENGTH, 'フリガナ(姓)は10文字以下でなければなりません')
    .matches(REG_EXP.KATAKANA, '無効な形式です。'),

  birthDate: yup
    .date()
    .required('生年月日が必要です')
    .test('min-age', '18歳以上でなければなりません', (value) => {
      if (!value) return true; // Let required validation handle empty values
      const today = new Date();
      const birthDate = new Date(value);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      // Check if birthday has occurred this year
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < birthDate.getDate())
      ) {
        return age - 1 >= 18;
      }
      return age >= 18;
    }),

  age: yup.number(),

  gender: yup.string().required('性別が必要です'),

  address: yup
    .string()
    .required('住所が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  phoneNumber: yup
    .string()
    .required('電話番号が必要です')
    .test(
      'phone-format',
      '電話番号の形式が正しくありません（例：080-1234-5678、************）',
      (value) => {
        if (!value) return true; // Let required validation handle empty values
        // Remove any whitespace
        const cleanValue = value.trim();
        // Accept both formats: ###-####-#### or ###-###-####
        const phoneRegex = /^(\d{3}-\d{4}-\d{4}|\d{3}-\d{3}-\d{4})$/;
        return phoneRegex.test(cleanValue);
      },
    ),

  bloodType: yup.string().required('血液型が必要です'),

  employmentDate: yup.date().required('雇用年月日が必要です'),

  yearsOfExperience: yup
    .number()
    .required('経験年数が必要です')
    .min(0, '無効な形式です。')
    .max(100, '無効な形式です。')
    .test('digit-format', '無効な形式です。', (value) => {
      if (value === undefined || value === null) return true;
      // Check if the number has more than 2 digits before decimal point
      const wholePart = Math.floor(Math.abs(value)).toString();
      return wholePart.length <= 2;
    })
    .test('decimal-places', '無効な形式です。', (value) => {
      if (value === undefined || value === null) return true;
      // Check if the number has more than 1 decimal place
      const decimalPlaces = (value.toString().split('.')[1] || '').length;
      return decimalPlaces <= 1;
    })
    .typeError('無効な形式です。'),

  jobTitle: yup
    .string()
    .required('職種が必要です')
    .min(1, '少なくとも一つの職種を選択してください'),

  // Section 2: Other Information
  emergencyContact: yup
    .string()
    .required('緊急連絡先が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `緊急連絡先は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  relationship: yup
    .string()
    .required('続柄が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `続柄は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  emergencyAddress: yup
    .string()
    .required('緊急連絡先住所が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `緊急連絡先住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  emergencyPhoneNumber: yup.string().required('緊急連絡先電話番号が必要です'),

  specialMedicalExamDate: yup
    .date()
    .nullable()
    .test({
      name: 'conditional-required',
      message:
        '特殊健康診断日が必要です（塗装工事/溶接工事/Nuclear power plantの場合）',
      test(value) {
        const { jobTitle } = this.parent;

        if (!jobTitle) return true; // If no jobTitle, field is optional

        // Split jobTitle by common separators and check if any part matches required types
        const jobTitleParts = jobTitle
          .split(/[,、\s/\\]+/) // Split by comma, Japanese comma, space, slash, backslash
          .map((part: string) => part.trim())
          .filter((part: string) => part.length > 0);

        const isRequired = jobTitleParts.some((part: string) =>
          SPECIAL_EXAM_REQUIRED_JOB_TYPES.some(
            (requiredType) =>
              part.includes(requiredType) || requiredType.includes(part),
          ),
        );

        if (isRequired && !value) {
          return false;
        }
        return true;
      },
    }),

  insuranceExamDate: yup.date().required('最近の保険診断日が必要です'),

  bloodPressure: yup
    .string()
    .required('血圧が必要です')
    .test('numeric-value', '血圧は200未満でなければなりません', (value) => {
      if (!value) return true; // Let required validation handle empty values
      const numericValue = parseFloat(value);
      return !Number.isNaN(numericValue) && numericValue < 200;
    }),

  healthInsurance: yup
    .string()
    .required('健康保険が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `健康保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  pensionInsurance: yup
    .string()
    .required('年金保険が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `年金保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),

  insuranceType: yup
    .string()
    .required('雇用保険/一人親方労災保険の種類が必要です'),

  insuranceNumber: yup
    .string()
    .required('雇用保険/一人親方労災保険番号が必要です')
    .max(20, '雇用保険/一人親方労災保険番号は20文字以下でなければなりません'),

  otherQualifications: yup
    .string()
    .required('免許・資格・教育が必要です')
    .max(
      GENERAL_MAX_LENGTH,
      `免許・資格・教育は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
    ),
});

// Function to create schema with conditional validation
export const createContractedWorkerSchema = (jobTypeNames: string[] = []) => {
  const isSpecialExamRequired = jobTypeNames.some((jobType) =>
    SPECIAL_EXAM_REQUIRED_JOB_TYPES.includes(jobType),
  );

  return yup.object().shape({
    // Section 1: Profile
    firstName: yup
      .string()
      .trim()
      .required('名が必要です')
      .max(10, '名は10文字以下でなければなりません')
      .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

    lastName: yup
      .string()
      .trim()
      .required('姓が必要です')
      .max(10, '姓は10文字以下でなければなりません')
      .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),

    firstNameKata: yup
      .string()
      .trim()
      .required('フリガナ(名)が必要です')
      .max(10, 'フリガナ(名)は10文字以下でなければなりません')
      .matches(REG_EXP.KATAKANA, '無効な形式です。'),

    lastNameKata: yup
      .string()
      .trim()
      .required('フリガナ(姓)が必要です')
      .max(10, 'フリガナ(姓)は10文字以下でなければなりません')
      .matches(REG_EXP.KATAKANA, '無効な形式です。'),

    birthDate: yup
      .date()
      .required('生年月日が必要です')
      .test('min-age', '18歳以上でなければなりません', (value) => {
        if (!value) return true; // Let required validation handle empty values
        const today = new Date();
        const birthDate = new Date(value);
        const age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        // Check if birthday has occurred this year
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && today.getDate() < birthDate.getDate())
        ) {
          return age - 1 >= 18;
        }
        return age >= 18;
      }),

    age: yup.number(),

    gender: yup.string().required('性別が必要です'),

    address: yup
      .string()
      .required('現住所が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `現住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    phoneNumber: yup.string().required('電話番号が必要です'),

    bloodType: yup.string().required('血液型が必要です'),

    employmentDate: yup.date().required('雇用年月日が必要です'),

    yearsOfExperience: yup
      .number()
      .required('経験年数が必要です')
      .min(0, '経験年数は0年以上でなければなりません')
      .max(100, '無効な形式です。')
      .test(
        'digit-format',
        '経験年数は2桁以下の数字で入力してください（例：8、25、8.5）',
        (value) => {
          if (value === undefined || value === null) return true;
          // Check if the number has more than 2 digits before decimal point
          const wholePart = Math.floor(Math.abs(value)).toString();
          return wholePart.length <= 2;
        },
      )
      .test(
        'decimal-places',
        '経験年数は小数点以下1桁まで入力可能です',
        (value) => {
          if (value === undefined || value === null) return true;
          // Check if the number has more than 1 decimal place
          const decimalPlaces = (value.toString().split('.')[1] || '').length;
          return decimalPlaces <= 1;
        },
      )
      .typeError('経験年数は数字でなければなりません'),

    jobTitle: yup
      .string()
      .required('職種が必要です')
      .min(1, '職種を少なくとも1つ選択してください'),

    // Section 2: Other Information
    emergencyContact: yup
      .string()
      .required('緊急連絡先が必要です')
      .max(
        NAME_MAX_LENGTH,
        `緊急連絡先は${NAME_MAX_LENGTH}文字以下でなければなりません`,
      ),

    relationship: yup
      .string()
      .required('続柄が必要です')
      .max(
        NAME_MAX_LENGTH,
        `続柄は${NAME_MAX_LENGTH}文字以下でなければなりません`,
      ),

    emergencyAddress: yup
      .string()
      .required('緊急連絡先住所が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `緊急連絡先住所は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    emergencyPhoneNumber: yup.string().required('緊急連絡先電話番号が必要です'),

    // Conditional validation for special medical exam
    specialMedicalExamDate: isSpecialExamRequired
      ? yup
          .date()
          .nullable()
          .required(
            '特殊健康診断日が必要です（塗装工事/溶接工事/Nuclear power plantの場合）',
          )
      : yup.date().nullable().notRequired(),

    insuranceExamDate: yup.date().required('最近の保険診断日が必要です'),

    bloodPressure: yup
      .string()
      .required('血圧が必要です')
      .test('numeric-value', '血圧は200未満でなければなりません', (value) => {
        if (!value) return true; // Let required validation handle empty values
        const numericValue = parseFloat(value);
        return !Number.isNaN(numericValue) && numericValue < 200;
      }),

    healthInsurance: yup
      .string()
      .required('健康保険が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `健康保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    pensionInsurance: yup
      .string()
      .required('年金保険が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `年金保険は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),

    insuranceType: yup.string().required('保険種類が必要です'),

    insuranceNumber: yup
      .string()
      .required('保険番号が必要です')
      .max(20, '保険番号は20文字以下でなければなりません'),

    otherQualifications: yup
      .string()
      .required('免許・資格・教育が必要です')
      .max(
        GENERAL_MAX_LENGTH,
        `免許・資格・教育は${GENERAL_MAX_LENGTH}文字以下でなければなりません`,
      ),
  });
};

export default schema;

// API Request type
export interface CreateContractedWorkerRequest {
  firstName: string;
  firstNameKata: string;
  lastName: string;
  lastNameKata: string;
  birthDate: string; // ISO date string
  gender: string;
  address: string;
  phone: string;
  bloodType: string;
  employmentDate: string; // ISO date string
  yearsOfExperience: number;
  jobTitle: string;
  emergencyContact: {
    name: string;
    relationship: string;
    address: string;
    phone: string;
  };
  specialMedicalExamDate?: string; // ISO date string - optional
  insuranceExamDate: string; // ISO date string
  bloodPressure: string;
  healthInsurance: string;
  pensionInsurance: string;
  employmentInsurance: {
    type: string;
    number: string;
  };
  licenseCertEducation: string;
}

// Helper function to convert form values to API request format
export const convertFormToApiRequest = (
  values: ContractedWorkerFormValues,
): CreateContractedWorkerRequest => {
  return {
    firstName: values.firstName,
    firstNameKata: values.firstNameKata,
    lastName: values.lastName,
    lastNameKata: values.lastNameKata,
    birthDate: dayjs(values.birthDate).format('YYYY-MM-DD'),
    gender: values.gender,
    address: values.address,
    phone: values.phoneNumber,
    bloodType: values.bloodType,
    employmentDate: dayjs(values.employmentDate).format('YYYY-MM-DD'),
    yearsOfExperience: values.yearsOfExperience,
    jobTitle: values.jobTitle,
    emergencyContact: {
      name: values.emergencyContact,
      relationship: values.relationship,
      address: values.emergencyAddress,
      phone: values.emergencyPhoneNumber,
    },
    specialMedicalExamDate: values.specialMedicalExamDate
      ? dayjs(values.specialMedicalExamDate).format('YYYY-MM-DD')
      : undefined,
    insuranceExamDate: dayjs(values.insuranceExamDate).format('YYYY-MM-DD'),
    bloodPressure: values.bloodPressure,
    healthInsurance: values.healthInsurance,
    pensionInsurance: values.pensionInsurance,
    employmentInsurance: {
      type: values.insuranceType,
      number: values.insuranceNumber,
    },
    licenseCertEducation: values.otherQualifications,
  };
};
