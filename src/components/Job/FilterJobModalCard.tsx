import IconSearch from '@icons/icon-search.svg';
import {
  Badge,
  Box,
  Card,
  Center,
  Input,
  MediaQuery,
  Modal,
  Text,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useFetch, useSearch } from 'hooks';
import _isEqual from 'lodash/isEqual';
import jobQuery from 'models/job';
import type { IJobType } from 'models/job/type';
import type { ISelectableOption } from 'models/resource/type';
import React, { useEffect, useMemo, useState } from 'react';

import FilterJob from './FilterJob';
import type { FilterJobProps, FilterValues } from './types';

const FilterJobModalCard = ({ refetch }: FilterJobProps) => {
  const [opened, { open, close }] = useDisclosure(false);
  const { query } = useSearch();

  const { data: jobTypes } = useFetch<
    { jobTypes: IJobType[] },
    ISelectableOption[]
  >({
    ...jobQuery.getJobTypes,
    customParams: {
      page: 1,
      limit: 9999,
    },
    select: (data) =>
      data.jobTypes.map((item) => ({ label: item.name, value: item._id })),
  });

  const parsedParamValuesCount = useMemo(() => {
    const parsedParams = {
      workAreas: [...(query?.workAreas ?? '').split(',').filter(Boolean)],
      jobTypeIds: [...(query?.jobTypeIds ?? '').split(',').filter(Boolean)],
    };
    if (jobTypes?.length) {
      parsedParams.jobTypeIds = jobTypes
        .filter(({ value }) => parsedParams.jobTypeIds.includes(value))
        .map(({ value }) => value);
    }
    return {
      workAreas: parsedParams.workAreas.length,
      jobTypeIds: parsedParams.jobTypeIds.length,
    };
  }, [jobTypes, query?.workAreas, query?.jobTypeIds]);

  const [filterQueryCount, setFilterQueryCount] = useState(
    parsedParamValuesCount,
  );
  useEffect(() => {
    if (!_isEqual(parsedParamValuesCount, filterQueryCount)) {
      setFilterQueryCount(parsedParamValuesCount);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parsedParamValuesCount]);

  const onFilter = (conditions?: FilterValues) => {
    setFilterQueryCount({
      jobTypeIds: conditions?.jobTypeIds.length || 0,
      workAreas: conditions?.workAreas.length || 0,
    });
    close();
    const isConditionChanged =
      (query.jobTypeIds || '').trim() !== conditions?.jobTypeIds.toString() ||
      (query.workAreas || '').trim() !== conditions?.workAreas.toString();

    if (!isConditionChanged) {
      refetch?.();
    }
  };

  return (
    <MediaQuery largerThan={'sm'} styles={{ display: 'none' }}>
      <Box
        sx={{
          position: 'sticky',
          top: 'var(--mantine-header-height)',
          zIndex: 10,
        }}
      >
        <Modal
          fullScreen
          onClose={close}
          opened={opened}
          title={null}
          transitionProps={{ transition: 'fade', duration: 200 }}
          withCloseButton={false}
        >
          {/* Modal content */}
          <Card mih={'100vh'} withBorder={false}>
            <Card.Section
              inheritPadding
              sx={{
                display: 'flex',
                alignItems: 'center',
                margin: 'auto',
                marginTop: 'auto !important',
              }}
              withBorder
            >
              <Center mb={'17px'} mt={'20px'} w={'100%'}>
                <Text color="black" fw={700} fz={'lg'} lh={'26px'}>
                  案件を探す
                </Text>
              </Center>
            </Card.Section>
            <FilterJob onActionsCallback={onFilter} />
          </Card>
        </Modal>

        <Card
          display="flex"
          mih={'80px'}
          mx={'auto'}
          sx={() => ({
            padding: '16px',
            paddingBottom: '12px',
            flexDirection: 'column',
            alignItems: 'center',
            borderRadius: 0,
          })}
          w="100%"
          withBorder={false}
        >
          <Input
            component="button"
            icon={<IconSearch color="#4B5563" height={'24px'} width={'24px'} />}
            mb={'8px'}
            onClick={open}
            w={'100%'}
          >
            <Text color="gray-original" fw={400} fz="md" lh={'24px'}>
              案件を探す
            </Text>
          </Input>
          {/* Badge filters count */}
          <Box w={'100%'}>
            {filterQueryCount?.workAreas ? (
              <Badge
                className="curved"
                color={'gray-200'}
                p={'4px 8px 4px 16px'}
                sx={() => ({
                  '&:not(:last-child)': {
                    marginRight: '8px',
                  },
                })}
              >
                <Text
                  color="gray-500"
                  display={'inline-block'}
                  fw={400}
                  fz="14px"
                  lh={'20px'}
                  mr={'tight'}
                >
                  稼働エリア
                </Text>
                <Text
                  color="primary"
                  display={'inline-block'}
                  fw={700}
                  fz="14px"
                  lh={'20px'}
                  mih={'20px'}
                  miw={'20px'}
                  ta={'center'}
                >
                  {filterQueryCount.workAreas}
                </Text>
              </Badge>
            ) : null}

            {filterQueryCount?.jobTypeIds ? (
              <Badge
                className="curved"
                color={'gray-200'}
                p={'4px 8px 4px 16px'}
                sx={() => ({
                  '&:not(:last-child)': {
                    marginRight: '8px',
                  },
                })}
              >
                <Text
                  color="gray-500"
                  display={'inline-block'}
                  fw={400}
                  fz="14px"
                  lh={'20px'}
                  mr={'tight'}
                >
                  職種
                </Text>
                <Text
                  color="primary"
                  display={'inline-block'}
                  fw={700}
                  fz="14px"
                  lh={'20px'}
                  mih={'20px'}
                  miw={'20px'}
                  ta={'center'}
                >
                  {filterQueryCount.jobTypeIds}
                </Text>
              </Badge>
            ) : null}
          </Box>
        </Card>
      </Box>
    </MediaQuery>
  );
};
export default FilterJobModalCard;
