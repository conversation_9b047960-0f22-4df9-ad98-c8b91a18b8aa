import { Card, Group, MediaQuery, Text } from '@mantine/core';
import { useSearch } from 'hooks';

import FilterJob from './FilterJob';
import type { FilterJobProps, FilterValues } from './types';

export default function FilterJobSideCard({ refetch }: FilterJobProps) {
  const { query } = useSearch();

  const onFilter = (conditions?: FilterValues) => {
    const isConditionChanged =
      (query.jobTypeIds || '')?.trim() !== conditions?.jobTypeIds.toString() ||
      (query.workAreas || '')?.trim() !== conditions?.workAreas.toString();

    if (!isConditionChanged) {
      refetch?.();
    }
  };

  return (
    <MediaQuery smallerThan={'sm'} styles={{ display: 'none' }}>
      <Card
        h={'fit-content'}
        mah={'calc(100vh - 136px)'}
        maw={367}
        pos={'sticky'}
        sx={{
          overflow: 'auto',
        }}
        top={'104px'}
        w="100%"
        withBorder={false}
      >
        <Card.Section
          inheritPadding
          mih={'75px'}
          py={'md'}
          sx={{
            display: 'flex',
            alignItems: 'center',
            margin: 'auto',
            marginTop: 'auto !important',
          }}
          withBorder
        >
          <Group>
            <Text fw={700} fz={'lg'} lh={'26px'}>
              案件を探す
            </Text>
          </Group>
        </Card.Section>
        <FilterJob onActionsCallback={onFilter} />
      </Card>
    </MediaQuery>
  );
}
