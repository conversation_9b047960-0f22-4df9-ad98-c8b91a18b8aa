import IconLocation from '@icons/icon-location-16px.svg';
import IconReset from '@icons/icon-reset.svg';
import IconSearch from '@icons/icon-search.svg';
import type { MantineTheme } from '@mantine/core';
import {
  Box,
  Button,
  Center,
  Container,
  Flex,
  Stack,
  Text,
  ThemeIcon,
} from '@mantine/core';
import MultiSelectCheckbox from 'components/MultiSelectCheckbox';
import { JobTypeFilterTag } from 'components/Tag';
import { useFetch, useSearch } from 'hooks';
import _isEqual from 'lodash/isEqual';
import jobQuery from 'models/job';
import type { IJobType } from 'models/job/type';
import resourceQuery from 'models/resource';
import type { IPrefecture, ISelectableOption } from 'models/resource/type';
import React, { useEffect, useMemo, useState } from 'react';

import type { FilterValues } from './types';

export default function FilterJob({
  onActionsCallback,
}: {
  onActionsCallback?: (param?: FilterValues) => void;
}) {
  const { query, setQuery } = useSearch();

  const { data: prefectureOptions = [] } = useFetch<
    IPrefecture[],
    ISelectableOption[]
  >({
    ...resourceQuery.prefectures,
    select: (data) =>
      data.map((item) => ({
        label: item.prefecture_kanji,
        value: item.prefecture_kanji,
      })),
  });

  const { data: jobTypes } = useFetch<
    { jobTypes: IJobType[] },
    ISelectableOption[]
  >({
    ...jobQuery.getJobTypes,
    customParams: {
      page: 1,
      limit: 9999,
    },
    select: (data) =>
      data.jobTypes.map((item) => ({ label: item.name, value: item._id })),
  });

  const parsedParamValues = useMemo(() => {
    const parsedParams = {
      workAreas: [
        ...(query?.workAreas ?? '').split(',').filter(Boolean),
        ...(query?.all ? ['all'] : []),
      ],
      jobTypeIds: [...(query?.jobTypeIds ?? '').split(',').filter(Boolean)],
    };
    if (jobTypes?.length) {
      parsedParams.jobTypeIds = jobTypes
        .filter(({ value }) => parsedParams.jobTypeIds.includes(value))
        .map(({ value }) => value);
    }
    return parsedParams;
  }, [query?.workAreas, query?.all, query?.jobTypeIds, jobTypes]);

  const [filterValues, setFilterValues] =
    useState<FilterValues>(parsedParamValues);

  useEffect(() => {
    if (!_isEqual(parsedParamValues, filterValues)) {
      setFilterValues(parsedParamValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parsedParamValues]);

  function toggleFilterJobTypes(selectedValue: string): void {
    setFilterValues({
      ...filterValues,
      jobTypeIds: filterValues.jobTypeIds?.includes(selectedValue)
        ? [
            ...(filterValues.jobTypeIds ?? []).filter(
              (v) => v !== selectedValue,
            ),
          ]
        : [...new Set((filterValues.jobTypeIds ?? []).concat([selectedValue]))],
    });
  }
  const handleSelectPrefectures = (vals: FilterValues['workAreas']) => {
    setFilterValues({
      ...filterValues,
      workAreas: vals,
    });
  };

  const onFilter = () => {
    setQuery(
      {
        workAreas: filterValues.workAreas?.filter((i) => i !== 'all').join(','),
        all: filterValues.workAreas?.includes('all') ? '1' : 0,
        jobTypeIds: (filterValues.jobTypeIds ?? []).join(','),
        page: '1',
      },
      { replace: true, skipEmptyString: true },
    );
    onActionsCallback?.({
      workAreas: filterValues.workAreas
        ?.filter((i) => i !== 'all')
        .filter(Boolean),
      jobTypeIds: filterValues.jobTypeIds ?? [],
    });
  };

  const onReset = () => {
    setFilterValues({ workAreas: [], jobTypeIds: [] });

    setQuery(
      {
        workAreas: '',
        all: 0,
        jobTypeIds: '',
        sortField: '',
        page: '1',
      },
      { replace: true, skipEmptyString: true },
    );

    onActionsCallback?.({
      workAreas: [],
      jobTypeIds: [],
    });
  };

  const hadFiltered = useMemo(() => {
    return (
      filterValues?.jobTypeIds?.length ||
      filterValues?.workAreas?.length ||
      parsedParamValues?.jobTypeIds?.length ||
      parsedParamValues?.workAreas?.length
    );
  }, [
    filterValues?.jobTypeIds,
    filterValues?.workAreas,
    parsedParamValues?.jobTypeIds,
    parsedParamValues?.workAreas,
  ]);

  return (
    <Box>
      <Stack
        m={{ base: '24px 16px', sm: '32px auto auto' }}
        maw={{ base: undefined, sm: '319px' }}
        spacing={'32px'}
      >
        <Box>
          {/* Location */}
          <Stack mb={'24px'} spacing={'md'}>
            <Text color={'gray-700'} fw={700} fz={'md'} lh={'24px'}>
              稼働エリア
            </Text>
            <MultiSelectCheckbox
              data={prefectureOptions}
              disableSelectedItemFiltering
              icon={
                <ThemeIcon color="gray-700" size={24}>
                  <IconLocation height={24} width={24} />
                </ThemeIcon>
              }
              maxDropdownHeight={536}
              name="prefectures"
              placeholder="稼働エリア"
              setSelected={handleSelectPrefectures}
              sx={{
                '&.mantine-MultiSelect-root': {
                  padding: 0,
                },
                '.mantine-MultiSelect-dropdown .mantine-MultiSelect-itemsWrapper':
                  {
                    padding: '8px',
                  },
              }}
              value={(filterValues.workAreas || ['']) as string[]}
            />
            {filterValues.workAreas?.length ? (
              <Text color={'gray-500'} fz={'sm'} lh={'20px'}>
                {filterValues.workAreas
                  .filter((item) => item !== 'all')
                  .join('・')}
              </Text>
            ) : null}
          </Stack>

          {/* Job-type */}
          <Box>
            <Flex gap={'loose'} mb={'md'} w={'100%'}>
              <Text color="gray-700" fw={700} fz={'md'} lh={'24px'}>
                職種
              </Text>
              {filterValues.jobTypeIds?.length ? (
                <Center
                  h={'24px'}
                  p={'2px'}
                  sx={(theme) => ({
                    backgroundColor: theme.colors.primary,
                    borderRadius: '100%',
                  })}
                  w={'24px'}
                >
                  <Text color="white" fw={700} fz={'12px'} lh={'16px'}>
                    {filterValues.jobTypeIds.length}
                  </Text>
                </Center>
              ) : null}
            </Flex>

            <Container>
              {(jobTypes ?? []).map((item, index) => (
                <JobTypeFilterTag<ISelectableOption>
                  key={index}
                  onClick={toggleFilterJobTypes}
                  selected={filterValues.jobTypeIds?.includes(item.value)}
                  {...item}
                />
              ))}
            </Container>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Stack
          mb={'24px'}
          spacing={'md'}
          sx={(theme: MantineTheme) => ({
            [theme.fn.smallerThan('sm')]: {
              columnGap: '8px',
              rowGap: '8px',
              '@supports (-webkit-touch-callout: none)': {
                paddingBottom: '24px',
              },
              'button:last-child': {
                marginBottom: '24px',
              },
            },
          })}
        >
          <Button
            disabled={!hadFiltered}
            leftIcon={<IconSearch color="white" />}
            mih={'56px'}
            onClick={onFilter}
            size="lg"
            sx={(theme: MantineTheme) => ({
              [theme.fn.smallerThan('sm')]: {
                '.mantine-Button-inner': {
                  height: '30px',
                },
              },
            })}
          >
            検索
          </Button>
          {hadFiltered ? (
            <Button
              leftIcon={
                <ThemeIcon color="primary" size={24}>
                  <IconReset height={24} width={24} />
                </ThemeIcon>
              }
              mih={'56px'}
              onClick={onReset}
              size="lg"
              sx={(theme: MantineTheme) => ({
                [theme.fn.smallerThan('sm')]: {
                  '.mantine-Button-inner': {
                    height: '30px',
                  },
                },
              })}
              variant="subtle"
            >
              クリア
            </Button>
          ) : null}
        </Stack>
      </Stack>
    </Box>
  );
}
