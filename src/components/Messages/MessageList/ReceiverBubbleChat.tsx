import type { FlexProps } from '@mantine/core';
import { Box, Flex, Text } from '@mantine/core';
import MetaActionExtensionForMessageBubble from 'components/MessagesAI/MetaActionExtentionForMessageBubble';
import Linkify from 'linkify-react';
import type { IUser } from 'models/auth/type';
import {
  FileType,
  type IMessage,
  type IMessageAttachment,
} from 'models/chat/type';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import dayjs from 'utils/dayjs';

import FileMessage from '../attachment/File';
import ImageMessage from '../attachment/Image';
import Video from '../attachment/Video';
import Utils from '../utils';

interface ReceiverBubbleChatProps extends FlexProps {
  data: IMessage;
  userData?: IUser;
  isStartGroup: boolean;
  isEndGroup: boolean;
  roomId: string;
}

const ReceiverBubbleChat: React.FC<ReceiverBubbleChatProps> = ({
  data,
  userData,
  isStartGroup,
  isEndGroup,
  roomId,
  ...rest
}) => {
  const renderLink = ({
    attributes,
    content,
  }: {
    attributes: any;
    content: any;
  }) => {
    return (
      <Link {...attributes} rel="noreferrer" target="_blank">
        {content}
      </Link>
    );
  };

  const renderAttachments = (attachments: IMessageAttachment[]) => {
    return (
      <Flex align={'flex-start'} direction={'column'} gap={8} justify={'end'}>
        {attachments.map((file) => {
          if (
            FileType.VIDEO ===
            Utils.getFileTypeFromContentType(file.contentType)
          ) {
            return <Video attachment={file} key={file.id} />;
          }

          if (
            FileType.IMAGE ===
            Utils.getFileTypeFromContentType(file.contentType)
          ) {
            return <ImageMessage attachment={file} key={file.id} />;
          }

          return <FileMessage attachment={file} key={file.id} />;
        })}
      </Flex>
    );
  };

  return (
    <Flex {...rest}>
      {isStartGroup && (
        <Box
          h={{ base: 32, sm: 48 }}
          mr={{ base: 8, sm: 16 }}
          pos="relative"
          sx={{ flexShrink: 0 }}
          w={{ base: 32, sm: 48 }}
        >
          <Image
            alt="avatar"
            fill
            sizes="20vw"
            src={
              (typeof userData?.avatar === 'string'
                ? userData.avatar
                : userData?.avatar?.originUrl) || '/images/default-avatar.png'
            }
            style={{ borderRadius: '50%' }}
          />
        </Box>
      )}
      <Flex
        align="flex-start"
        direction="column"
        justify="flex-start"
        ml={isStartGroup ? 0 : { base: 40, sm: 64 }}
      >
        {isStartGroup && (
          <Text
            color="gray-original"
            fw={400}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            mb={4}
            ta="start"
          >
            <strong>{userData?.fullName}</strong>,{' '}
            {dayjs(data.createdAt).format('HH:mm')}
          </Text>
        )}
        {renderAttachments(data.attachments || [])}
        {!!data.value && !!data.value.length && (
          <Text
            color="black"
            fw={400}
            fz={{ base: 14 }}
            lh={{ base: '20px' }}
            pb={{ base: 12, sm: 16 }}
            pt={{ base: 8, sm: 16 }}
            px={{ base: 12, sm: 24 }}
            sx={(theme) => ({
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              borderTopRightRadius: '16px',
              borderBottomRightRadius: '16px',
              borderTopLeftRadius: '4px',
              borderBottomLeftRadius: '4px',
              backgroundColor: theme.colors['gray-200'],
              ...(isEndGroup
                ? {
                    borderBottomLeftRadius: '16px',
                  }
                : {}),
              a: {
                color: theme.colors['info-500'],
              },
            })}
          >
            <Linkify options={{ render: renderLink }}>{data.value}</Linkify>
          </Text>
        )}
        <MetaActionExtensionForMessageBubble
          message={data}
          metaActions={data.metaActions || []}
          roomId={roomId}
        />
      </Flex>
    </Flex>
  );
};

export default ReceiverBubbleChat;
