import IconTemplate from '@icons/icon-template.svg';
import type { FlexProps } from '@mantine/core';
import {
  Divider,
  Flex,
  Loader,
  Text,
  ThemeIcon,
  UnstyledButton,
} from '@mantine/core';
import { useIntersection } from '@mantine/hooks';
import type { InfiniteData } from '@tanstack/react-query';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { useGlobalState, useUser } from 'hooks';
import type { IUser } from 'models/auth/type';
import { chatService } from 'models/chat/chatService';
import type { IMessage } from 'models/chat/type';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { DateFormat } from 'utils/constants';
import dayjs from 'utils/dayjs';
import queryClient from 'utils/queryClient';

import { useAITypingIndicator } from './AITypingIndicator';
import ReceiverBubbleChat from './ReceiverBubbleChat';
import SenderBubbleChat from './SenderBubbleChat';

interface MessageListProps extends FlexProps {
  onSelectTemplate?: () => void;
  templateText?: string;
  members?: Record<string, IUser>;
  roomId: string;
  EmptyMessage?: React.ReactNode;
  onMarkRoomHasNoMessage?: () => void;
}

const MessageList = ({
  onSelectTemplate,
  templateText,
  members,
  roomId: id,
  EmptyMessage,
  onMarkRoomHasNoMessage,
  ...rest
}: MessageListProps) => {
  const { showChatTemplate, setShowChatTemplate } = useGlobalState();
  const messageListRef = useRef<HTMLDivElement>(null);
  const { data: currentUser } = useUser();
  const firebaseUserId = currentUser?.firebaseUserId || '';
  const { ref, entry } = useIntersection();

  const messageData = queryClient.getQueryData<InfiniteData<IMessage[]>>([
    'messages',
    id,
  ]);
  const messageStartAfter =
    messageData?.pages[0] && messageData?.pages[0][0]?.createdAt;

  const {
    data: messages,
    fetchNextPage,
    hasNextPage,
    fetchStatus,
    status,
  } = useInfiniteQuery<IMessage[]>(['messages', id], chatService.getMessages, {
    refetchOnReconnect: false,
    enabled: !!id && !messageData && !!firebaseUserId,
    getNextPageParam: (lp) =>
      lp.length && lp.length !== 0 ? lp[lp.length - 1]?.createdAt : null,
  });

  useEffect(() => {
    if (
      !messageData?.pages.at(0)?.length &&
      !messages?.pages.at(0)?.length &&
      status !== 'loading'
    ) {
      onMarkRoomHasNoMessage?.();
    }
  }, [messageData?.pages, messages?.pages, onMarkRoomHasNoMessage, status]);

  const { data: firebaseUsers } = useQuery<unknown, unknown, IUser[]>({
    queryKey: ['deleted', 'users', 'firebase'],
    refetchOnReconnect: false,
    queryFn: () => chatService.getDeletedRoomMemberInfo(id),
    enabled: !!id && !!firebaseUserId,
  });

  const { data: unsubscribe, remove } = useQuery({
    queryKey: ['messages-snapshot', id],
    enabled: !!id && !!firebaseUserId,
    refetchOnReconnect: false,
    queryFn: (payload) =>
      chatService.attachMessageListener({
        ...payload,
        startAfter: messageStartAfter,
      }),
  });

  useEffect(() => {
    return () => {
      if (unsubscribe) {
        unsubscribe();
        remove();
      }
    };
  }, [remove, unsubscribe]);

  useEffect(() => {
    if (entry?.isIntersecting && hasNextPage && fetchStatus !== 'fetching') {
      fetchNextPage();
    }
  }, [entry?.isIntersecting, fetchNextPage, hasNextPage, fetchStatus]);

  const handleReadMessages = useCallback(async () => {
    await chatService.readMessages({
      roomId: id,
      senderId: firebaseUserId,
    });
  }, [firebaseUserId, id]);

  useEffect(() => {
    if (messageData?.pages[0] && messageData?.pages[0][0]?.id) {
      handleReadMessages();
    }
  }, [handleReadMessages, messageData?.pages]);

  useEffect(() => {
    if (messageStartAfter) {
      setTimeout(() => {
        if (messageListRef.current) {
          messageListRef.current.scrollTo(0, 0);
        }
      }, 500);
    }
  }, [messageStartAfter]);

  const renderTimestamp = (createdAt: string) => {
    return (
      <Flex
        align="center"
        justify="center"
        my={{ base: 12, sm: 16 }}
        pos="relative"
      >
        <Divider color="gray-300" pos="absolute" w="100%" />
        <Text
          bg="white"
          color="gray-original"
          fz={{ base: 12, sm: 14 }}
          lh={{ base: '16px', sm: '20px' }}
          pos="relative"
          px={4}
          py={2}
        >
          {dayjs().startOf('d').diff(dayjs(createdAt).startOf('d'), 'd') > 0
            ? dayjs(createdAt).format(DateFormat.YEAR_MONTH_DATE_JP)
            : '今日'}
        </Text>
      </Flex>
    );
  };

  const messageList = useMemo(() => {
    return messages?.pages.map((page, pageIndex) => {
      const currentPage = messages.pages[pageIndex];
      const previousPage = messages.pages[pageIndex + 1];
      const nextPage = messages.pages[pageIndex - 1];

      return page.map((messageContext, messageIndex) => {
        const messageSenderId = messageContext.senderId || '';
        let previousMessage = currentPage
          ? currentPage[messageIndex + 1]
          : undefined;
        if (messageIndex === (currentPage?.length || 0) - 1) {
          previousMessage = previousPage ? previousPage[0] : undefined;
        }

        let nextMessage = currentPage
          ? currentPage[messageIndex - 1]
          : undefined;
        if (messageIndex === 0) {
          nextMessage = nextPage ? nextPage[0] : undefined;
        }

        const passed15Min =
          dayjs(messageContext.createdAt).diff(
            previousMessage?.createdAt,
            'm',
          ) > 15;
        const passed1Day =
          !previousMessage ||
          dayjs(messageContext.createdAt)
            .startOf('d')
            .diff(dayjs(previousMessage?.createdAt).startOf('d'), 'd') > 0;

        const isStartGroup =
          passed1Day ||
          passed15Min ||
          previousMessage?.senderId !== messageContext.senderId;

        const isEndGroup =
          (!pageIndex && !messageIndex) ||
          nextMessage?.senderId !== messageContext.senderId ||
          (nextMessage &&
            dayjs(nextMessage.createdAt).diff(messageContext.createdAt, 'm') >=
              15);

        if (messageContext.senderId === firebaseUserId) {
          return (
            <React.Fragment key={messageContext.id}>
              <Flex
                justify="flex-end"
                mb={{ base: 4, sm: 8 }}
                mt={isStartGroup ? { base: 4, sm: 8 } : 0}
              >
                <SenderBubbleChat
                  data={messageContext}
                  isStartGroup={isStartGroup}
                  maw={{ base: '80%', md: '60%' }}
                />
              </Flex>
              {passed1Day && renderTimestamp(messageContext.createdAt)}
            </React.Fragment>
          );
        }

        return (
          <React.Fragment key={messageContext.id}>
            <Flex
              justify="flex-start"
              mb={{ base: 4, sm: 8 }}
              mt={isStartGroup ? { base: 4, sm: 8 } : 0}
            >
              <ReceiverBubbleChat
                data={messageContext}
                isEndGroup={isEndGroup}
                isStartGroup={isStartGroup}
                maw={{ base: '80%', md: '60%' }}
                roomId={id}
                userData={
                  members && Object.keys(members).includes(messageSenderId)
                    ? members[messageSenderId]
                    : firebaseUsers?.find(
                        (u) => u.id === messageContext.senderId,
                      )
                }
              />
            </Flex>
            {passed1Day && renderTimestamp(messageContext.createdAt)}
          </React.Fragment>
        );
      });
    });
  }, [firebaseUserId, firebaseUsers, members, messages?.pages]);

  const { renderAITypingIndicator } = useAITypingIndicator({
    id,
    firebaseUserId,
    members,
    latestMessage: messages?.pages.flat().at(0),
  });

  return (
    <Flex
      direction="column-reverse"
      h="100%"
      pos="relative"
      ref={messageListRef}
      sx={{
        overflow: 'auto',
        '& > *': {
          flexShrink: 0,
        },
      }}
      w="100%"
      {...rest}
    >
      {onSelectTemplate && showChatTemplate && templateText && (
        <UnstyledButton
          animate={{ opacity: 1 }}
          bg="neutral-100"
          component={motion.div}
          initial={{ opacity: 0 }}
          layout
          mt={8}
          onClick={() => {
            onSelectTemplate();
            setShowChatTemplate(false);
          }}
          pb={{ base: 8, sm: 16 }}
          pt={{ base: 10, sm: 12 }}
          px={{ base: 8, sm: 16 }}
          sx={{ borderRadius: '10px', cursor: 'pointer', zIndex: 1 }}
        >
          <Flex align="center" mb={{ base: 6, sm: 8 }}>
            <Text
              color="neutral-600"
              fw={500}
              fz={{ base: 14, sm: 16 }}
              lh={{ base: '20px', sm: '24px' }}
            >
              メッセージ定型文を使って挨拶しましょう。
            </Text>
            <ThemeIcon color="neutral-600" size={24}>
              <IconTemplate />
            </ThemeIcon>
          </Flex>
          <Text
            bg="white"
            color="black"
            fw={400}
            fz={{ base: 14, sm: 16 }}
            lh={{ base: '20px', sm: '24px' }}
            pb={{ base: 8, sm: 16 }}
            pt={8}
            px={{ base: 8, sm: 12 }}
            sx={{ borderRadius: '10px' }}
          >
            {templateText}
          </Text>
        </UnstyledButton>
      )}
      {!messages?.pages.at(0)?.length && !!EmptyMessage && (
        <Flex
          align={'center'}
          bottom={0}
          justify={'center'}
          left={0}
          pos={'absolute'}
          right={0}
          top={0}
        >
          {EmptyMessage}
        </Flex>
      )}
      {renderAITypingIndicator()}
      {messageList}
      {(hasNextPage || fetchStatus === 'fetching' || status === 'loading') && (
        <Flex
          justify="center"
          my={24}
          ref={fetchStatus !== 'fetching' && hasNextPage ? ref : undefined}
          w="100%"
        >
          <Loader size={32} />
        </Flex>
      )}
    </Flex>
  );
};

export default MessageList;
