import type { BoxProps } from '@mantine/core';
import { Box, Flex, Text } from '@mantine/core';
import Linkify from 'linkify-react';
import type { IMessage, IMessageAttachment } from 'models/chat/type';
import { FileType } from 'models/chat/type';
import Link from 'next/link';
import React from 'react';
import dayjs from 'utils/dayjs';

import FileMessage from '../attachment/File';
import ImageMessage from '../attachment/Image';
import Video from '../attachment/Video';
import Utils from '../utils';

interface SenderBubbleChatProps extends BoxProps {
  data: IMessage;
  isStartGroup: boolean;
}

const SenderBubbleChat: React.FC<SenderBubbleChatProps> = ({
  data,
  isStartGroup,
  ...rest
}) => {
  const renderLink = ({
    attributes,
    content,
  }: {
    attributes: any;
    content: any;
  }) => {
    return (
      <Link {...attributes} rel="noreferrer" target="_blank">
        {content}
      </Link>
    );
  };

  const renderAttachments = (attachments: IMessageAttachment[]) => {
    return (
      <Flex align={'flex-end'} direction={'column'} gap={8} justify={'end'}>
        {attachments.map((file) => {
          if (
            FileType.VIDEO ===
            Utils.getFileTypeFromContentType(file.contentType)
          ) {
            return <Video attachment={file} key={file.id} />;
          }

          if (
            FileType.IMAGE ===
            Utils.getFileTypeFromContentType(file.contentType)
          ) {
            return <ImageMessage attachment={file} key={file.id} />;
          }

          return <FileMessage attachment={file} key={file.id} />;
        })}
      </Flex>
    );
  };

  return (
    <Box {...rest}>
      {isStartGroup && (
        <Text
          color="gray-original"
          fw={400}
          fz={{ base: 12, sm: 14 }}
          lh={{ base: '16px', sm: '20px' }}
          mb={4}
          ta="end"
        >
          {dayjs(data.createdAt).format('HH:mm')}
        </Text>
      )}
      {renderAttachments(data.attachments || [])}
      {!!data.value && !!data.value.length && (
        <Text
          color="neutral-600"
          fw={400}
          fz={{ base: 14 }}
          lh={{ base: '20px' }}
          pb={{ base: 12, sm: 16 }}
          pt={{ base: 8, sm: 16 }}
          px={{ base: 12, sm: 24 }}
          sx={(theme) => ({
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            borderTopLeftRadius: '16px',
            borderBottomLeftRadius: '16px',
            borderTopRightRadius: '4px',
            borderBottomRightRadius: '4px',
            backgroundColor: theme.colors['neutral-100'],
            ...(isStartGroup
              ? {
                  borderTopRightRadius: '16px',
                }
              : {}),
            a: {
              color: theme.colors['info-500'],
            },
          })}
        >
          <Linkify options={{ render: renderLink }}>{data.value}</Linkify>
        </Text>
      )}
    </Box>
  );
};

export default SenderBubbleChat;
