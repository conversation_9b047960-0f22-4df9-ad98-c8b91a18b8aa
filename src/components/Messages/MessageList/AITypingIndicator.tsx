import { Box, Flex, Text } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import type { IUser } from 'models/auth/type';
import type { IMessage, IRoom } from 'models/chat/type';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { AI_INDICATOR_TIMEOUT, AI_MEMBER_ID } from 'utils/constants';
import dayjs from 'utils/dayjs';

interface AITypingIndicatorProps {
  members?: Record<string, IUser>;
}

export const AITypingIndicator: React.FC<AITypingIndicatorProps> = ({
  members,
}) => {
  const aiMember = members?.[AI_MEMBER_ID];

  if (!aiMember) return null;

  return (
    <Flex justify="flex-start" mb={{ base: 4, sm: 8 }} mt={{ base: 4, sm: 8 }}>
      <Flex align="flex-start" maw={{ base: '80%', md: '60%' }}>
        <Box
          h={{ base: 32, sm: 48 }}
          mr={{ base: 8, sm: 16 }}
          pos="relative"
          sx={{ flexShrink: 0 }}
          w={{ base: 32, sm: 48 }}
        >
          <img
            alt="AI avatar"
            src={
              typeof aiMember.avatar === 'string'
                ? aiMember.avatar
                : aiMember.avatar?.originUrl || '/images/default-avatar.png'
            }
            style={{ borderRadius: '50%', width: '100%', height: '100%' }}
          />
        </Box>
        <Flex align="flex-start" direction="column" justify="flex-start">
          <Text
            color="gray-original"
            fw={400}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            mb={4}
            ta="start"
          >
            <strong>{aiMember.fullName}</strong>, {dayjs().format('HH:mm')}
          </Text>
          <Text
            color="black"
            fw={400}
            fz={{ base: 14 }}
            lh={{ base: '20px' }}
            pb={{ base: 12, sm: 16 }}
            pt={{ base: 8, sm: 16 }}
            px={{ base: 16, sm: 24 }}
            sx={(theme) => ({
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              borderTopRightRadius: '16px',
              borderBottomRightRadius: '16px',
              borderTopLeftRadius: '4px',
              borderBottomLeftRadius: '16px',
              backgroundColor: theme.colors['gray-200'],
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px',
              minWidth: '80px',
            })}
          >
            <motion.span
              animate={{ y: [0, -8, 0] }}
              style={{
                display: 'inline-block',
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: '#666',
              }}
              transition={{
                repeat: Infinity,
                duration: 1,
                delay: 0,
              }}
            />
            <motion.span
              animate={{ y: [0, -8, 0] }}
              style={{
                display: 'inline-block',
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: '#666',
              }}
              transition={{
                repeat: Infinity,
                duration: 1,
                delay: 0.2,
              }}
            />
            <motion.span
              animate={{ y: [0, -8, 0] }}
              style={{
                display: 'inline-block',
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: '#666',
              }}
              transition={{
                repeat: Infinity,
                duration: 1,
                delay: 0.4,
              }}
            />
          </Text>
        </Flex>
      </Flex>
    </Flex>
  );
};

export const useAITypingIndicator = ({
  id,
  firebaseUserId: _firebaseUserId,
  members,
  latestMessage,
}: {
  id: string;
  firebaseUserId: string;
  members?: Record<string, IUser>;
  latestMessage?: IMessage;
}) => {
  const [showAITyping, setShowAITyping] = useState(false);
  const aiTypingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { data: roomData } = useQuery({
    queryKey: ['chat', 'rooms', id],
    enabled: false,
    refetchOnReconnect: false,
    select: (data: any) => data?.pages?.[0]?.[0] as IRoom | undefined,
    queryFn: () => undefined,
  });

  const isAIChatRoom = useMemo(() => {
    return !!roomData?.aiThreadId && roomData?.isAIAssisting;
  }, [roomData?.aiThreadId, roomData?.isAIAssisting]);

  const clearAITypingIndicator = () => {
    if (aiTypingTimeoutRef.current) {
      clearTimeout(aiTypingTimeoutRef.current);
    }
    setShowAITyping(false);
  };

  const typingExpireAt = dayjs(latestMessage?.createdAt).add(
    AI_INDICATOR_TIMEOUT,
    'ms',
  );

  const setAITypingIndicator = () => {
    if (aiTypingTimeoutRef.current) {
      clearTimeout(aiTypingTimeoutRef.current);
    }
    setShowAITyping(true);

    aiTypingTimeoutRef.current = setTimeout(() => {
      setShowAITyping(false);
    }, typingExpireAt.diff(dayjs()));
  };

  useEffect(() => {
    if (isAIChatRoom) {
      if (
        latestMessage &&
        latestMessage?.senderId !== AI_MEMBER_ID &&
        typingExpireAt.isAfter(dayjs())
      ) {
        setAITypingIndicator();
      } else {
        clearAITypingIndicator();
      }
    }
  }, [latestMessage]);

  useEffect(() => {
    return () => {
      if (aiTypingTimeoutRef.current) {
        clearTimeout(aiTypingTimeoutRef.current);
      }
    };
  }, []);

  const renderAITypingIndicator = () => {
    if (!isAIChatRoom || !showAITyping) return null;
    return <AITypingIndicator members={members} />;
  };

  return {
    isAIChatRoom,
    showAITyping,
    renderAITypingIndicator,
  };
};
