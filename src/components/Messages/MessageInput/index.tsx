import IconSendMessage from '@icons/icon-send-message.svg';
import { ActionIcon, Textarea } from '@mantine/core';
import { debounce } from 'lodash';
import set from 'lodash/set';
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { isMobile } from 'react-device-detect';

import useStyles from './styles';

interface Props {
  disabled?: boolean;
  maxLength?: number;
  maxRow?: number;
  minRow?: number;
  readonly?: boolean;
  loading?: boolean;
  onSendMessage?: () => void;
}

export type TMessageInputRef = {
  message: string;
  clear: () => void;
  onSetText: (value: string) => void;
  focus: () => void;
};

const MessageInput = forwardRef<TMessageInputRef, Props>(
  (
    {
      disabled,
      maxLength = 500,
      maxRow = 1,
      minRow = 1,
      readonly,
      loading,
      onSendMessage,
    },
    ref,
  ) => {
    const [text, setText] = useState('');
    const { classes } = useStyles();
    const isComposing = useRef(false);
    const textAreaRef = useRef<HTMLTextAreaElement>(null);

    useImperativeHandle(ref, () => {
      return {
        message: text,
        clear: () => setText(''),
        onSetText: (value) => setText(value),
        focus: () => {
          if (textAreaRef && textAreaRef.current) textAreaRef.current.focus();
        },
      };
    });

    const handleSendMessage = debounce(() => {
      onSendMessage?.();
    }, 0);

    const setMessage = debounce((event) => {
      const { selectionStart, selectionEnd } = event.target;
      setText(
        (oldText) =>
          `${oldText.substring(0, selectionStart)}\n${oldText.substring(
            selectionEnd,
          )}`,
      );
      setTimeout(() => {
        set(event, 'target.selectionStart', selectionStart + 1);
        set(event, 'target.selectionEnd', selectionStart + 1);
      }, 1);
    }, 0);

    const handleOnEnter = async (
      event: React.KeyboardEvent<HTMLTextAreaElement>,
    ) => {
      if (isMobile || loading) return;
      if (
        event.key === 'Enter' &&
        (event.ctrlKey || event.shiftKey || event.altKey) &&
        isComposing.current === false
      ) {
        event.preventDefault();
        if (event.target instanceof HTMLTextAreaElement) {
          setMessage(event);
        }
        return;
      }
      if (event.key === 'Enter' && isComposing.current === false) {
        event.preventDefault();
        handleSendMessage();
      }
    };

    const onTouchMove = () => {
      textAreaRef.current?.blur();
    };

    return (
      <Textarea
        autosize
        className={classes.inputWrapper}
        disabled={disabled}
        maxLength={maxLength}
        maxRows={maxRow}
        minRows={minRow}
        onBlur={() => {
          document.removeEventListener('touchmove', onTouchMove);
        }}
        onChange={(event) => {
          const value = event.target.value || '';
          setText(value.slice(0, maxLength));
        }}
        onCompositionEnd={() => {
          isComposing.current = false;
        }}
        onCompositionStart={() => {
          isComposing.current = true;
        }}
        onFocus={() => {
          document.addEventListener('touchmove', onTouchMove);
        }}
        onKeyDown={handleOnEnter}
        placeholder="メッセージを入力してください。"
        readOnly={readonly}
        ref={textAreaRef}
        rightSection={
          <ActionIcon
            color="primary"
            disabled={disabled}
            loading={loading}
            onClick={onSendMessage}
            size="sm"
            variant="filled"
          >
            <IconSendMessage />
          </ActionIcon>
        }
        rightSectionProps={{
          style: {
            justifyContent: 'flex-start',
            right: 24,
          },
        }}
        rightSectionWidth={'fit-content'}
        size="md"
        value={text}
        w="100%"
      />
    );
  },
);

MessageInput.displayName = 'MessageInput';

export default MessageInput;
