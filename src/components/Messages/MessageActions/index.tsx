import IconAttached from '@icons/icon-attached.svg';
import IconClose from '@icons/icon-close.svg';
import IconPlay from '@icons/icon-play.svg';
import IconTrash from '@icons/icon-trash.svg';
import { Avatar, Box, Button, Flex, Text } from '@mantine/core';
import { useUpload } from 'hooks';
import useGlobalState from 'hooks/useGlobalState';
import type { IFile } from 'models/auth/type';
import { FileType } from 'models/chat/type';
import type { ChangeEvent } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import { OCR_FILE_UPLOAD_TYPES } from 'utils/constants';
import helpers from 'utils/helpers';

import {
  ACCEPTED_FILE_TYPES,
  MAX_FILE_SIZE,
  MAX_FILES,
  MAX_IMAGE_SIZE,
  MAX_VIDEO_SIZE,
} from '../const';
import type { TMessageInputRef } from '../MessageInput';
import MessageInput from '../MessageInput';
import Utils from '../utils';

interface Props {
  disabled?: boolean;
  loading?: boolean;
  maxInputRow?: number;
  onSendMessage?: (payload: TSendMessagePayload, onSuccess: () => void) => void;
  readonly?: boolean;
  templateText?: string;
  isTemplateUsing?: boolean;
  onTemplateUsed?: () => void;
  displayAttachButton?: boolean;
  accept?: string;
  maxFiles?: number;
}

export type TSendMessagePayload = {
  message: string;
  attachments: IFile[];
};

const MessageActions = ({
  disabled,
  loading,
  maxInputRow,
  onSendMessage,
  readonly,
  templateText,
  isTemplateUsing,
  onTemplateUsed,
  displayAttachButton = true,
  accept,
  maxFiles = MAX_FILES,
}: Props) => {
  const inputRef = useRef<TMessageInputRef>(null);
  const uploadRef = useRef<HTMLInputElement>(null);
  const [uploadedFiles, setUploadedFiles] = useState<IFile[]>([]);
  const { setChatUploadRef } = useGlobalState();

  const { isUploading, uploadFile } = useUpload({
    onUploadSuccess: (data) => {
      setUploadedFiles((prev) => [...prev, ...data]);
      inputRef.current?.focus();
    },
    maxSize: 25,
  });

  useEffect(() => {
    setChatUploadRef(uploadRef);

    return () => {
      setChatUploadRef(null);
    };
  }, [setChatUploadRef]);

  const handleSelectTemplate = useCallback(
    (text: string) => {
      if (inputRef.current) {
        inputRef.current.onSetText(`${inputRef.current.message}${text}`);
        onTemplateUsed?.();
      }
    },
    [onTemplateUsed],
  );

  useEffect(() => {
    if (!!templateText && !!isTemplateUsing) {
      handleSelectTemplate(templateText);
    }
  }, [handleSelectTemplate, isTemplateUsing, templateText]);

  const checkFileBeforeUpload = async (file: File): Promise<boolean> => {
    // Check if this is OCR file upload (when accept prop matches OCR types)
    const isOCRUpload = accept === OCR_FILE_UPLOAD_TYPES;

    if (isOCRUpload) {
      // Use OCR-specific validation
      const ocrValidation = await helpers.validateOCRFile(file);
      if (!ocrValidation.isValid) {
        helpers.toast({
          type: 'error',
          message: ocrValidation.errorMessage || 'ファイルの検証に失敗しました',
        });
        return false;
      }
      return true;
    }

    // Standard validation for non-OCR files
    if (!Utils.isAllowedFileType(file, ACCEPTED_FILE_TYPES.flat())) {
      helpers.toast({
        type: 'error',
        message: `指定できないファイル形式です。画像は${ACCEPTED_FILE_TYPES.flat()
          .join(', ')
          .toUpperCase()}形式でアップロードしてください。`,
      });

      return false;
    }

    if (
      Utils.isAllowedFileType(file, ACCEPTED_FILE_TYPES[0]) &&
      !Utils.isFileSizeValid(file, MAX_IMAGE_SIZE)
    ) {
      helpers.toast({
        type: 'error',
        message: `画像サイズは${MAX_IMAGE_SIZE}MB以内にしてください`,
      });

      return false;
    }

    if (
      Utils.isAllowedFileType(file, ACCEPTED_FILE_TYPES[1]) &&
      !Utils.isFileSizeValid(file, MAX_VIDEO_SIZE)
    ) {
      helpers.toast({
        type: 'error',
        message: `動画サイズは${MAX_VIDEO_SIZE}MB以内にしてください`,
      });

      return false;
    }

    if (
      Utils.isAllowedFileType(file, ACCEPTED_FILE_TYPES[2]) &&
      !Utils.isFileSizeValid(file, MAX_FILE_SIZE)
    ) {
      helpers.toast({
        type: 'error',
        message: `ファイルサイズは${MAX_FILE_SIZE}MB以内にしてください。`,
      });

      return false;
    }

    return true;
  };

  const handleOnFilesChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;

    if (!files || files.length === 0) {
      return;
    }

    const [payload] = files;

    if (!payload) {
      e.target.value = '';
      return;
    }

    if (maxFiles <= uploadedFiles.length) {
      helpers.toast({
        message: `1度にアップロードできるのは ${maxFiles} 個のファイル までです`,
        type: 'error',
      });
      e.target.value = '';
      return;
    }

    const isValidFile = await checkFileBeforeUpload(payload);
    if (!isValidFile) {
      e.target.value = '';
      return;
    }

    uploadFile({ files: [payload] });
  };

  const handleSendMessage = () => {
    if (isUploading) {
      return;
    }

    if (inputRef.current) {
      const val = (inputRef.current.message || '').trim();

      if ((val && val.length) || uploadedFiles.length) {
        onSendMessage?.(
          {
            message: val,
            attachments: uploadedFiles,
          },
          () => {
            inputRef?.current?.clear();
            setUploadedFiles([]);
          },
        );
      }
    }
  };

  const removeUploadedFiles = (file: IFile) => {
    setUploadedFiles((prev) => prev.filter((f) => f.key !== file.key));
  };

  const renderPreviewAttachments = () => {
    if (!uploadedFiles.length) return null;

    const medias = uploadedFiles
      .filter((file) =>
        [FileType.IMAGE, FileType.VIDEO].includes(
          Utils.getFileTypeFromContentType(file.file?.type || ''),
        ),
      )
      .map((file) => {
        if (
          Utils.getFileTypeFromContentType(file.file?.type || '') ===
          FileType.IMAGE
        ) {
          return (
            <Box key={file.key} pos={'relative'}>
              <Avatar
                alt={file.originalName}
                h={64}
                src={file.originUrl}
                sx={{ cursor: 'pointer' }}
                w={64}
              />
              <Flex
                align={'center'}
                bg={'gray-200'}
                h={12}
                justify={'center'}
                onClick={() => removeUploadedFiles(file)}
                pos={'absolute'}
                right={0}
                sx={{
                  borderRadius: '50%',
                  transform: 'translateX(50%) translateY(-50%)',
                  cursor: 'pointer',
                }}
                top={0}
                w={12}
              >
                <IconClose height={8} viewBox="0 0 10 10" width={8} />
              </Flex>
            </Box>
          );
        }

        return (
          <Box
            key={file.key}
            pos={'relative'}
            sx={(theme) => ({
              border: '1px solid',
              borderColor: theme.colors['gray-300'],
              borderRadius: 8,
            })}
          >
            <ReactPlayer height={64} url={file.originUrl} width={64} />
            <Flex
              align={'center'}
              bg={'gray-200'}
              h={12}
              justify={'center'}
              onClick={() => removeUploadedFiles(file)}
              pos={'absolute'}
              right={0}
              sx={{
                borderRadius: '50%',
                transform: 'translateX(50%) translateY(-50%)',
                cursor: 'pointer',
              }}
              top={0}
              w={12}
            >
              <IconClose height={8} viewBox="0 0 10 10" width={8} />
            </Flex>
            <IconPlay
              height={18}
              style={{
                transform: 'translateX(-50%) translateY(-50%)',
                position: 'absolute',
                top: '50%',
                left: '50%',
              }}
              viewBox="0 0 48 48"
              width={18}
            />
          </Box>
        );
      });

    const documents = uploadedFiles
      .filter((file) =>
        [FileType.FILE].includes(
          Utils.getFileTypeFromContentType(file.file?.type || ''),
        ),
      )
      .map((file) => (
        <Flex
          align={'center'}
          bg={'gray-200'}
          direction={'row'}
          justify={'space-between'}
          key={file.key}
          p={{ base: 4, sm: 8 }}
          sx={{ borderRadius: 6 }}
        >
          <Text fz={12}>{file.originalName}</Text>
          <Box
            onClick={() => removeUploadedFiles(file)}
            sx={{ cursor: 'pointer' }}
          >
            <IconTrash height={14} viewBox="0 0 20 20" width={14} />
          </Box>
        </Flex>
      ));

    return (
      <Flex direction={'column'} gap={4} px={{ base: 16, sm: 32 }}>
        <Flex direction={'row'} gap={12}>
          {medias}
        </Flex>
        <Flex direction={'column'} gap={4}>
          {documents}
        </Flex>
      </Flex>
    );
  };

  return (
    <Flex direction={'column'} gap={4}>
      <Flex
        align={'center'}
        bg="white"
        bottom={0}
        direction={'row'}
        gap={{ base: 4, sm: 8 }}
        px={{ base: 16, sm: 24 }}
        py={{ base: 8, sm: 16 }}
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            boxShadow: '2px -4px 10px 0px  rgba(117, 138, 147, 0.10)',
          },
        })}
        w="100%"
      >
        {displayAttachButton && (
          <input
            accept={
              accept ||
              ACCEPTED_FILE_TYPES.flat()
                .map((ext) => `.${ext}`)
                .join(',')
            }
            name="uploadFile"
            onChange={handleOnFilesChange}
            ref={uploadRef}
            style={{ display: 'none' }}
            type="file"
          />
        )}
        <Button
          loading={isUploading}
          onClick={() => setTimeout(() => uploadRef.current?.click(), 100)}
          p={{ base: 2, sm: 4 }}
          size="md"
          style={{ display: displayAttachButton ? 'block' : 'none' }}
          sx={{ border: 'none', minHeight: 32 }}
          variant="default"
        >
          <IconAttached />
        </Button>
        <MessageInput
          disabled={disabled}
          loading={loading}
          maxRow={maxInputRow}
          onSendMessage={handleSendMessage}
          readonly={readonly}
          ref={inputRef}
        />
      </Flex>
      {renderPreviewAttachments()}
    </Flex>
  );
};

export default MessageActions;
