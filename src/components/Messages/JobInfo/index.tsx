import IconArrowDown from '@icons/icon-arrow-down.svg';
import IconClock from '@icons/icon-clock.svg';
import IconDeadline from '@icons/icon-deadline.svg';
import IconJobType from '@icons/icon-job-type.svg';
import IconLocation from '@icons/icon-location-16px.svg';
import IconSalary from '@icons/icon-salary.svg';
import {
  Box,
  Collapse,
  Flex,
  MediaQuery,
  Text,
  ThemeIcon,
  Title,
  UnstyledButton,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import Tag from 'components/Tag';
import type { IJobMatchingItem } from 'models/job/type';
import React, { useMemo } from 'react';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

import useStyles from './styles';

const JobInfo = ({ data }: { data?: IJobMatchingItem }) => {
  const { classes } = useStyles();
  const [opened, { toggle }] = useDisclosure(false);

  const ContentRows = useMemo(() => {
    return [
      {
        icon: <IconJobType />,
        label: '職種',
        content: <Tag text={data?.jobPostInfo.jobType || ''} />,
      },
      {
        icon: <IconLocation />,
        label: '稼働エリア',
        content: data?.jobPostInfo.workArea,
      },
      {
        icon: <IconClock />,
        label: '予定工期',
        content: `${
          data?.jobPostInfo.startDate
            ? dayjs(data.jobPostInfo.startDate).format('YYYY年MM月DD日')
            : ''
        }${
          data?.jobPostInfo.endDate
            ? ` - ${dayjs(data.jobPostInfo.endDate).format('YYYY年MM月DD日')}`
            : ''
        }`,
      },
      {
        icon: <IconDeadline />,
        label: '募集終了日',
        content: dayjs(data?.jobPostInfo.publicationEndDate).format(
          'YYYY年MM月DD日',
        ),
      },
      {
        icon: <IconSalary />,
        label: '単価(税抜)',
        content: data?.jobPostInfo.isPrivateSalary
          ? 'メッセージにて見積もり依頼'
          : helpers.renderSalary(
              data?.jobPostInfo.minSalary || 0,
              data?.jobPostInfo.maxSalary,
            ),
      },
    ].map(({ label, icon, content }, index) => (
      <Flex align="center" gap={4} key={`${label}-${index}`}>
        <ThemeIcon color="primary" size={16}>
          {icon}
        </ThemeIcon>
        <Text
          color="gray-700"
          fw={400}
          fz={14}
          lh="20px"
          sx={{
            '& > span': {
              marginLeft: 4,
            },
          }}
        >
          {label}:<span>{content}</span>
        </Text>
      </Flex>
    ));
  }, [data]);

  return (
    <Box>
      <Text
        color="gray-700"
        fw={400}
        fz={{ base: 12, sm: 14 }}
        lh={{ base: '16px', sm: '20px' }}
        mb={4}
      >
        {data?.jobPostInfo.companyName}
      </Text>
      <Title
        color="black"
        fw={700}
        fz={{ base: 14, sm: 18 }}
        lh={{ base: '20px', sm: '26px' }}
        mb={{ base: 8, sm: 16 }}
      >
        {data?.jobPostInfo.title}
      </Title>
      <MediaQuery smallerThan="sm" styles={{ display: 'none' }}>
        <Flex direction="column" gap={8} justify="flex-start">
          {ContentRows}
        </Flex>
      </MediaQuery>
      <MediaQuery largerThan="sm" styles={{ display: 'none' }}>
        <Box>
          <UnstyledButton
            className={classes.collapseBtn}
            onClick={toggle}
            w="100%"
          >
            {opened ? 'Less' : 'More'}
            <ThemeIcon
              color="info-500"
              size={24}
              sx={
                opened
                  ? {
                      transform: 'rotate(180deg)',
                    }
                  : {}
              }
            >
              <IconArrowDown />
            </ThemeIcon>
          </UnstyledButton>
          <Collapse in={opened}>
            <Flex direction="column" gap={8} justify="flex-start" mt={8}>
              {ContentRows}
            </Flex>
          </Collapse>
        </Box>
      </MediaQuery>
    </Box>
  );
};

export default JobInfo;
