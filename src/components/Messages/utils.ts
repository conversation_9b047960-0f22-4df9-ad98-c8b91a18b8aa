import dayjs from 'dayjs';
import { FileType, type IMessageAttachment } from 'models/chat/type';

import { IMAGE_CONTENT_TYPE, VIDEO_CONTENT_TYPE } from './const';

const Utils = {
  getRenderedSize: (width: number = 0, height: number = 0) => {
    const widthPercent = 400 / width;
    const heightPercent = 200 / height;
    const mobileWidthPercent = 200 / width;
    const mobileHeightPercent = 280 / height;
    const smallestPercent = Math.min(widthPercent, heightPercent);
    const mobileSmallestPercent = Math.min(
      mobileWidthPercent,
      mobileHeightPercent,
    );
    const renderedWidth = width * smallestPercent;
    const renderedHeight = height * smallestPercent;
    const mobileRenderedWidth = width * mobileSmallestPercent;
    const mobileRenderedHeight = height * mobileSmallestPercent;
    return {
      width: renderedWidth,
      height: renderedHeight,
      mobileRenderedWidth,
      mobileRenderedHeight,
    };
  },
  convertSecondsToTimeString: (second: number) => {
    return dayjs.utc(second * 1000).format('mm:ss');
  },
  getLastMessageFromAttachment: (attachment: IMessageAttachment) => {
    let lastMessageContent = '';
    if (attachment.contentType.startsWith('image')) {
      lastMessageContent = `[画像]`;
    } else if (attachment.contentType.startsWith('video')) {
      lastMessageContent = `[動画]`;
    } else lastMessageContent = `[ファイル] ${attachment.name}`;
    return lastMessageContent;
  },
  getFileTypeFromContentType: (contentType: string): FileType => {
    if (IMAGE_CONTENT_TYPE.includes(contentType)) {
      return FileType.IMAGE;
    }
    if (VIDEO_CONTENT_TYPE.includes(contentType)) {
      return FileType.VIDEO;
    }
    return FileType.FILE;
  },
  downloadURI: (url: string, name: string) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = name;
        link.click();
      });
  },
  isAllowedFileType: (file: File, exts?: string[]) => {
    return (exts || []).includes(
      file.name.split('.').reverse().at(0)?.toLowerCase() || '',
    );
  },
  isFileSizeValid: (file: File, size?: number) => {
    if (!size || size === 0) {
      return true;
    }
    return file.size / 1024 / 1024 <= size;
  },
};

export default Utils;
