import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  fileWrapper: {
    border: `1px solid ${theme.colors['gray-3']}`,
    borderRadius: 8,
    gap: 10,
    alignItems: 'center',
    overflow: 'hidden',
  },
  icon: {
    [theme.fn.smallerThan('sm')]: {
      width: 20,
      height: 20,
      minWidth: 20,
      minHeight: 20,
    },
  },
  modalContent: {
    height: '100%',
    backgroundColor: 'transparent',
  },
  modalBody: {
    height: 'calc(100% - 48px)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    padding: 0,
    backgroundColor: 'rgb(0, 0, 0, 0.80)',
  },
  modalHeader: {
    padding: 8,
    backgroundColor: '#2A2A2C',
  },
  modalClose: {
    '&:hover': {
      backgroundColor: theme.colors['gray-6'],
    },
  },
  modalTitle: {
    // position: 'absolute',
    padding: 8,
  },
}));

export default useStyles;
