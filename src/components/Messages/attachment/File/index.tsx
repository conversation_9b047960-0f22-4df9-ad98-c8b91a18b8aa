import <PERSON>Vie<PERSON>, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import FileIcon from '@icons/icon-file.svg';
import {
  Box,
  Button,
  Flex,
  LoadingOverlay,
  Modal,
  Text,
  ThemeIcon,
} from '@mantine/core';
import Utils from 'components/Messages/utils';
import { useState } from 'react';

import type { AttachmentComponentProps } from '../type';
import useStyles from './styles';

const FileMessage = ({ attachment, isLoading }: AttachmentComponentProps) => {
  const { classes } = useStyles();
  const [opened, setOpened] = useState(false);
  const fileExtension = attachment.name.split('.').pop();
  return (
    <Box>
      <Box
        bg="gray-200"
        className={classes.fileWrapper}
        display="flex"
        maw={{ base: 256, sm: 400 }}
        onClick={() => {
          if (!isLoading) {
            setOpened(true);
          }
        }}
        p={8}
        pos="relative"
        sx={{
          cursor: !isLoading ? 'pointer' : 'unset',
        }}
      >
        <Box
          display="inline-flex"
          p={{ base: 10, sm: 12 }}
          sx={{ borderRadius: 6 }}
        >
          <ThemeIcon className={classes.icon} color="gray-8">
            <FileIcon />
          </ThemeIcon>
        </Box>
        <Box>
          <Flex>
            <Text
              fw={500}
              fz={{ base: 12, sm: 14 }}
              lineClamp={1}
              sx={{ flex: 1 }}
            >
              {attachment.name.replace(/\.[^/.]+$/, '')}
            </Text>
            <Text
              component="span"
              fw={500}
              fz={{ base: 12, sm: 14 }}
              w="fit-content "
            >
              .{fileExtension}
            </Text>
          </Flex>
          <Text c="gray-6" fz={12} lh="16px" mt={{ base: 3, sm: 0 }}>
            {(attachment.size / (1024 * 1024)).toFixed(2)} MB
          </Text>
        </Box>
        <LoadingOverlay visible={!!isLoading} zIndex={2} />
      </Box>
      <Modal
        classNames={{
          content: classes.modalContent,
          body: classes.modalBody,
          header: classes.modalHeader,
          close: classes.modalClose,
          title: classes.modalTitle,
        }}
        fullScreen
        onClose={() => setOpened(false)}
        opened={opened}
        radius={0}
        title={
          <Button
            onClick={() => Utils.downloadURI(attachment.url, attachment.name)}
            size="sm"
          >
            Download
          </Button>
        }
        transitionProps={{ transition: 'fade', duration: 200 }}
        trapFocus={false}
      >
        <Box
          h="100%"
          maw={1200}
          sx={{
            '#pdf-controls': {
              display: 'none',
            },
          }}
          w="100%"
        >
          <DocViewer
            config={{ header: { disableHeader: true } }}
            documents={[{ uri: attachment.url }]}
            pluginRenderers={DocViewerRenderers}
            prefetchMethod="GET"
            style={{ width: '100%' }}
          />
        </Box>
      </Modal>
    </Box>
  );
};

export default FileMessage;
