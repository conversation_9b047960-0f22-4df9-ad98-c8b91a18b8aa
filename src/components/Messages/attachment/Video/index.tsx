import PlayIcon from '@icons/icon-play.svg';
import { Box, LoadingOverlay, Modal } from '@mantine/core';
import Utils from 'components/Messages/utils';
import { useState } from 'react';
import ReactPlayer from 'react-player';

import type { AttachmentComponentProps } from '../type';
import useStyles from './styles';

const Video = ({ attachment, isLoading }: AttachmentComponentProps) => {
  const { classes } = useStyles();
  const [opened, setOpened] = useState(false);
  const [videoDuration, setVideoDuration] = useState<number | null>(null);
  return (
    <div>
      <div
        className={classes.videoWrapper}
        onClick={() => {
          if (!isLoading) {
            setOpened(true);
          }
        }}
      >
        <ReactPlayer
          height={200}
          key={attachment.url}
          onDuration={(duration) => setVideoDuration(duration)}
          url={attachment.url}
          width={280}
        />
        {!isLoading && (
          <Box className={classes.playButton}>
            <PlayIcon />
          </Box>
        )}
        {videoDuration && (
          <Box className={classes.timeWrapper} pos="absolute">
            {Utils.convertSecondsToTimeString(videoDuration)}
          </Box>
        )}
        <LoadingOverlay visible={!!isLoading} zIndex={2} />
      </div>
      <Modal
        classNames={{
          content: classes.modalContent,
          body: classes.modalBody,
          header: classes.modalHeader,
          close: classes.modalClose,
        }}
        fullScreen
        onClose={() => setOpened(false)}
        opened={opened}
        radius={0}
        transitionProps={{ transition: 'fade', duration: 200 }}
        trapFocus={false}
      >
        <ReactPlayer
          controls
          height="100%"
          style={{ maxHeight: '99%', maxWidth: '100%' }}
          url={attachment.url}
          width="100%"
        />
      </Modal>
    </div>
  );
};

export default Video;
