import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  videoWrapper: {
    position: 'relative',
    cursor: 'pointer',
    video: {
      objectFit: 'cover',
      borderRadius: 8,
      display: 'block',
    },
  },
  timeWrapper: {
    backgroundColor: theme.colors['gray-8'],
    opacity: 0.6,
    fontSize: 12,
    fontWeight: 500,
    color: theme.colors.white,
    padding: '0px 2px',
    borderRadius: 2,
    position: 'absolute',
    left: 8,
    bottom: 8,
  },
  playButton: {
    position: 'absolute',
    width: 'fit-content',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
  },
  modalContent: {
    height: '100%',
    backgroundColor: 'transparent',
  },
  modalBody: {
    height: 'calc(100% - 48px)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    padding: 0,
    backgroundColor: 'rgb(0, 0, 0, 0.80)',
  },
  modalHeader: {
    padding: 8,
    backgroundColor: '#2A2A2C',
  },
  modalClose: {
    '&:hover': {
      backgroundColor: theme.colors['gray-6'],
    },
  },
}));

export default useStyles;
