import { Avatar, Box, LoadingOverlay, Modal } from '@mantine/core';
import { useState } from 'react';

import type { AttachmentComponentProps } from '../type';
import useStyles from './styles';

const ImageMessage = ({ attachment, isLoading }: AttachmentComponentProps) => {
  const { classes } = useStyles();
  const [opened, setOpened] = useState(false);
  return (
    <Box
      mah={{ base: 280, sm: 200 }}
      maw={{ base: 200, sm: 400 }}
      sx={{ position: 'relative' }}
    >
      <Avatar
        alt=""
        h={{ base: 160, sm: 200 }}
        onClick={() => setOpened(true)}
        src={attachment.url}
        sx={{ cursor: 'pointer' }}
        w={{ base: 200, sm: 280 }}
      />
      <LoadingOverlay visible={!!isLoading} zIndex={2} />
      <Modal
        classNames={{
          content: classes.modalContent,
          body: classes.modalBody,
          header: classes.modalHeader,
          close: classes.modalClose,
        }}
        fullScreen
        onClose={() => setOpened(false)}
        opened={opened}
        radius={0}
        transitionProps={{ transition: 'fade', duration: 200 }}
        trapFocus={false}
      >
        <img
          alt=""
          src={attachment.url}
          style={{ maxHeight: '100%', maxWidth: '100%' }}
        />
      </Modal>
    </Box>
  );
};

export default ImageMessage;
