import { REG_EXP } from 'utils/constants';
import type { InferType } from 'yup';
import { array, object, string } from 'yup';

const schema = object({
  lastName: string()
    .trim()
    .required()
    .max(10)
    .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),
  firstName: string()
    .trim()
    .required()
    .max(10)
    .matches(REG_EXP.ALPHABET_JP_REGX, '無効な形式です。'),
  firstNameKata: string()
    .required()
    .trim()
    .max(10)
    .matches(REG_EXP.KATAKANA, '無効な形式です。'),
  lastNameKata: string()
    .required()
    .trim()
    .max(10)
    .matches(REG_EXP.KATAKANA, '無効な形式です。'),
  avatarKey: array().nullable(),
  phone: string().trim().matches(REG_EXP.PHONE, {
    message: '正しい電話番号を入力してください。',
    excludeEmptyString: true,
  }),
});

export default schema;
export type BasicProfileFormValues = InferType<typeof schema>;
