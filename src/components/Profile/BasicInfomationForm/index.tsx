import CameraIcon from '@icons/icon-camera.svg';
import IconGallery from '@icons/icon-gallery.svg';
import IconTrash from '@icons/icon-trash.svg';
import DefaultAvatar from '@images/default-avatar.png';
import { Box, Flex, LoadingOverlay, Menu } from '@mantine/core';
import { FileUpload, TextField } from 'components/Form';
import Image from 'next/image';
import React from 'react';
import { useFormContext } from 'react-hook-form';

const BasicInfomationForm = () => {
  // Wrap component with FormProvider to get context
  const { control } = useFormContext();
  return (
    <Box component="form" w="100%">
      <FileUpload
        control={control}
        name="avatarKey"
        render={({ ref, remove, values, isUploading }) => (
          <Menu disabled={isUploading} width={160}>
            <Menu.Target>
              <Box h={120} mx="auto" pos={'relative'} w={120}>
                <Image
                  alt="avatar"
                  fill
                  priority
                  sizes="20vw"
                  src={values[0]?.originUrl || DefaultAvatar}
                  style={{ borderRadius: 60 }}
                />
                <Box bottom={0} pos={'absolute'} right={0}>
                  <CameraIcon />
                </Box>
                <LoadingOverlay visible={isUploading} />
              </Box>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item
                icon={<IconGallery />}
                onClick={() => ref.current?.click()}
              >
                アップロード
              </Menu.Item>
              {values[0]?.originUrl && (
                <Menu.Item icon={<IconTrash />} onClick={() => remove(0)}>
                  削除
                </Menu.Item>
              )}
            </Menu.Dropdown>
          </Menu>
        )}
        withCrop
      />
      <Flex
        align={'flex-start'}
        gap={16}
        justify="stretch"
        mb={24}
        mt={32}
        sx={{ '& > *': { flex: 1 } }}
      >
        <TextField
          control={control}
          label="姓"
          name="lastName"
          placeholder="姓"
          required
        />
        <TextField
          control={control}
          label="名"
          name="firstName"
          placeholder="名"
          required
        />
      </Flex>
      <Flex align={'flex-start'} gap={16} mb={24} sx={{ '& > *': { flex: 1 } }}>
        <TextField
          control={control}
          label="フリガナ(姓)"
          name="lastNameKata"
          placeholder="フリガナ"
          required
        />
        <TextField
          control={control}
          label="フリガナ(名)"
          name="firstNameKata"
          placeholder="フリガナ"
          required
        />
      </Flex>
      <TextField
        control={control}
        label="電話番号"
        name="phone"
        placeholder="電話番号（ハイフンなし）"
      />
    </Box>
  );
};

export default BasicInfomationForm;
