import { Box, Divider, Flex } from '@mantine/core';
import {
  DateInputField,
  MultiSelectField,
  SelectField,
  TextField,
} from 'components/Form';
import RadioGroup from 'components/Form/RadioGroup';
import TextareaField from 'components/Form/TextareaField';
import dayjs from 'dayjs';
import type { IConstructionCertification, IInsurance } from 'models/auth/type';
import type { ISelectableOption } from 'models/resource/type';
import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { DateFormat, NationalCertificate, WorkerType } from 'utils/constants';

import type { WorkerProfileFormValues } from './schema';

interface WorkerProfileFormProps {
  formType?: 'create' | 'edit';
  prefectureOptions?: ISelectableOption[];
  jobTypeOptions?: ISelectableOption[];
}

const constructionCertificationValues: IConstructionCertification[] = [
  '一般建設業許可',
  '特定建設業許可',
];

const insurancesValues: IInsurance[] = [
  '一人親方労災保険',
  '社会保険',
  '第三者賠償責任保険',
];

const WorkerProfileForm = ({
  prefectureOptions = [],
  jobTypeOptions = [],
}: WorkerProfileFormProps) => {
  const { control, watch, trigger, setValue, clearErrors } =
    useFormContext<WorkerProfileFormValues>();

  const isCompanyWorker = watch('type') === WorkerType.COMPANY_WORKER;
  const hasNationalCertifications =
    watch('hasNationalCertifications') === 'true';
  const nationalCertifications = watch('nationalCertifications') as string[];
  const jobTypes = watch('jobTypes') as string[];
  const hasConstructionCertifications =
    watch('hasConstructionCertifications') === 'true';
  const constructionCertifications = watch('constructionCertifications');
  const hasInsurances = watch('hasInsurances') === 'true';
  const insurances = watch('insurances');

  useEffect(() => {
    if (!hasNationalCertifications) {
      setValue('nationalCertifications', undefined);
      clearErrors('nationalCertifications');
    }
    if (nationalCertifications && !nationalCertifications?.length) {
      trigger('nationalCertifications');
    }
  }, [
    clearErrors,
    hasNationalCertifications,
    nationalCertifications,
    setValue,
    trigger,
  ]);

  useEffect(() => {
    if (jobTypes && !jobTypes?.length) {
      trigger('jobTypes');
    }
  }, [jobTypes, trigger]);

  useEffect(() => {
    if (!hasConstructionCertifications) {
      setValue('constructionCertifications', undefined);
    }
    if (constructionCertifications && !constructionCertifications?.length) {
      trigger('constructionCertifications');
    }
  }, [
    constructionCertifications,
    hasConstructionCertifications,
    setValue,
    trigger,
  ]);

  useEffect(() => {
    if (!hasInsurances) {
      setValue('insurances', undefined);
    }
    if (insurances && !insurances?.length) {
      trigger('insurances');
    }
  }, [hasInsurances, insurances, setValue, trigger]);

  return (
    <Box component="form" w={'100%'}>
      <Flex direction={'column'} gap={24}>
        <RadioGroup
          control={control}
          data={[
            {
              value: WorkerType.COMPANY_WORKER,
              label: '法人',
            },
            {
              value: WorkerType.FREELANCER_WORKER,
              label: '個人（一人親方含む）',
            },
          ]}
          label="雇用形態"
          name="type"
          required
        />
        <Flex
          direction={'column'}
          display={isCompanyWorker ? 'flex' : 'none !important'}
          gap={24}
          hidden={!isCompanyWorker}
        >
          <TextField
            control={control}
            label="会社名"
            name="companyName"
            placeholder="会社名を入力してください"
            required
          />
          <TextField
            control={control}
            label="電話番号"
            name="companyPhone"
            placeholder="電話番号（ハイフンなし）"
            required
          />
        </Flex>
        <Flex
          direction={'column'}
          display={!isCompanyWorker ? 'flex' : 'none !important'}
          gap={24}
          hidden={isCompanyWorker}
        >
          <TextField
            control={control}
            label="屋号"
            name="tradeName"
            placeholder="会社名を入力してください"
            required
          />
        </Flex>
        <Flex
          direction={{ base: 'column', sm: 'row' }}
          gap={16}
          sx={{ '& > *': { flex: 1 } }}
        >
          <TextField
            control={control}
            format="###-####"
            icon="〒"
            label="郵便番号"
            name="postCode"
            placeholder="郵便番号を入力してください"
            required
          />
          <SelectField
            control={control}
            data={prefectureOptions}
            label="都道府県"
            name="prefecture"
            placeholder="都道府県"
            required
          />
        </Flex>
        <Flex
          direction={{ base: 'column', sm: 'row' }}
          gap={16}
          sx={{ '& > *': { flex: 1 } }}
        >
          <TextField
            control={control}
            label="市区町村"
            name="city"
            placeholder="市区町村を入力してください"
            required
          />
          <TextField
            control={control}
            label="町名・番地"
            name="district"
            placeholder="町名・番地を入力してください"
            required
          />
        </Flex>
        <TextField
          control={control}
          label="建物名・部屋番号"
          name="building"
          placeholder="建物名・部屋番号を入力してください"
        />
        <Divider mt={8} />
      </Flex>
      <Flex direction={'column'} gap={24} mt={32}>
        <DateInputField
          control={control}
          label="生年月日"
          maxDate={dayjs(new Date()).subtract(1, 'day').toDate()}
          minDate={dayjs(new Date('1890-01-01')).toDate()}
          name="birthDate"
          placeholder="自分の生年月日(8桁)を入力か選択してください"
          required
          valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
        />
        <MultiSelectField
          control={control}
          data={jobTypeOptions}
          label="職種"
          name="jobTypes"
          placeholder="職種を選択してください"
          required
        />
        <RadioGroup
          control={control}
          data={[
            { label: '登録されていない', value: 'false' },
            {
              label: '登録されている',
              value: 'true',
            },
          ]}
          direction="vertical"
          label="建築システムの会員状況"
          name="hasNationalCertifications"
        />
        <MultiSelectField
          control={control}
          data={[
            {
              value: NationalCertificate.GREEN_SITE,
              label: 'グリーンサイト',
            },
            {
              value: NationalCertificate.CAREER_UP_SYSTEM,
              label: '建築キャリアアップシステム',
            },
          ]}
          disabled={!hasNationalCertifications}
          name="nationalCertifications"
          placeholder="建築システムの会員状況を選択してください"
          sx={{
            display: hasNationalCertifications ? 'block' : 'none',
            '.mantine-MultiSelect-values': {
              width: '100%',
            },
          }}
        />
        <Flex
          direction={'column'}
          display={isCompanyWorker ? 'flex' : 'none !important'}
          gap={24}
          hidden={!isCompanyWorker}
        >
          <RadioGroup
            control={control}
            data={[
              { label: '持っていない', value: 'false' },
              {
                label: '持っている',
                value: 'true',
              },
            ]}
            direction="vertical"
            label="建設業許可証の有無"
            name="hasConstructionCertifications"
          />
          <MultiSelectField
            control={control}
            data={constructionCertificationValues}
            disabled={!hasConstructionCertifications}
            name="constructionCertifications"
            placeholder="建設業許可証の有無"
            sx={{ display: hasConstructionCertifications ? 'block' : 'none' }}
          />
        </Flex>
        <RadioGroup
          control={control}
          data={[
            { label: '持っていない', value: 'false' },
            {
              label: '持っている',
              value: 'true',
            },
          ]}
          direction="vertical"
          label="社会保険の有無"
          name="hasInsurances"
        />
        <MultiSelectField
          control={control}
          data={insurancesValues}
          disabled={!hasInsurances}
          name="insurances"
          placeholder="社会保険の有無"
          sx={{ display: hasInsurances ? 'block' : 'none' }}
        />
        <TextareaField
          control={control}
          label="自己紹介"
          maxRows={4}
          name="selfIntroduction"
          placeholder="自己紹介"
          rows={4}
        />
      </Flex>
    </Box>
  );
};

export default WorkerProfileForm;
