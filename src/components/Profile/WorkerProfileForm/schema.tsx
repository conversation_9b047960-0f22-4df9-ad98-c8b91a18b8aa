import type { IConstructionCertification, IInsurance } from 'models/auth/type';
import { NationalCertificate, REG_EXP, WorkerType } from 'utils/constants';
import type { InferType } from 'yup';
import { array, date, mixed, object, string } from 'yup';

const schema = object({
  type: string().trim().oneOf(Object.values(WorkerType)).defined().required(),
  companyName: string()
    .trim()
    .when('type', {
      is: WorkerType.COMPANY_WORKER,
      then: (s) => s.required().max(50),
    }),
  companyPhone: string()
    .trim()
    .when('type', {
      is: WorkerType.COMPANY_WORKER,
      then: (s) => s.required().matches(REG_EXP.PHONE),
    }),
  tradeName: string().when('type', {
    is: WorkerType.FREELANCER_WORKER,
    then: (s) => s.required().max(50),
  }),
  postCode: string().trim().required().matches(REG_EXP.ZIP_CODE),
  prefecture: string().trim().required(),
  city: string().trim().required().max(30),
  district: string().trim().required().max(30),
  building: string().trim().max(30),
  birthDate: date().required(),
  jobTypes: array()
    .of(string().trim())
    .min(1, 'この項目は入力必須です')
    .required(),
  hasNationalCertifications: string()
    .oneOf(['true', 'false'])
    .defined()
    .default('false')
    .required(),
  nationalCertifications: array()
    .of(string().trim().oneOf(Object.values(NationalCertificate)).defined())
    .when('hasNationalCertifications', {
      is: 'true',
      then: (s) => s.min(1, '一個以上選択してください。').required(),
    }),
  hasConstructionCertifications: string()
    .oneOf(['true', 'false'])
    .defined()
    .default('false')
    .required(),
  constructionCertifications: array()
    .of(mixed<IConstructionCertification>().defined())
    .when('hasConstructionCertifications', {
      is: 'true',
      then: (s) => s.min(1, '一個以上選択してください。').required(),
    }),
  hasInsurances: string()
    .oneOf(['true', 'false'])
    .defined()
    .default('false')
    .required(),
  insurances: array()
    .of(mixed<IInsurance>().defined())
    .when('hasInsurances', {
      is: 'true',
      then: (s) => s.min(1, '一個以上選択してください。').required(),
    }),
  selfIntroduction: string().trim().max(500),
});

export default schema;
export type WorkerProfileFormValues = InferType<typeof schema>;
