import IconToastError from '@icons/toast/icon-error.svg';
import IconToastStatus from '@icons/toast/icon-status.svg';
import IconToastSuccess from '@icons/toast/icon-success.svg';
import IconToastWarning from '@icons/toast/icon-warning.svg';
import { modals } from '@mantine/modals';
import type { OpenConfirmModal } from '@mantine/modals/lib/context';
import type { NotificationProps } from '@mantine/notifications';
import { notifications } from '@mantine/notifications';
import { deleteCookie, getCookie, setCookie } from 'cookies-next';
import type { OptionsType } from 'cookies-next/lib/types';
import { NoNationalCertification, NoRequired } from 'models/auth/type';
import type { NextApiRequest, NextApiResponse } from 'next';
import ReactGA from 'react-ga4';

import {
  COOKIE_TOKEN_KEY,
  COOKIE_USER_DATA_KEY,
  OCR_FILE_UPLOAD_TYPES,
  OCR_MAX_FILE_SIZE_MB,
  OCR_MAX_IMAGE_DIMENSION,
} from './constants';
import type { TokenParse, WebCookie } from './type';

export const nf = new Intl.NumberFormat('ja-JP');

export const toastOptions = {
  success: {
    color: 'success-500',
    bg: 'success-100',
    icon: <IconToastSuccess />,
  },
  error: { color: 'error-500', bg: 'error-100', icon: <IconToastError /> },
  status: {
    color: 'info-500',
    bg: 'info-100',
    icon: <IconToastStatus />,
  },
  warning: {
    color: 'warning-500',
    bg: 'warning-100',
    icon: <IconToastWarning />,
  },
};

const helpers = {
  getWebCookie: (req?: NextApiRequest, res?: NextApiResponse) => {
    try {
      const cookies = JSON.parse(
        (getCookie(COOKIE_TOKEN_KEY, req && res ? { req, res } : {}) ||
          '{}') as string,
      ) as WebCookie;
      return cookies;
    } catch {
      return undefined;
    }
  },
  setToken: (data: WebCookie, options?: OptionsType): void => {
    setCookie(COOKIE_TOKEN_KEY, data, {
      ...options,
      path: '/',
      maxAge: 7776000000, // 90 * 24 * 60 * 60 (90 days to miliseconds)
    });
  },
  removeWebCookie: (): void => {
    deleteCookie(COOKIE_TOKEN_KEY, { path: '/' });
    deleteCookie(COOKIE_USER_DATA_KEY, { path: '/' });
  },
  getTokenConfig: (req: unknown, res: unknown) => {
    const webCookie = helpers.getWebCookie(
      req as NextApiRequest,
      res as NextApiResponse,
    );
    if (webCookie?.accessToken) {
      return {
        headers: {
          Authorization: `Bearer ${webCookie?.accessToken}`,
        },
      };
    }
    return {};
  },
  currencyFormat: (number?: bigint | number) => {
    if (!number) return 0;
    return nf.format(number);
  },
  parseJwt: (token?: string): TokenParse | undefined => {
    if (token) {
      return JSON.parse(
        Buffer.from(token.split('.')[1] || '', 'base64').toString(),
      );
    }
    return undefined;
  },
  toast: ({
    type = 'success',
    ...props
  }: {
    type?: 'success' | 'error' | 'status' | 'warning';
  } & NotificationProps) => {
    if (typeof window === 'undefined') return;
    notifications.show({ ...toastOptions[type], ...props });
  },
  confirm: (props: OpenConfirmModal) => {
    modals.openConfirmModal({
      centered: true,
      labels: { confirm: 'はい', cancel: 'いいえ' },
      confirmProps: { miw: 100 },
      cancelProps: { miw: 100, variant: 'outline', color: 'primary' },
      trapFocus: false,
      size: 560,
      autoFocus: false,
      groupProps: {
        px: { base: 16, sm: 24 },
        py: 16,
        mt: -16,
        pos: 'sticky',
        bottom: 0,
        bg: 'white',
        sx: (theme) => ({
          borderTop: `1px solid ${theme.colors['gray-300']}`,
          [theme.fn.smallerThan('sm')]: {
            gap: 8,
            '& > *': {
              flex: 1,
            },
          },
        }),
      },
      ...props,
    });
  },
  handleDownloadFile: (path: string, filename: string) => {
    fetch(path)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        if (link.parentNode) {
          link.parentNode.removeChild(link);
        }
      });
  },
  downloadWorkerContractsPdf: async (matchingId: string): Promise<void> => {
    try {
      const webCookie = helpers.getWebCookie();
      const apiBaseUrl = process.env.API_SERVER_BASE_URL || '';

      if (!webCookie?.accessToken) {
        throw new Error('認証が必要です。');
      }

      const response = await fetch(
        `${apiBaseUrl}/workers/matchings/${matchingId}/worker-contracts/download-pdf`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${webCookie.accessToken}`,
            'Accept-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get the response as a blob
      const blob = await response.blob();

      // Create and trigger download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'contractedWorkerlist.pdf';
      document.body.appendChild(link);
      link.click();

      // Cleanup
      if (link.parentNode) {
        link.parentNode.removeChild(link);
      }
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      helpers.toast({
        message: 'ダウンロードに失敗しました。',
        type: 'error',
      });
      throw error; // Re-throw to allow component to handle loading states
    }
  },
  formatPostCode: (value?: string) => {
    if (!value) return '';
    return `〒${value.slice(0, 3)}-${value.slice(3)}`;
  },
  renderAddress: (
    postCode?: string,
    building?: string,
    district?: string,
    city?: string,
    prefecture?: string,
  ) => {
    const value =
      `${!!postCode && helpers.formatPostCode(postCode)} ${building || ''} ${
        district || ''
      } ${city || ''} ${prefecture || ''}`.trim() || '---';
    return value;
  },
  logEventTracking: (eventName: string, params?: Record<string, any>) => {
    ReactGA.event(eventName, params);
  },
  setEventTrackingUserId: (userId?: string) => {
    ReactGA.set({ userId });
  },
  checkCertificationRequired: (value: unknown) =>
    JSON.stringify(value) !== JSON.stringify([NoRequired]) &&
    JSON.stringify(value) !== JSON.stringify([NoNationalCertification]),
  renderSalary: (min: number, max?: number) => {
    return `${helpers.currencyFormat(min)}円${
      max && max > min ? ` - ${helpers.currencyFormat(max)}円` : ''
    }`;
  },

  // OCR file validation functions
  validateOCRFile: async (
    file: File,
  ): Promise<{ isValid: boolean; errorMessage?: string }> => {
    const OCR_IMAGE_TYPES = OCR_FILE_UPLOAD_TYPES.split(',').filter(
      (t) => t !== '.pdf', // image only: .jpg, .jpeg, .png, .tiff, .tif
    );
    const OCR_PDF_TYPES = ['.pdf']; // pdf only: .pdf

    // Check file size first (applies to all files)
    const fileSizeMB = file.size / 1024 / 1024;
    if (fileSizeMB > OCR_MAX_FILE_SIZE_MB) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const isPDF =
        fileExtension && OCR_PDF_TYPES.includes(`.${fileExtension}`);

      return {
        isValid: false,
        errorMessage: isPDF
          ? `PDFファイルサイズは${OCR_MAX_FILE_SIZE_MB}MB以内にしてください`
          : `画像ファイルサイズは${OCR_MAX_FILE_SIZE_MB}MB以内にしてください`,
      };
    }

    // Check image dimensions for image files
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const isImage =
      fileExtension && OCR_IMAGE_TYPES.includes(`.${fileExtension}`);

    if (isImage) {
      try {
        const dimensions = await helpers.getImageDimensions(file);
        if (
          dimensions.width > OCR_MAX_IMAGE_DIMENSION ||
          dimensions.height > OCR_MAX_IMAGE_DIMENSION
        ) {
          return {
            isValid: false,
            errorMessage: `画像サイズは${OCR_MAX_IMAGE_DIMENSION}x${OCR_MAX_IMAGE_DIMENSION}ピクセル以内、ファイルサイズは${OCR_MAX_FILE_SIZE_MB}MB以内にしてください`,
          };
        }
      } catch (error) {
        return {
          isValid: false,
          errorMessage: '画像ファイルの読み込みに失敗しました',
        };
      }
    }

    return { isValid: true };
  },

  getImageDimensions: (
    file: File,
  ): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({ width: img.width, height: img.height });
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };

      img.src = url;
    });
  },
};
export const formatGenderJpText = (
  gender: 'MALE' | 'FEMALE' | undefined,
): string => {
  switch (gender) {
    case 'MALE':
      return '男性';
    case 'FEMALE':
      return '女性';
    default:
      return '-';
  }
};

export const sleep = (ms: number) =>
  new Promise((resolve) => {
    setTimeout(resolve, ms);
  });

export default helpers;
