import type { IWorkerContract } from 'models/job/type';

export interface InstructionBannerData {
  message: string;
  show: boolean;
}

/**
 * Calculates the appropriate instruction banner message and visibility
 * based on the contract workers' completion status
 */
export const getInstructionBanner = (
  workerContracts: IWorkerContract[] = [],
): InstructionBannerData => {
  if (!workerContracts.length) {
    return { message: '', show: false };
  }

  // Count workers with incomplete status
  const incompleteDetailCount = workerContracts.filter(
    (worker) => !worker.isDoneDetail,
  ).length;
  const incompleteDocumentsCount = workerContracts.filter(
    (worker) => !worker.isDoneDocuments,
  ).length;

  // Priority 1: Information review (isDoneDetail) - higher priority
  if (incompleteDetailCount > 0) {
    if (incompleteDetailCount === workerContracts.length) {
      // Case #1: All workers need information review
      return {
        message: `＊「確認」ボタンをクリックし、各作業員の情報をご確認ください。
＊すべての作業員の情報を確認いただくまで、応募手続きは完了しません。
 必ず内容をご確認のうえ、手続きを進めてください。`,
        show: true,
      };
    }
    // Case #2: Partial information review completed
    return {
      message: `＊現在、${incompleteDetailCount}名の作業員の情報が未確認です。
＊「確認」ボタンをクリックし、各作業員の情報をご確認ください。
＊すべての作業員の情報を確認いただくまで、応募手続きは完了しません。
 必ず内容をご確認のうえ、手続きを進めてください。`,
      show: true,
    };
  }

  // Priority 2: Document submission (isDoneDocuments) - lower priority
  if (incompleteDocumentsCount > 0) {
    if (incompleteDocumentsCount === workerContracts.length) {
      // Case #3: All workers need document submission
      return {
        message: `＊最終応募に進まれる方は、すべての書類を必ずご提出（アップロード）ください。
＊すべての作業員の書類が提出されるまで、応募手続きは完了しません。`,
        show: true,
      };
    }
    // Case #4: Partial document submission completed
    return {
      message: `＊現在、${incompleteDocumentsCount}名の作業員の書類が未提出です。
＊最終応募に進まれる方は、すべての書類を必ずご提出（アップロード）ください。
＊すべての作業員の書類が提出されるまで、応募手続きは完了しません。`,
      show: true,
    };
  }

  // No incomplete workers - hide banner
  return { message: '', show: false };
};
