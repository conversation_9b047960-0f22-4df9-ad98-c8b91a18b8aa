import type { IWorkerContract } from 'models/job/type';

export interface InstructionBannerData {
  message: string;
  show: boolean;
}

/**
 * Calculates the appropriate instruction banner message and visibility
 * based on the contract workers' completion status and job status
 */
export const getInstructionBanner = (
  workerContracts: IWorkerContract[] = [],
  jobStatus?: string,
): InstructionBannerData => {
  if (!workerContracts.length) {
    return { message: '', show: false };
  }

  // Handle APPLYING status
  if (jobStatus === 'APPLYING') {
    // Count workers with documents uploaded
    const workersWithDocuments = workerContracts.filter(
      (worker) => worker.isDoneDocuments,
    ).length;

    if (workersWithDocuments > 0) {
      // Case 1 - Some Documents Uploaded: Partial documents uploaded When isDoneDocuments = True ≧ 1  AND isDoneDocuments = False  ≧ 1
      const workersWithoutDocuments =
        workerContracts.length - workersWithDocuments;
      return {
        message: `＊現在、○名の作業員の書類が未提出です。
＊最終応募に進まれる方は、すべての書類を必ずご提出（アップロード）ください。
＊すべての作業員の書類が提出されるまで、応募手続きは完了しません。`,
        show: workersWithoutDocuments > 0,
      };
    }
    // Case 2 - No Documents Uploaded: All workers need documents When ALL isDoneDocuments = False
    return {
      message: `＊最終応募に進まれる方は、すべての書類を必ずご提出（アップロード）ください。
＊すべての作業員の書類が提出されるまで、応募手続きは完了しません。`,
      show: true,
    };
  }

  // Handle COLLECTING status - only 3 cases based on isDoneDetail
  if (jobStatus === 'COLLECTING') {
    // Count workers with incomplete detail review
    const incompleteDetailCount = workerContracts.filter(
      (worker) => !worker.isDoneDetail,
    ).length;

    if (incompleteDetailCount === workerContracts.length) {
      // Case 1: All workers need information review When ALL contractWorkers isDoneDetail = False
      return {
        message: `＊「確認」ボタンをクリックし、各作業員の情報をご確認ください。
＊すべての作業員の情報を確認いただくまで、応募手続きは完了しません。
 必ず内容をご確認のうえ、手続きを進めてください。`,
        show: true,
      };
    }

    if (incompleteDetailCount > 0) {
      // Case 2: Mixed review status (some completed, some not): When count of contractWorkers isDoneDetail = True ≧ 1  AND isDoneDetail = False  ≧ 1
      return {
        message: `＊現在、${incompleteDetailCount}名の作業員の情報が未確認です。
＊「確認」ボタンをクリックし、各作業員の情報をご確認ください。
＊すべての作業員の情報を確認いただくまで、応募手続きは完了しません。
 必ず内容をご確認のうえ、手続きを進めてください。`,
        show: true,
      };
    }

    // Case 3: All workers have completed review :When ALL contractWorkers isDoneDetail = TRUE
    return {
      message: `作業員名簿の確認がすべて完了しましたら、画面下部の「確認しました」ボタンを押してください。`,
      show: true,
    };
  }

  // Default case - hide banner
  return { message: '', show: false };
};
