import type { Mutation, Query } from '@tanstack/react-query';
import { MutationCache, QueryCache, QueryClient } from '@tanstack/react-query';

import api from './api';
import helpers from './helpers';
import type {
  FetchDetailOptions,
  FetchListOptions,
  IError,
  MetaProps,
} from './type';

const handleError = async (
  error: IError,
  variables: Query,
  _context?: unknown,
  mutation?: Mutation<unknown, unknown, unknown, unknown>,
) => {
  let errorMessage = error.message;
  const errorCode = error.statusCode;
  const errorError = error.error;

  if (errorMessage === 'Network Error' || errorMessage.includes('offline')) {
    errorMessage = 'インターネット接続がありません。';
  }
  if (mutation && mutation.meta) {
    const { notToastErrorCodes, noToastError }: MetaProps = mutation.meta;
    if (notToastErrorCodes && notToastErrorCodes.includes(errorError)) return;
    if (noToastError) return;
  }
  if (variables && variables.meta) {
    const { notToastErrorCodes, noToastError }: MetaProps = variables.meta;
    if (notToastErrorCodes && notToastErrorCodes.includes(errorCode)) return;
    if (noToastError) return;
  }
  if (errorCode === 401) {
    return;
  }
  if (errorCode === 406 || errorError === 'AUTH_INVALID_REFRESHTOKEN') {
    const queryClient = new QueryClient();
    helpers.removeWebCookie();
    queryClient
      .getQueryCache()
      .findAll(['currentUser'])
      .forEach((query) => query.reset());
    return;
  }
  if (errorMessage) {
    helpers.toast({ type: 'error', message: errorMessage });
  }
};

const queryClient: QueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      structuralSharing: true,
      refetchOnWindowFocus: false,
      retry: false,
      suspense: false,
      networkMode: 'offlineFirst',
    },
    mutations: {
      networkMode: 'offlineFirst',
    },
  },
  mutationCache: new MutationCache({
    onError: (error, variables, context, mutation) =>
      handleError(error as IError, variables as Query, context, mutation),
  }),
  queryCache: new QueryCache({
    onError: (error, variables) => handleError(error as IError, variables),
  }),
});

export const fetchList = ({
  queryKey,
  apiUrl,
  customParams,
  axiosConfig,
  omitKeys,
  ...options
}: FetchListOptions) => {
  const queryParams = { ...(customParams || {}) };
  if (!queryParams.limit) {
    queryParams.limit = 10;
  }
  if (!queryParams.page) {
    queryParams.page = 1;
  }
  if (omitKeys) {
    omitKeys.forEach((key) => {
      delete queryParams[key];
    });
  }
  return queryClient.fetchQuery(
    [...queryKey, queryParams],
    async () => {
      const { data } = await api.get(apiUrl, {
        params: queryParams,
        ...axiosConfig,
      });
      return data;
    },
    options,
  );
};

export const fetchDetail = ({
  queryKey,
  apiUrl,
  customParams,
  axiosConfig,
  ...options
}: FetchDetailOptions) => {
  return queryClient.fetchQuery(
    queryKey,
    async () => {
      const { data } = await api.get(apiUrl, {
        params: customParams,
        ...axiosConfig,
      });
      return data;
    },
    options,
  );
};

export default queryClient;
