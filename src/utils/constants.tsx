import Image from 'next/image';

export const REG_EXP = {
  PASSWORD: /^(?=.*[A-Za-z])(?=.*\d)(?!.*\s)[A-Za-z\d\W]{8,}$/,
  PHONE: /^[0-9]{10,11}$/,
  EMAIL:
    /^[a-z0-9*+_-]+(?:\.[a-z0-9*+_-]+)*@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(((?![-])[a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i,
  ALPHABET_JP_REGX:
    /^[A-Za-z\x20\u3000\u3040-\u309f\u30a0-\u30ff\uff65-\uff9f\u4e00-\u9faf\u3400-\u4dbf\u3005\ufa11]+$/,
  WHITESPACE: /\s/,
  NON_WHITESPACE: /^\S*$/,
  KATAKANA: /^[\x20\u3000\u30a0-\u30ff\uff65-\uff9f]+$/,
  ZIP_CODE: /[0-9]{7}/,
};

export enum DateFormat {
  YEAR = 'YYYY',
  MONTH_YEAR_SHORT = 'MM/YY',
  YEAR_MONTH_DATE = 'YYYY/MM/DD',
  YEAR_MONTH = 'YYYY/MM',
  YEAR_MONTH_DASH = 'YYYY-MM',
  YEAR_MONTH_DATE_HOUR = 'YYYY/MM/DD HH:mm',
  YEAR_MONTH_DATE_HOUR_DASH = 'YYYY-MM-DD HH:mm',
  HOUR_YEAR_MONTH_DATE = 'HH:mm YYYY-MM-DD',
  HOUR_YEAR_MONTH_DATE_JP = 'HH:mm YYYY年MM月DD日',
  YEAR_MONTH_DATE_HOUR_JP = 'YYYY年MM月DD日 HH:mm',
  YEAR_MONTH_DATE_JP = 'YYYY年MM月DD日',
  YEAR_MONTH_DATE_HOUR_MS = 'YYYY/MM/DD HH:mm:ss',
  MONTH_DATE_HOUR_JP = 'YYYY年MM月DD日 HH時mm分',
  ISO = 'YYYY-MM-DDTHH:mm:ss.sss[Z]',
}
export const PHONE_FORMAT = '###-####-####';

export const FILE_UPLOAD_TYPES = '.jpg,.jpeg,.png,.pdf,.doc,.docx,.xlsx,.xls';
export const FILE_UPLOAD_IMAGE_TYPES = '.jpg,.jpeg,.png';
export const OCR_FILE_UPLOAD_TYPES = '.jpg,.jpeg,.png,.pdf,.tiff,.tif';

// OCR validation constants
export const OCR_MAX_IMAGE_DIMENSION = 5000; // 5000x5000 pixels max for images
export const OCR_MAX_FILE_SIZE_MB = 5; // 5MB max file size

export const CONTENT_TYPES_MAP = {
  '.jpg': 'image/jpg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.pdf': 'application/pdf',
  '.tiff': 'image/tiff',
  '.tif': 'image/tiff',
  '.doc': 'application/msword',
  '.docx':
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  '.xls': 'application/vnd.ms-excel',
};

export enum WorkerType {
  COMPANY_WORKER = 'COMPANY_WORKER',
  FREELANCER_WORKER = 'FREELANCER_WORKER',
}

export enum NationalCertificate {
  GREEN_SITE = 'GREEN_SITE',
  CAREER_UP_SYSTEM = 'CAREER_UP_SYSTEM',
}

export const COOKIE_TOKEN_KEY = '__user-token';
export const COOKIE_USER_DATA_KEY = '__user-data';

export const COMPANY_PROPS = ['companyName', 'companyPhone'];

export enum Roles {
  WORKER = 'WORKER',
  ADMIN = 'ADMIN',
  RECRUITER = 'RECRUITER',
  RECRUITER_MANAGER = 'RECRUITER_MANAGER',
}

export enum UserStatus {
  REGISTERING = 'REGISTERING',
  REGISTERED = 'REGISTERED',
  DEACTIVATED = 'DEACTIVATED',
}
export enum WorkerTypeLabel {
  COMPANY_WORKER = '法人',
  FREELANCER_WORKER = '個人（一人親方含む）',
}
export enum NationalCertificationsLabel {
  GREEN_SITE = 'グリーンサイト',
  CAREER_UP_SYSTEM = '建築キャリアアップシステム',
  NOT_REGISTERED = '登録されていない',
}

export enum StatusBadgeColor {
  WARNING,
  SUCCESS,
  ERROR,
  INFO,
  DISABLED,
  COLLECTING,
}

export const PUBLIC_NAVIGATION_MENU = [
  {
    href: '/policy',
    label: 'プライバシーポリシー',
  },
  {
    href: '/terms',
    label: '利用規約',
  },
  {
    href: '/company',
    label: '運営会社',
  },
];

export const SOCIAL_CONTACTS = [
  {
    href: 'https://twitter.com/teqnowa',
    icon: (
      <Image
        alt="twitter-logo"
        height={32}
        src={'/icons/twitter-logo.svg'}
        width={32}
      />
    ),
  },
  {
    href: 'https://www.instagram.com/teqnowa/',
    icon: (
      <Image
        alt="instagram-logo"
        height={32}
        src={'/icons/instagram-logo.svg'}
        width={32}
      />
    ),
  },
  {
    href: 'https://liff.line.me/**********-kWRPP32q/?accountId=803ucajy',
    icon: (
      <Image
        alt="line-logo"
        height={32}
        src={'/icons/line-logo.svg'}
        width={32}
      />
    ),
  },
  {
    href: 'https://www.tiktok.com/@toshishi0203',
    icon: (
      <Image
        alt="tiktok-logo"
        height={32}
        src={'/icons/tiktok-logo.svg'}
        width={32}
      />
    ),
  },
];

export const ERROR_MESSAGES = {
  JOB_STATUS_CHANGED:
    '案件が非公開に設定されたため応募ができません。詳しくは企業に連絡してください。',
};

export const CONTACT_OPTIONS = [
  { label: '発注者としての登録を希望', value: '発注者としての登録を希望' },
  {
    label: 'サービスに関するお問い合わせ',
    value: 'サービスに関するお問い合わせ',
  },
  { label: 'その他', value: 'その他' },
];

export const AI_MEMBER_ID = 'ai_assistant';
export const AI_INDICATOR_TIMEOUT = 40000;
// Job types that require special medical exam
export const SPECIAL_EXAM_REQUIRED_JOB_TYPES = [
  '塗装工事',
  '溶接工事',
  'Nuclear power plant',
];
export const GENDER_OPTION = [
  { label: '男性', value: 'MALE' },
  { label: '女性', value: 'FEMALE' },
];
export const BLOOD_TYPE_OPTIONS = [
  { label: 'A', value: 'A' },
  { label: 'B', value: 'B' },
  { label: 'AB', value: 'AB' },
  { label: 'O', value: 'O' },
];
export const INSURANCE_TYPE_OPTIONS = [
  { label: '雇用保険', value: '雇用保険' },
  { label: '一人親方労災保険', value: '一人親方労災保険' },
];
