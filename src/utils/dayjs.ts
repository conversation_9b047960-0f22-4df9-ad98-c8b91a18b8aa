import 'dayjs/locale/ja';

import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import isBetween from 'dayjs/plugin/isBetween';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.locale('ja');
dayjs.extend(utc);
dayjs.extend(isBetween);
dayjs.extend(timezone);
dayjs.extend(localizedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(duration);
dayjs.tz.setDefault('Asia/Tokyo');

const timezonedDayjs = (...args: any[]) => {
  return dayjs(...args).tz();
};

export default timezonedDayjs;
