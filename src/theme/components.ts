import type { MantineThemeComponents } from '@mantine/styles/lib/theme/types/MantineTheme';

const components: MantineThemeComponents = {
  Text: {
    defaultProps: {
      size: 14,
      color: 'black',
    },
    styles: {
      root: {
        whiteSpace: 'pre-line',
        wordBreak: 'break-all',
      },
    },
  },
  Input: {
    defaultProps: {
      radius: 'md',
      size: 'md',
      iconWidth: 48,
      rightSectionWidth: 40,
    },
    styles: (theme) => ({
      input: {
        paddingLeft: 16,
        borderColor: theme.colors['gray-original'],
        color: theme.colors.black,
        '&:focus, &:focus-within, &:hover': {
          borderColor: theme.colors['gray-400'],
        },
        '&[data-invalid]': {
          borderColor: theme.colors['error-500'],
          color: theme.black,
          '&::placeholder': {
            color: theme.colors['gray-original'],
          },
        },
        '&:disabled, &[data-disabled]': {
          borderColor: theme.colors['gray-400'],
          backgroundColor: theme.colors['gray-200'],
          color: theme.colors['gray-500'],
        },
        '&::placeholder': {
          color: theme.colors['gray-original'],
        },
      },
    }),
    sizes: {
      lg: () => ({
        input: {
          height: 56,
          minHeight: 56,
          fontSize: 18,
        },
      }),
      md: () => ({
        input: {
          height: 48,
          minHeight: 48,
          fontSize: 16,
        },
      }),
    },
  },
  PasswordInput: {
    defaultProps: {
      radius: 'md',
      size: 'md',
      iconWidth: 48,
    },
    styles: (theme) => ({
      innerInput: {
        paddingLeft: 16,
        height: '100%',
        width: 'calc(100% - 40px)',
        '&[data-with-icon]': {
          paddingLeft: 48,
        },
        '&[data-invalid]': {
          color: theme.black,
          '&::placeholder': {
            color: theme.colors['gray-original'],
          },
        },
      },
      rightSection: {
        width: 40,
        button: {
          '&:after': {
            borderRadius: '10px',
          },
        },
      },
    }),
  },
  TextInput: {
    defaultProps: {
      radius: 'md',
      size: 'md',
      iconWidth: 48,
      rightSectionWidth: 40,
    },
  },
  Select: {
    defaultProps: {
      radius: 'md',
      size: 'md',
      rightSectionWidth: 40,
    },
    styles: (theme) => ({
      rightSection: {
        pointerEvents: 'none',
      },
      itemsWrapper: {
        padding: 8,
      },
      item: {
        padding: '14px 16px',
        fontSize: 14,
        borderRadius: 4,
        '&[data-hovered]': {
          fontWeight: 700,
          backgroundColor: theme.colors['gray-200'],
        },
        '&[data-selected]': {
          fontWeight: 700,
          color: theme.colors.primary,
          backgroundColor: theme.colors['neutral-100'],
        },
      },
    }),
  },
  DatePickerInput: {
    defaultProps: {
      radius: 'md',
      size: 'md',
      rightSectionWidth: 40,
    },
    styles: (theme) => ({
      input: {
        paddingLeft: 16,
        borderColor: theme.colors['gray-original'],
        color: theme.black,
        '&:focus, &:focus-within, &:hover': {
          borderColor: theme.colors['gray-400'],
        },
        '&[data-invalid]': {
          borderColor: theme.colors['error-500'],
          color: theme.black,
          '&::placeholder': {
            color: theme.colors['gray-original'],
          },
        },
        '&:disabled, &[data-disabled]': {
          borderColor: theme.colors['gray-400'],
          backgroundColor: theme.colors['gray-200'],
          color: theme.colors['gray-500'],
        },
        '&::placeholder': {
          color: theme.colors['gray-original'],
        },
        '&[data-with-icon]': {
          paddingLeft: 48,
        },
      },
      rightSection: {
        pointerEvents: 'none',
      },
      itemsWrapper: {
        padding: 8,
      },
      item: {
        padding: '14px 16px',
        fontSize: 14,
        borderRadius: 4,
        '&[data-hovered]': {
          fontWeight: 700,
          backgroundColor: theme.colors['gray-200'],
        },
        '&[data-selected]': {
          fontWeight: 700,
          color: theme.colors.primary,
          backgroundColor: theme.colors['neutral-100'],
        },
      },
      day: {
        color: theme.colors.black,
        '&[data-weekend]': {
          color: theme.colors['neutral-600'],
        },
        '&[data-selected]': {
          color: theme.colors['neutral-100'],
        },
      },
    }),
    sizes: {
      lg: () => ({
        input: {
          height: 56,
          minHeight: 56,
          fontSize: 18,
        },
      }),
      md: () => ({
        input: {
          height: 48,
          minHeight: 48,
          fontSize: 16,
        },
      }),
    },
  },
  DateInput: {
    defaultProps: {
      radius: 'md',
      size: 'md',
      rightSectionWidth: 40,
    },
    styles: (theme) => ({
      input: {
        paddingLeft: 16,
        borderColor: theme.colors['gray-original'],
        color: theme.black,
        '&:focus, &:focus-within, &:hover': {
          borderColor: theme.colors['gray-400'],
        },
        '&[data-invalid]': {
          borderColor: theme.colors['error-500'],
          color: theme.black,
          '&::placeholder': {
            color: theme.colors['gray-original'],
          },
        },
        '&:disabled, &[data-disabled]': {
          borderColor: theme.colors['gray-400'],
          backgroundColor: theme.colors['gray-200'],
          color: theme.colors['gray-500'],
        },
        '&::placeholder': {
          color: theme.colors['gray-original'],
        },
        '&[data-with-icon]': {
          paddingLeft: 48,
        },
      },
      rightSection: {
        pointerEvents: 'none',
      },
      itemsWrapper: {
        padding: 8,
      },
      item: {
        padding: '14px 16px',
        fontSize: 14,
        borderRadius: 4,
        '&[data-hovered]': {
          fontWeight: 700,
          backgroundColor: theme.colors['gray-200'],
        },
        '&[data-selected]': {
          fontWeight: 700,
          color: theme.colors.primary,
          backgroundColor: theme.colors['neutral-100'],
        },
      },
      day: {
        color: theme.colors.black,
        '&[data-weekend]': {
          color: theme.colors['neutral-600'],
        },
        '&[data-selected]': {
          color: theme.colors['neutral-100'],
        },
      },
    }),
    sizes: {
      lg: () => ({
        input: {
          height: 56,
          minHeight: 56,
          fontSize: 18,
        },
      }),
      md: () => ({
        input: {
          height: 48,
          minHeight: 48,
          fontSize: 16,
        },
      }),
    },
  },
  MultiSelect: {
    defaultProps: {
      radius: 'md',
      size: 'md',
      rightSectionWidth: 40,
    },
    styles: (theme) => ({
      rightSection: {
        pointerEvents: 'none',
      },
      input: {
        display: 'flex',
        height: 'unset',
        '&[data-invalid] input::placeholder': {
          color: theme.colors['gray-original'],
        },
      },
      item: {
        padding: '14px 16px',
        fontSize: 14,
        borderRadius: 4,
        '&[data-hovered]': {
          fontWeight: 700,
          backgroundColor: theme.colors['gray-200'],
        },
        '&[data-selected]': {
          fontWeight: 700,
          color: theme.colors.primary,
          backgroundColor: theme.colors['neutral-100'],
        },
      },
      value: {
        backgroundColor: theme.colors['neutral-50'],
        color: theme.colors.primary,
        lineHeight: '20px',
        margin: 4,
        height: 26,
      },
      defaultValueRemove: {
        color: theme.colors['neutral-300'],
      },
      defaultValue: {
        maxWidth: 'unset',
      },
    }),
  },
  Button: {
    defaultProps: {
      variant: 'filled',
      size: 'md',
      loaderProps: {
        width: 18,
        height: 18,
        mr: 4,
      },
    },
    styles: (theme) => ({
      root: {
        height: 'unset',
        transition: 'all 200ms ease-out',
        fontSize: 14,
        fontWeight: 500,
        lineHeight: '20px',
        '&:after': {
          content: '""',
          display: 'block',
          position: 'absolute',
          left: 0,
          top: 0,
          width: '100%',
          height: '100%',
          opacity: '0',
          transition: 'all 0.5s ease-out',
          boxShadow: `0 0 15px 2px ${theme.colors.primary}`,
        },
        '&:active:after': {
          boxShadow: `0 0 0 0 ${theme.colors.primary}`,
          position: 'absolute',
          left: 0,
          top: 0,
          opacity: 1,
          transition: '0s',
        },
      },
      leftIcon: {
        marginRight: 4,
      },
    }),
    variants: {
      filled: (theme) => ({
        root: {
          '&:hover': {
            backgroundColor: theme.colors['neutral-600'],
          },
          '&:disabled': {
            backgroundColor: theme.colors['gray-500'],
            color: theme.white,
          },
        },
      }),
      outline: (theme) => ({
        root: {
          '&:hover': {
            backgroundColor: theme.colors['neutral-50'],
          },
          '&:disabled': {
            color: theme.colors['gray-500'],
            backgroundColor: 'transparent',
            borderColor: theme.colors['gray-500'],
          },
        },
      }),
      subtle: (theme) => ({
        root: {
          '&:hover': {
            backgroundColor: theme.colors['neutral-50'],
          },
          '&:disabled': {
            color: theme.colors['gray-500'],
            backgroundColor: 'transparent',
          },
        },
      }),
      white: (theme) => ({
        root: {
          borderColor: theme.colors.primary,
          '&:hover': {
            backgroundColor: theme.colors.primary,
            color: theme.white,
          },
          '&:disabled': {
            color: theme.colors['gray-500'],
            backgroundColor: 'transparent',
          },
        },
      }),
    },
    sizes: {
      xl: () => ({
        root: {
          minHeight: 64,
          borderRadius: '10px',
          padding: '12px',
          '&:after': {
            borderRadius: '10px',
          },
          '&:active:after': {
            borderRadius: '10px',
          },
        },
      }),
      lg: () => ({
        root: {
          minHeight: 56,
          borderRadius: '10px',
          padding: '12px',
          '&:after': {
            borderRadius: '10px',
          },
          '&:active:after': {
            borderRadius: '10px',
          },
        },
      }),
      md: () => ({
        root: {
          minHeight: 48,
          borderRadius: '10px',
          padding: '12px',
          '&:after': {
            borderRadius: '10px',
          },
          '&:active:after': {
            borderRadius: '10px',
          },
        },
      }),
      sm: () => ({
        root: {
          minHeight: '32px',
          borderRadius: '8px',
          padding: '0 12px',
          '&:after': {
            borderRadius: '8px',
          },
          '&:active:after': {
            borderRadius: '8px',
          },
        },
      }),
    },
  },
  ActionIcon: {
    defaultProps: {
      size: 'md',
    },
    styles: (theme) => ({
      root: {
        '&:after': {
          content: '""',
          display: 'block',
          position: 'absolute',
          left: 0,
          top: 0,
          width: '100%',
          height: '100%',
          opacity: '0',
          transition: 'all 0.5s ease-out',
          boxShadow: `0 0 15px 2px ${theme.colors.primary}`,
        },
        '&:active:after': {
          boxShadow: `0 0 0 0 ${theme.colors.primary}`,
          position: 'absolute',
          left: 0,
          top: 0,
          opacity: 1,
          transition: '0s',
        },
      },
    }),
    variants: {
      outline: (theme) => ({
        root: {
          '&:hover': {
            backgroundColor: theme.colors['neutral-50'],
          },
          '&:disabled': {
            color: theme.colors['gray-500'],
            backgroundColor: 'transparent',
            borderColor: theme.colors['gray-500'],
          },
        },
      }),
      subtle: (theme) => ({
        root: {
          '&:hover': {
            backgroundColor: theme.colors['neutral-50'],
          },
          '&:disabled': {
            color: theme.colors['gray-500'],
            backgroundColor: 'transparent',
          },
        },
      }),
      white: (theme) => ({
        root: {
          border: `1px solid ${theme.colors.primary}`,
          background: theme.white,
          color: theme.colors.primary,
          '&:disabled': {
            color: theme.colors['gray-500'],
            backgroundColor: 'transparent',
          },
        },
      }),
    },
    sizes: {
      md: () => ({
        root: {
          width: 48,
          minWidth: 48,
          height: 48,
          minHeight: 48,
          borderRadius: '10px',
          '&:after': {
            borderRadius: '10px',
          },
          '&:active:after': {
            borderRadius: '10px',
          },
        },
      }),
      sm: () => ({
        root: {
          width: 32,
          minWidth: 32,
          height: 32,
          minHeight: 32,
          borderRadius: '10px',
          '&:after': {
            borderRadius: '8px',
          },
          '&:active:after': {
            borderRadius: '8px',
          },
        },
      }),
      r50: () => ({
        root: {
          width: 48,
          minWidth: 48,
          height: 48,
          minHeight: 48,
          borderRadius: '50%',
          '&:after': {
            borderRadius: '50%',
          },
          '&:active:after': {
            borderRadius: '50%',
          },
        },
      }),
    },
  },
  Container: {
    defaultProps: {
      p: 0,
    },
  },
  Card: {
    defaultProps: {
      shadow: 'sm',
      radius: 'md',
      withBorder: true,
      p: undefined,
    },
    styles: (theme) => ({
      root: {
        '&[data-with-border]': {
          borderColor: theme.colors['gray-300'],
        },
      },
    }),
  },
  Divider: {
    defaultProps: {
      color: 'gray-300',
    },
  },
  Checkbox: {
    defaultProps: {
      radius: 'xs',
      size: 19.5,
    },
    styles: (theme) => ({
      inner: {
        padding: 2.5,
        width: 24,
        height: 24,
      },
      input: {
        border: `2px solid ${theme.colors['gray-original']}`,
        '&:checked': {
          backgroundColor: theme.colors['gray-700'],
          borderColor: theme.colors['gray-700'],
        },
      },
      icon: {
        width: '50%',
      },
      label: {
        paddingLeft: 8,
      },
    }),
  },
  Notification: {
    styles: (theme, params) => {
      return {
        root: {
          padding: '16px 24px',
          boxShadow: theme.shadows.md,
          '&[data-with-icon]': {
            paddingLeft: 24,
          },
        },
        closeButton: {
          color: theme.colors['gray-700'],
          ':hover': {
            backgroundColor: 'unset',
          },
        },
        description: {
          fontSize: 16,
          fontWeight: 400,
          lineHeight: '24px',
          color: theme.colors[params.color],
        },
        icon: {
          background: 'unset',
          width: 24,
          height: 24,
        },
      };
    },
  },
  Modal: {
    defaultProps: {
      shadow: 'sm',
    },
    styles: (theme) => ({
      content: {
        position: 'relative',
      },
      title: {
        color: theme.colors.black,
        fontSize: 18,
        fontWeight: 700,
        lineHeight: '26px',
        padding: '16px 42px 20px 24px',
        width: '100%',
        borderBottom: `1px solid ${theme.colors['gray-300']}`,
      },
      close: {
        top: 15,
        right: 15,
        color: theme.colors['gray-700'],
        position: 'absolute',
        fontSize: 16,
      },
      header: {
        padding: 0,
        borderRadius: 10,
      },
      body: {
        padding: 0,
      },
    }),
  },
  Menu: {
    defaultProps: {
      shadow: 'md',
    },
    styles: (theme) => ({
      dropdown: {
        padding: 8,
      },
      item: {
        padding: '14px 16px',
        color: theme.colors['gray-700'],
        fontWeight: 400,
        lineHeight: '20px',
        '.mantine-Menu-itemIcon': {
          marginRight: 8,
          color: theme.colors['gray-700'],
        },
        '&[data-hovered]': {
          backgroundColor: theme.colors['gray-200'],
        },
      },
    }),
  },
  Radio: {
    styles: (theme) => ({
      label: {
        color: theme.colors.black,
        fontSize: 16,
      },
      labelWrapper: {
        justifyContent: 'center',
      },
      radio: {
        borderColor: theme.colors['gray-700'],
        width: 24,
        height: 24,
        '&:checked': {
          backgroundColor: 'transparent',
          borderColor: theme.colors['gray-700'],
          '&+.___ref-icon': {
            transform: 'scale(1.8)',
            color: theme.colors['gray-700'],
          },
        },
      },
    }),
  },
  Badge: {
    defaultProps: {
      size: 'md',
    },
    styles: (theme) => ({
      root: {
        '&.rounded': {
          borderRadius: theme.radius.md,
        },
        '&.curved': {
          borderRadius: '8px',
        },
        height: '28px',
        padding: '4px 8px',
      },
    }),
    sizes: {
      md: (theme) => ({
        root: {
          borderRadius: theme.radius.xs,
        },
        inner: {
          '> .mantine-Text-root': {
            lineHeight: '20px',
          },
        },
      }),
    },
  },
  ThemeIcon: {
    defaultProps: {
      variant: 'default',
    },
    variants: {
      default: (theme, params) => ({
        root: {
          background: 'transparent',
          border: 0,
          color: theme.colors[params.color] || 'inherit',
        },
      }),
    },
  },
};

export default components;
