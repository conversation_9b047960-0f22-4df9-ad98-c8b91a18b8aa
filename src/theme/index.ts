import type { MantineThemeOverride } from '@mantine/core';

import colors from './colors';
import components from './components';

// *NOTE: Mantine default theme
// https://github.com/mantinedev/mantine/blob/master/src/mantine-styles/src/theme/default-theme.ts
const MantineTheme: MantineThemeOverride = {
  colors,
  other: {}, // Other variable need to store
  primaryColor: 'primary',
  datesLocale: 'ja',
  breakpoints: {
    xs: '36em', // 576px
    sm: '48em', // 768px
    md: '62em', // 992px
    lg: '75em', // 1200px
    xl: '88em', // 1408px
  },
  fontSizes: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '24px',
  },
  radius: {
    xs: '4px',
    sm: '6px',
    md: '10px',
    lg: '16px',
    xl: '32px',
  },
  spacing: {
    tight: '4px',
    loose: '8px',
    xs: '10px',
    sm: '12px',
    md: '16px',
    lg: '20px',
    xl: '24px',
  },
  shadows: {
    sm: '2px 4px 10px 0px  rgba(117, 138, 147, 0.10)',
    md: '2px 8px 14px 0px  rgba(117, 138, 147, 0.14)',
    lg: '4px 12px 20px 0px  rgba(117, 138, 147, 0.24)',
  },
  primaryShade: 0,
  defaultRadius: 'md',
  activeStyles: {
    transform: 'translate(0)',
  },
  // Controls font-family in all components except Title, Code and Kbd
  fontFamily: 'Noto Sans JP, sans-serif',
  // Controls font-family of components that require monospace font: Code, Kbd and Prism
  fontFamilyMonospace:
    'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace',
  headings: {
    // Controls font-family of h1-h6 tags in Title and TypographyStylesProvider components, fallbacks to theme.fontFamily if not defined
    fontFamily: 'Noto Sans JP, sans-serif',
    fontWeight: 700,
    sizes: {
      h1: { fontSize: '40px', lineHeight: '58px', fontWeight: 700 },
      h2: { fontSize: '32px', lineHeight: '52px', fontWeight: 700 },
      h3: { fontSize: '32px', lineHeight: '46px', fontWeight: 700 },
      h4: { fontSize: '28px', lineHeight: '40px', fontWeight: 700 },
      h5: { fontSize: '24px', lineHeight: '34px', fontWeight: 700 },
      h6: { fontSize: '18px', lineHeight: '26px', fontWeight: 700 },
    },
  },
  components,
  focusRingStyles: {
    styles: (theme) => ({
      outlineOffset: '0.125rem',
      outline: `0.125rem solid ${theme.colors.primary}`,
    }),
    resetStyles: () => ({ outline: 'none' }),
    inputStyles: (theme) => ({
      outline: 'none',
      borderColor: theme.colors.primary,
    }),
  },
  globalStyles: () => ({
    // Denfine global styles
    html: {
      fontSize: 16,
      scrollBehavior: 'smooth',
      scrollPaddingTop: '5vh',
    },
    a: {
      textDecoration: 'none',
    },
    '.hover-underline': {
      position: 'relative',
      textDecoration: 'none',
      '&:after': {
        content: '""',
        position: 'absolute',
        width: '100%',
        height: 2,
        bottom: 0,
        left: 0,
        backgroundColor: 'currentColor',
        visibility: 'hidden',
        transform: 'scaleX(0)',
        transition: 'all 0.3s ease-in-out 0s',
      },
      '&:hover:after': {
        visibility: 'visible',
        transform: 'scaleX(1)',
      },
    },
  }),
};

export default MantineTheme;
