import type {
  DefaultMantineColor,
  MantineThemeColors,
  Tuple,
} from '@mantine/core';

// Add more color
const extra = {
  primary: '#D23A19',
  'neutral-50': '#FBF2E8',
  'neutral-100': '#F8E6D2',
  'neutral-200': '##F1C6A7',
  'neutral-300': '#E29C79',
  'neutral-400': '#D17555',
  'neutral-600': '#B23D24',
  'neutral-700': '#89230D',
  'neutral-800': '#6E2515',
  'neutral-900': '#521406',
  'gray-original': '#B8BEC8',
  'gray-100': '#F9FAFB',
  'gray-200': '#F3F4F6',
  'gray-300': '#E5E7EB',
  'gray-400': '#D2D5DA',
  'gray-500': '#9CA3AF',
  'gray-600': '#7B8492',
  'gray-700': '#4B5563',
  'gray-800': '#1F2937',
  'info-50': '#E6F0FB',
  'info-100': '#def6ff',
  'info-300': '#8ad8ff',
  'info-500': '#0162d1',
  'success-50': '#E8F3E9',
  'success-100': '#ddfdcd',
  'success-300': '#b5ed9a',
  'success-500': '#16831c',
  'warning-50': `#FFF5E9`,
  'warning-100': '#fff3d0',
  'warning-300': '#fabf71',
  'warning-500': '#e39937',
  'warning-600': '#F69B21',
  'error-50': '#F8E9E9',
  'error-100': '#fee2e2',
  'error-300': '#f87171',
  'error-500': '#b91c1c',
  'lily-white': '#E9F8FF',
  'dodger-blue': '#1EB7FF',
  'aqua-squeeze': '#ECF7F7',
  keppel: '#38AAAD',
  black: '#111827',
} as const;

type Colors = keyof typeof extra;

declare module '@mantine/core' {
  export interface MantineThemeColorsOverride {
    colors: Record<Colors | DefaultMantineColor, Tuple<string, 10>>;
  }
}

const colors = Object.keys(extra).reduce((acc, curr) => {
  return {
    ...acc,
    [curr]: [extra[curr as Colors]],
  };
}, {});

export default colors as MantineThemeColors;
