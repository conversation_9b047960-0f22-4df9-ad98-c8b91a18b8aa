import { getIronSession } from 'iron-session/edge';
import sessionOptions from 'lib/session';
import { type NextRequest, NextResponse } from 'next/server';

export const middleware = async (req: NextRequest) => {
  const res = NextResponse.next();

  const { user } = await getIronSession(req, res, sessionOptions);
  const nextUrl = req.nextUrl.clone();

  // const referer = req.headers.get('referer') || nextUrl.origin;
  // const { pathname } = new URL(referer);
  // const prevPath = pathname !== nextUrl.pathname ? pathname : '/';

  if (user) {
    if (
      user.isCompletedProfile === false &&
      nextUrl.pathname !== '/register/complete'
    ) {
      nextUrl.pathname = '/register/complete';
      const nextResponse = NextResponse.rewrite(nextUrl);
      nextResponse.headers.set('x-middleware-cache', 'no-cache');
      return nextResponse;
    }
    if (
      ['/login', '/register'].includes(nextUrl.pathname) ||
      (user.isCompletedProfile === true &&
        nextUrl.pathname === '/register/complete')
    ) {
      nextUrl.pathname = '/';
      const nextResponse = NextResponse.rewrite(nextUrl);
      nextResponse.headers.set('x-middleware-cache', 'no-cache');
      return nextResponse;
    }
  }

  if (!user) {
    if (
      nextUrl.pathname.startsWith('/my-page') ||
      ['/messages', '/register/complete'].includes(nextUrl.pathname) ||
      nextUrl.pathname.includes('/apply') ||
      nextUrl.pathname.startsWith('/contact-admin')
    ) {
      nextUrl.searchParams.set('referer', nextUrl.pathname);
      nextUrl.pathname = '/login';
      const nextResponse = NextResponse.rewrite(nextUrl);
      nextResponse.headers.set('x-middleware-cache', 'no-cache');
      return nextResponse;
    }
  }

  return res;
};

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
    '/',
  ],
};
