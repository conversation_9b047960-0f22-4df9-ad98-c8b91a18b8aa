# Form Guidelines

This document outlines the standard form implementation guidelines for our project, based on the worker profile edit form implementation.

## Table of Contents
- [Form Structure](#form-structure)
- [Form Components](#form-components)
- [Validation](#validation)
- [Layout & Responsiveness](#layout--responsiveness)
- [State Management](#state-management)
- [Error Handling](#error-handling)

## Form Structure

### Basic Setup
```tsx
import { FormProvider, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

const MyForm = () => {
  const methods = useForm({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  return (
    <FormProvider {...methods}>
      <Box component="form" w={'100%'}>
        {/* Form fields */}
      </Box>
    </FormProvider>
  );
};
```

### Schema Definition
```tsx
import { object, string, array, date } from 'yup';

const schema = object({
  // Define validation rules
  field: string().required().max(50),
});

export type FormValues = InferType<typeof schema>;
```

## Form Components

### Standard Input Types

1. **Text Fields**
```tsx
<TextField
  control={control}
  label="Label"
  name="fieldName"
  placeholder="Placeholder text"
  required
  maxLength={50}
/>
```

2. **Select Fields**
```tsx
<SelectField
  control={control}
  data={options}
  label="Label"
  name="fieldName"
  placeholder="Select an option"
  required
/>
```

3. **Multi-Select Fields**
```tsx
<MultiSelectField
  control={control}
  data={options}
  label="Label"
  name="fieldName"
  placeholder="Select options"
  required
/>
```

4. **Date Input Fields**
```tsx
<DateInputField
  control={control}
  label="Date"
  name="date"
  placeholder="Select date"
  required
  valueFormat={DateFormat.YEAR_MONTH_DATE_JP}
  minDate={minDate}
  maxDate={maxDate}
/>
```

5. **Radio Groups**
```tsx
<RadioGroup
  control={control}
  data={[
    { value: 'value1', label: 'Label 1' },
    { value: 'value2', label: 'Label 2' }
  ]}
  label="Label"
  name="fieldName"
  required
/>
```

6. **Textarea Fields**
```tsx
<TextareaField
  control={control}
  label="Label"
  name="fieldName"
  placeholder="Enter text"
  maxLength={500}
/>
```

## Validation

### Common Validation Rules

```tsx
// String validations
string().trim().required().max(50)

// Phone number
string().matches(REG_EXP.PHONE)

// Postal code
string().matches(REG_EXP.ZIP_CODE)

// Date
date().required()

// Arrays
array().min(1, 'At least one selection required').required()

// Conditional validation
string().when('fieldName', {
  is: (value) => value === 'specificValue',
  then: (schema) => schema.required()
})
```

## Layout & Responsiveness

### Desktop Layout
```tsx
<Flex
  direction={{ base: 'column', sm: 'row' }}
  gap={16}
  sx={{ '& > *': { flex: 1 } }}
>
  <TextField {...props} />
  <TextField {...props} />
</Flex>
```

### Mobile Layout
```tsx
<Flex
  bg="white"
  gap={8}
  justify="flex-end"
  mt={42}
  px={16}
  py={12}
  sx={(theme) => ({
    boxShadow: theme.shadows.sm,
    position: 'sticky',
    bottom: 0,
    [theme.fn.largerThan('sm')]: {
      display: 'none',
    },
  })}
>
  {/* Mobile specific content */}
</Flex>
```

## State Management

### Form State Tracking
```tsx
const { formState: { isDirty, isValid } } = methods;

// Track unsaved changes
const handleCancel = () => {
  if (methods.formState.isDirty) {
    helpers.confirm({
      title: 'Discard Changes',
      children: <Text>Are you sure you want to discard changes?</Text>,
      onConfirm: () => {
        // Handle navigation
      },
    });
  }
};
```

### Conditional Field Display
```tsx
const isFieldVisible = watch('toggleField');

<Box display={isFieldVisible ? 'block' : 'none'}>
  {/* Conditional fields */}
</Box>
```

## Error Handling

### Form Submission
```tsx
const onSubmit: SubmitHandler<FormValues> = async (values) => {
  try {
    await submitData(values);
    // Handle success
  } catch (error) {
    // Handle error
  }
};
```

### Loading States
```tsx
<Button
  loading={isSubmitting}
  disabled={!isValid || !isDirty || isLoading}
  onClick={handleSubmit(onSubmit)}
>
  Submit
</Button>
```

## Best Practices

1. Always use `FormProvider` for complex forms
2. Implement form validation using Yup schemas
3. Use `onBlur` validation mode for better UX
4. Include proper loading and error states
5. Implement unsaved changes warnings
6. Use responsive layouts
7. Provide clear validation feedback
8. Use consistent spacing and layout patterns 